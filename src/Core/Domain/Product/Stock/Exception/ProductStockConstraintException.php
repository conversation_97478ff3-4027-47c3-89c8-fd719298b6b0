<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

declare(strict_types=1);

namespace PrestaShop\PrestaShop\Core\Domain\Product\Stock\Exception;

/**
 * Thrown when product stock constraints are violated
 */
class ProductStockConstraintException extends ProductStockException
{
    /**
     * Code is sent when invalid out of stock type is used
     */
    public const INVALID_OUT_OF_STOCK_TYPE = 10;

    /**
     * When quantity is invalid
     */
    public const INVALID_QUANTITY = 20;

    /**
     * When location is invalid
     */
    public const INVALID_LOCATION = 30;

    /**
     * When out_of_stock is invalid
     */
    public const INVALID_OUT_OF_STOCK = 40;

    /**
     * When id is invalid
     */
    public const INVALID_ID = 50;

    /**
     * When delta quantity is invalid
     */
    public const INVALID_DELTA_QUANTITY = 60;

    /**
     * When fixed quantity and delta quantity are both provided
     */
    public const FIXED_AND_DELTA_QUANTITY_PROVIDED = 70;
}
