<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace PrestaShop\PrestaShop\Core\Domain\Theme\CommandHandler;

use PrestaShop\PrestaShop\Core\Domain\Theme\Command\AdaptThemeToRTLLanguagesCommand;
use PrestaShop\PrestaShop\Core\Domain\Theme\Exception\CannotAdaptThemeToRTLLanguagesException;
use PrestaShop\PrestaShop\Core\Localization\RTL\Exception\GenerationException;
use PrestaShop\PrestaShop\Core\Localization\RTL\StyleSheetProcessorFactoryInterface;

/**
 * Class AdaptThemeToRTLLanguagesHandler
 */
final class AdaptThemeToRTLLanguagesHandler implements AdaptThemeToRTLLanguagesHandlerInterface
{
    /**
     * @var StyleSheetProcessorFactoryInterface
     */
    private $stylesheetProcessorFactory;

    /**
     * @param StyleSheetProcessorFactoryInterface $stylesheetProcessorFactory
     */
    public function __construct(StyleSheetProcessorFactoryInterface $stylesheetProcessorFactory)
    {
        $this->stylesheetProcessorFactory = $stylesheetProcessorFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function handle(AdaptThemeToRTLLanguagesCommand $command)
    {
        $plainThemeName = $command->getThemeName()->getValue();

        try {
            $this->stylesheetProcessorFactory
                ->create()
                ->setProcessFOThemes([$plainThemeName])
                ->process()
            ;
        } catch (GenerationException $e) {
            throw new CannotAdaptThemeToRTLLanguagesException(sprintf('Cannot adapt "%s" theme to RTL languages.', $plainThemeName), 0, $e);
        }
    }
}
