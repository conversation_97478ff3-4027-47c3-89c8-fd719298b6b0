<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace PrestaShop\PrestaShop\Core\Domain\Order\Command;

use PrestaShop\PrestaShop\Core\Domain\Address\ValueObject\AddressId;
use PrestaShop\PrestaShop\Core\Domain\Order\ValueObject\OrderId;

/**
 * Changes invoice address for given order.
 */
class ChangeOrderInvoiceAddressCommand
{
    /**
     * @var OrderId
     */
    private $orderId;

    /**
     * @var AddressId
     */
    private $newInvoiceAddressId;

    /**
     * @param int $orderId
     * @param int $newInvoiceAddressId
     */
    public function __construct($orderId, $newInvoiceAddressId)
    {
        $this->orderId = new OrderId($orderId);
        $this->newInvoiceAddressId = new AddressId($newInvoiceAddressId);
    }

    /**
     * @return OrderId
     */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * @return AddressId
     */
    public function getNewInvoiceAddressId()
    {
        return $this->newInvoiceAddressId;
    }
}
