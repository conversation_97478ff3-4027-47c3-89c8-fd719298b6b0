<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace PrestaShop\PrestaShop\Core\Grid\Data;

use PrestaShop\PrestaShop\Core\Grid\Record\RecordCollectionInterface;

/**
 * Class GridData is responsible for storing grid data.
 */
final class GridData implements GridDataInterface
{
    /**
     * @var RecordCollectionInterface
     */
    private $records;

    /**
     * @var int
     */
    private $recordsTotal;

    /**
     * @var string
     */
    private $query;

    /**
     * @param RecordCollectionInterface $records Filtered & paginated rows data
     * @param int $recordsTotal Total number of rows (without pagination)
     * @param string $query Query used to get rows
     */
    public function __construct(RecordCollectionInterface $records, $recordsTotal, $query = '')
    {
        $this->records = $records;
        $this->recordsTotal = $recordsTotal;
        $this->query = $query;
    }

    /**
     * {@inheritdoc}
     */
    public function getRecords()
    {
        return $this->records;
    }

    /**
     * {@inheritdoc}
     */
    public function getRecordsTotal()
    {
        return $this->recordsTotal;
    }

    /**
     * {@inheritdoc}
     */
    public function getQuery()
    {
        return $this->query;
    }
}
