<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace PrestaShop\PrestaShop\Core\Grid\Definition\Factory;

use PrestaShop\PrestaShop\Core\Grid\Action\Bulk\BulkActionCollection;
use PrestaShop\PrestaShop\Core\Grid\Action\Bulk\Type\SubmitBulkAction;
use PrestaShop\PrestaShop\Core\Grid\Action\GridActionCollection;
use PrestaShop\PrestaShop\Core\Grid\Action\Row\RowActionCollection;
use PrestaShop\PrestaShop\Core\Grid\Action\Row\Type\LinkRowAction;
use PrestaShop\PrestaShop\Core\Grid\Action\Type\SimpleGridAction;
use PrestaShop\PrestaShop\Core\Grid\Column\ColumnCollection;
use PrestaShop\PrestaShop\Core\Grid\Column\Type\Common\ActionColumn;
use PrestaShop\PrestaShop\Core\Grid\Column\Type\Common\BulkActionColumn;
use PrestaShop\PrestaShop\Core\Grid\Column\Type\Common\DataColumn;
use PrestaShop\PrestaShop\Core\Grid\Column\Type\Common\ImageColumn;
use PrestaShop\PrestaShop\Core\Grid\Column\Type\Common\LinkColumn;
use PrestaShop\PrestaShop\Core\Grid\Column\Type\Common\ToggleColumn;
use PrestaShop\PrestaShop\Core\Grid\Filter\Filter;
use PrestaShop\PrestaShop\Core\Grid\Filter\FilterCollection;
use PrestaShopBundle\Form\Admin\Type\SearchAndResetType;
use PrestaShopBundle\Form\Admin\Type\YesAndNoChoiceType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\Request;

/**
 * Class LanguageGridDefinitionFactory creates definition for languages grid.
 */
final class LanguageGridDefinitionFactory extends AbstractGridDefinitionFactory
{
    use BulkDeleteActionTrait;
    use DeleteActionTrait;

    public const GRID_ID = 'language';

    /**
     * {@inheritdoc}
     */
    protected function getId()
    {
        return self::GRID_ID;
    }

    /**
     * {@inheritdoc}
     */
    protected function getName()
    {
        return $this->trans('Languages', [], 'Admin.Global');
    }

    /**
     * {@inheritdoc}
     */
    protected function getColumns()
    {
        return (new ColumnCollection())
            ->add(
                (new BulkActionColumn('language_bulk'))
                    ->setOptions([
                        'bulk_field' => 'id_lang',
                    ])
            )
            ->add(
                (new DataColumn('id_lang'))
                    ->setName($this->trans('ID', [], 'Admin.Global'))
                    ->setOptions([
                        'field' => 'id_lang',
                    ])
            )
            ->add(
                (new ImageColumn('flag'))
                    ->setName($this->trans('Flag', [], 'Admin.International.Feature'))
                    ->setOptions([
                        'src_field' => 'flag',
                    ])
            )
            ->add(
                (new LinkColumn('name'))
                    ->setName($this->trans('Name', [], 'Admin.Global'))
                    ->setOptions([
                        'field' => 'name',
                        'route' => 'admin_languages_edit',
                        'route_param_name' => 'languageId',
                        'route_param_field' => 'id_lang',
                    ])
            )
            ->add(
                (new DataColumn('iso_code'))
                    ->setName($this->trans('ISO code', [], 'Admin.International.Feature'))
                    ->setOptions([
                        'field' => 'iso_code',
                    ])
            )
            ->add(
                (new DataColumn('language_code'))
                    ->setName($this->trans('Language code', [], 'Admin.International.Feature'))
                    ->setOptions([
                        'field' => 'language_code',
                    ])
            )
            ->add(
                (new DataColumn('date_format_lite'))
                    ->setName($this->trans('Date format', [], 'Admin.International.Feature'))
                    ->setOptions([
                        'field' => 'date_format_lite',
                    ])
            )
            ->add(
                (new DataColumn('date_format_full'))
                    ->setName($this->trans('Date format (full)', [], 'Admin.International.Feature'))
                    ->setOptions([
                        'field' => 'date_format_full',
                    ])
            )
            ->add(
                (new ToggleColumn('active'))
                    ->setName($this->trans('Enabled', [], 'Admin.Global'))
                    ->setOptions([
                        'field' => 'active',
                        'primary_field' => 'id_lang',
                        'route' => 'admin_languages_toggle_status',
                        'route_param_name' => 'languageId',
                    ])
            )
            ->add(
                (new ActionColumn('actions'))
                    ->setName($this->trans('Actions', [], 'Admin.Global'))
                    ->setOptions([
                        'actions' => (new RowActionCollection())
                            ->add(
                                (new LinkRowAction('edit'))
                                    ->setName($this->trans('Edit', [], 'Admin.Actions'))
                                    ->setIcon('edit')
                                    ->setOptions([
                                        'route' => 'admin_languages_edit',
                                        'route_param_name' => 'languageId',
                                        'route_param_field' => 'id_lang',
                                        'clickable_row' => true,
                                    ])
                            )
                            ->add(
                                $this->buildDeleteAction(
                                    'admin_languages_delete',
                                    'languageId',
                                    'id_lang',
                                    Request::METHOD_DELETE
                                )
                            ),
                    ])
            );
    }

    /**
     * {@inheritdoc}
     */
    protected function getFilters()
    {
        return (new FilterCollection())
            ->add(
                 (new Filter('id_lang', NumberType::class))
                     ->setTypeOptions([
                         'required' => false,
                         'attr' => [
                             'placeholder' => $this->translator->trans('Search ID', [], 'Admin.Actions'),
                         ],
                     ])
                     ->setAssociatedColumn('id_lang')
             )
            ->add(
                 (new Filter('name', TextType::class))
                     ->setTypeOptions([
                         'required' => false,
                         'attr' => [
                             'placeholder' => $this->translator->trans('Search name', [], 'Admin.Actions'),
                         ],
                     ])
                     ->setAssociatedColumn('name')
             )
            ->add(
                 (new Filter('iso_code', TextType::class))
                     ->setTypeOptions([
                         'required' => false,
                         'attr' => [
                             'placeholder' => $this->translator->trans('Search ISO code', [], 'Admin.Actions'),
                         ],
                     ])
                     ->setAssociatedColumn('iso_code')
             )
            ->add(
                 (new Filter('language_code', TextType::class))
                     ->setTypeOptions([
                         'required' => false,
                         'attr' => [
                             'placeholder' => $this->translator->trans('Search code', [], 'Admin.Actions'),
                         ],
                     ])
                     ->setAssociatedColumn('language_code')
             )
            ->add(
                 (new Filter('date_format_lite', TextType::class))
                     ->setTypeOptions([
                         'required' => false,
                         'attr' => [
                             'placeholder' => $this->translator->trans('Search date format', [], 'Admin.Actions'),
                         ],
                     ])
                     ->setAssociatedColumn('date_format_lite')
             )
            ->add(
                 (new Filter('date_format_full', TextType::class))
                     ->setTypeOptions([
                         'required' => false,
                         'attr' => [
                             'placeholder' => $this->translator->trans('Search date format', [], 'Admin.Actions'),
                         ],
                     ])
                     ->setAssociatedColumn('date_format_full')
             )
            ->add(
                 (new Filter('active', YesAndNoChoiceType::class))
                     ->setAssociatedColumn('active')
             )
            ->add(
                 (new Filter('actions', SearchAndResetType::class))
                     ->setTypeOptions([
                         'reset_route' => 'admin_common_reset_search_by_filter_id',
                         'reset_route_params' => [
                             'filterId' => self::GRID_ID,
                         ],
                         'redirect_route' => 'admin_languages_index',
                     ])
                     ->setAssociatedColumn('actions')
             )
        ;
    }

    /**
     * {@inheritdoc}
     */
    protected function getGridActions()
    {
        return (new GridActionCollection())
            ->add(
                (new SimpleGridAction('common_refresh_list'))
                    ->setName($this->trans('Refresh list', [], 'Admin.Advparameters.Feature'))
                    ->setIcon('refresh')
            )
            ->add(
                (new SimpleGridAction('common_show_query'))
                    ->setName($this->trans('Show SQL query', [], 'Admin.Actions'))
                    ->setIcon('code')
            )
            ->add(
                (new SimpleGridAction('common_export_sql_manager'))
                    ->setName($this->trans('Export to SQL Manager', [], 'Admin.Actions'))
                    ->setIcon('storage')
            );
    }

    /**
     * {@inheritdoc}
     */
    protected function getBulkActions()
    {
        return (new BulkActionCollection())
            ->add(
                (new SubmitBulkAction('enable_selection'))
                    ->setName($this->trans('Enable selection', [], 'Admin.Actions'))
                    ->setOptions([
                        'submit_route' => 'admin_languages_bulk_toggle_status',
                        'route_params' => [
                            'status' => 'enable',
                        ],
                    ])
            )
            ->add(
                (new SubmitBulkAction('disable_selection'))
                    ->setName($this->trans('Disable selection', [], 'Admin.Actions'))
                    ->setOptions([
                        'submit_route' => 'admin_languages_bulk_toggle_status',
                        'route_params' => [
                            'status' => 'disable',
                        ],
                    ])
            )
            ->add(
                $this->buildBulkDeleteAction('admin_languages_bulk_delete')
            );
    }
}
