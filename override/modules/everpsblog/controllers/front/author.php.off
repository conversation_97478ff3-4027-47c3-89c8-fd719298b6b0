<?php
/**
 * Page Cache Ultimate, Page Cache standard and Speed pack are powered by <PERSON><PERSON><PERSON><PERSON> (jpresta . com)
 *
 * <AUTHOR>
 * @copyright Jpresta
 * @license   See the license of this module in file LICENSE.txt, thank you.
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

/**
 * This override is disabled by default because the views 'count' is updated each time and so clears the cache is soon as it is created.
 * To work, this count should be disabled or directly done with an SQL query without using the ObjectModel function 'update'.
 */
class EverPsBlogauthorModuleFrontControllerOverride extends EverPsBlogauthorModuleFrontController
{
    /**
     * @return string The ObjectModel class name to be used by Page Cache Ultimate module to refresh the cache of pages generated by this controller
     */
    public static function getJprestaModelObjectClassName()
    {
        return 'EverPsBlogAuthor';
    }

    /**
     * @return int|null The ID of the current ObjectModel (if any) to be used by Page Cache Ultimate module to refresh the cache of pages generated by this controller
     */
    public function getJprestaModelObjectId()
    {
        $id_object = (int)Tools::getValue('id_ever_author');
        if ($id_object && ($postObj = new EverPsBlogAuthor($id_object)) && Validate::isLoadedObject($postObj)) {
            return $id_object;
        }
        return null;
    }
}
