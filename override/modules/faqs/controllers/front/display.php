<?php
/**
 * Page Cache Ultimate, Page Cache standard and Speed pack are powered by <PERSON><PERSON><PERSON><PERSON> (jpresta . com)
 *
 * <AUTHOR>
 * @copyright Jpresta
 * @license   See the license of this module in file LICENSE.txt, thank you.
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

/**
 * Implements getJprestaModelObjectClassName() and getJprestaModelObjectId() to enable the HTML cache provided by the
 * module Page Cache Ultimate created by jpresta.com
 */
class FaqsDisplayModuleFrontControllerOverride extends FaqsDisplayModuleFrontController
{
    /**
     * @return string The ObjectModel class name to be used by Page Cache Ultimate module to refresh the cache of pages generated by this controller
     */
    public static function getJprestaModelObjectClassName()
    {
        $faqCategoryId = Tools::getValue("category");
        $faqId = Tools::getValue("question");
        if ($faqCategoryId && $faqId) {
            return 'faqsPost';
        }
        else if ($faqCategoryId) {
            return 'faqsCategory';
        }
        else {
            return 'FakeFaqToEnableCache';
        }
    }

    /**
     * @return int|null The ID of the current ObjectModel (if any) to be used by Page Cache Ultimate module to refresh the cache of pages generated by this controller
     */
    public function getJprestaModelObjectId()
    {
        $faqCategoryId = Tools::getValue("category");
        $faqId = Tools::getValue("question");
        if ($faqCategoryId && $faqId) {
            $faq = faqsPost::getFaqsByUrl((int) Shop::getContextShopID(), Context::getContext()->language->id, $faqCategoryId, $faqId, true);
            return $faq[0] ? (int) $faq[0]['id_gomakoil_faq'] : null;
        }
        else if ($faqCategoryId) {
            $cat = faqsCategory::getCategoryByName((int) Shop::getContextShopID(), Context::getContext()->language->id, $faqCategoryId);
            return $cat[0] ? (int) $cat[0]['id_gomakoil_faq_category'] : null;
        }
        else {
            return null;
        }
    }

    /**
     * List all URLs generated by this controller for the current shop context and the specified language.
     * This is used by the Jresta-Cache-Warmer service to generate the cache of these pages.
     * @param $id_lang int ID of the language
     * @return string[] All URLs to warmup
     */
    public static function getJprestaAllURLs($id_lang)
    {
        $id_shop = (int)Shop::getContextShopID();
        $links = [];
        // URL of the root
        $baseUrl = $links[] = Context::getContext()->link->getModuleLink('faqs', 'display', [], $id_shop, $id_lang);
        // URLs of categories
        $cats = JPresta\SpeedPack\JprestaUtils::dbSelectRows('
            SELECT c.id_gomakoil_faq_category, cl.link_rewrite
            FROM `' . _DB_PREFIX_ . 'gomakoil_faq_category` c
            LEFT JOIN `' . _DB_PREFIX_ . 'gomakoil_faq_category_shop` cs ON (c.id_gomakoil_faq_category = cs.id_gomakoil_faq_category)
            LEFT JOIN `' . _DB_PREFIX_ . 'gomakoil_faq_category_lang` cl ON (c.id_gomakoil_faq_category = cl.id_gomakoil_faq_category)
            WHERE c.active=1 AND cs.id_shop=' . (int)$id_shop . ' AND cl.id_lang=' . (int)$id_lang);
        foreach ($cats as $cat) {
            if (!faqs::getRewriteSettings()) {
                $links[] = $baseUrl . '&category=' . $cat['link_rewrite'];
            } else {
                $links[] = $baseUrl . $cat['link_rewrite'] . '.html';
            }
            // URLs of FAQs
            $faqs = JPresta\SpeedPack\JprestaUtils::dbSelectRows('
                SELECT fl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'gomakoil_faq` f
                LEFT JOIN `' . _DB_PREFIX_ . 'gomakoil_faq_shop` fs ON (f.id_gomakoil_faq = fs.id_gomakoil_faq)
                LEFT JOIN `' . _DB_PREFIX_ . 'gomakoil_faq_lang` fl ON (f.id_gomakoil_faq = fl.id_gomakoil_faq)
                WHERE f.active=1 AND f.id_gomakoil_faq_category=' . (int)$cat['id_gomakoil_faq_category'] . ' AND fs.id_shop=' . (int)$id_shop . ' AND fl.id_lang=' . (int)$id_lang);
            foreach ($faqs as $faq) {
                if (!faqs::getRewriteSettings()) {
                    $links[] = $baseUrl . '&category=' . $cat['link_rewrite'] . '&question=' . $faq['link_rewrite'];
                } else {
                    $links[] = $baseUrl . $cat['link_rewrite'] . '/' . $faq['link_rewrite'] . '.html';
                }
            }
        }
        return $links;
    }

    /**
     * An estimated number of URLs that will be returned by self::getJprestaAllURLs() for the current shop context.
     * Since we don't have the id_lang parameter we recommend to return the number of URLs for the language that have
     * the most URLs.
     * @return int The estimated number of URLs to warmup for the current shop context
     */
    public static function getJprestaAllURLsCount() {
        $queryCountPagesCat = '
                    SELECT COUNT(*)
                    FROM `' . _DB_PREFIX_ . 'gomakoil_faq_category` c
                    LEFT JOIN `' . _DB_PREFIX_ . 'gomakoil_faq_category_shop` cs ON (c.id_gomakoil_faq_category = cs.id_gomakoil_faq_category)
                    WHERE c.`active` = 1 AND cs.`id_shop` = ' . (int) Shop::getContextShopID();
        $queryCountPagesFaq = '
                    SELECT COUNT(*)
                    FROM `' . _DB_PREFIX_ . 'gomakoil_faq` f
                    LEFT JOIN `' . _DB_PREFIX_ . 'gomakoil_faq_shop` fs ON (f.id_gomakoil_faq = fs.id_gomakoil_faq)
                    WHERE f.`active` = 1 AND fs.`id_shop` = ' . (int) Shop::getContextShopID();
        $countPagesRoot = 1;
        return (int) JPresta\SpeedPack\JprestaUtils::dbGetValue($queryCountPagesCat)
            + (int) JPresta\SpeedPack\JprestaUtils::dbGetValue($queryCountPagesFaq)
            + $countPagesRoot;
    }
}
