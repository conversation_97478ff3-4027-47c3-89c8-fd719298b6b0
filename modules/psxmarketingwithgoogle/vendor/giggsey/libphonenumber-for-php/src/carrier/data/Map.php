<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  'ar' => 
  array (
    0 => 965,
  ),
  'be' => 
  array (
    0 => 375,
  ),
  'en' => 
  array (
    0 => 1,
    1 => 20,
    2 => 211,
    3 => 212,
    4 => 213,
    5 => 216,
    6 => 218,
    7 => 220,
    8 => 221,
    9 => 222,
    10 => 223,
    11 => 224,
    12 => 225,
    13 => 226,
    14 => 227,
    15 => 228,
    16 => 229,
    17 => 230,
    18 => 231,
    19 => 232,
    20 => 233,
    21 => 234,
    22 => 235,
    23 => 236,
    24 => 237,
    25 => 238,
    26 => 239,
    27 => 240,
    28 => 241,
    29 => 242,
    30 => 243,
    31 => 244,
    32 => 245,
    33 => 246,
    34 => 247,
    35 => 248,
    36 => 249,
    37 => 250,
    38 => 251,
    39 => 252,
    40 => 253,
    41 => 254,
    42 => 255,
    43 => 256,
    44 => 257,
    45 => 258,
    46 => 260,
    47 => 261,
    48 => 262,
    49 => 263,
    50 => 264,
    51 => 265,
    52 => 266,
    53 => 267,
    54 => 268,
    55 => 269,
    56 => 27,
    57 => 290,
    58 => 291,
    59 => 297,
    60 => 298,
    61 => 299,
    62 => 30,
    63 => 31,
    64 => 32,
    65 => 33,
    66 => 34,
    67 => 350,
    68 => 351,
    69 => 352,
    70 => 353,
    71 => 354,
    72 => 355,
    73 => 356,
    74 => 357,
    75 => 358,
    76 => 359,
    77 => 36,
    78 => 370,
    79 => 371,
    80 => 372,
    81 => 373,
    82 => 374,
    83 => 375,
    84 => 376,
    85 => 377,
    86 => 378,
    87 => 380,
    88 => 381,
    89 => 382,
    90 => 383,
    91 => 385,
    92 => 386,
    93 => 387,
    94 => 389,
    95 => 39,
    96 => 40,
    97 => 41,
    98 => 420,
    99 => 421,
    100 => 423,
    101 => 43,
    102 => 44,
    103 => 45,
    104 => 46,
    105 => 47,
    106 => 48,
    107 => 49,
    108 => 500,
    109 => 501,
    110 => 502,
    111 => 503,
    112 => 504,
    113 => 505,
    114 => 506,
    115 => 507,
    116 => 508,
    117 => 509,
    118 => 51,
    119 => 53,
    120 => 55,
    121 => 56,
    122 => 57,
    123 => 58,
    124 => 590,
    125 => 591,
    126 => 592,
    127 => 593,
    128 => 594,
    129 => 595,
    130 => 596,
    131 => 597,
    132 => 598,
    133 => 599,
    134 => 60,
    135 => 61,
    136 => 62,
    137 => 63,
    138 => 64,
    139 => 65,
    140 => 66,
    141 => 670,
    142 => 672,
    143 => 673,
    144 => 674,
    145 => 675,
    146 => 676,
    147 => 677,
    148 => 678,
    149 => 679,
    150 => 680,
    151 => 681,
    152 => 682,
    153 => 683,
    154 => 685,
    155 => 686,
    156 => 687,
    157 => 688,
    158 => 689,
    159 => 690,
    160 => 691,
    161 => 692,
    162 => 7,
    163 => 81,
    164 => 82,
    165 => 84,
    166 => 850,
    167 => 852,
    168 => 853,
    169 => 855,
    170 => 856,
    171 => 86,
    172 => 880,
    173 => 881,
    174 => 882,
    175 => 886,
    176 => 90,
    177 => 91,
    178 => 92,
    179 => 93,
    180 => 94,
    181 => 95,
    182 => 960,
    183 => 961,
    184 => 962,
    185 => 963,
    186 => 964,
    187 => 965,
    188 => 966,
    189 => 967,
    190 => 968,
    191 => 970,
    192 => 971,
    193 => 972,
    194 => 973,
    195 => 974,
    196 => 975,
    197 => 976,
    198 => 977,
    199 => 98,
    200 => 992,
    201 => 993,
    202 => 994,
    203 => 995,
    204 => 996,
    205 => 998,
  ),
  'fa' => 
  array (
    0 => 93,
    1 => 98,
  ),
  'ko' => 
  array (
    0 => 82,
  ),
  'ru' => 
  array (
    0 => 374,
    1 => 375,
    2 => 7,
  ),
  'uk' => 
  array (
    0 => 380,
  ),
  'zh' => 
  array (
    0 => 852,
    1 => 86,
  ),
  'zh_Hant' => 
  array (
    0 => 852,
    1 => 86,
  ),
);
