<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  59069000 => 'SFR/Rife',
  59069005 => 'SFR/Rife',
  59069006 => 'Digicel',
  59069007 => 'Digicel',
  59069008 => 'Digicel',
  59069009 => 'Digicel',
  5906901 => 'Digicel',
  59069010 => 'UTS',
  59069020 => 'Digicel',
  59069021 => 'Digicel',
  59069022 => 'Dauphin Telecom',
  59069023 => 'Digicel',
  59069024 => 'Digicel',
  59069025 => 'Digicel',
  59069026 => 'Orange',
  59069027 => 'Orange',
  59069028 => 'Orange',
  59069029 => 'Orange',
  5906903 => 'Orange',
  59069036 => 'Digicel',
  59069040 => 'Orange',
  59069041 => 'Orange',
  59069042 => 'Digicel',
  59069043 => 'Digicel',
  59069044 => 'Digicel',
  59069045 => 'Digicel',
  59069046 => 'Digicel',
  59069047 => 'Orange',
  59069048 => 'Orange',
  59069049 => 'Orange',
  5906905 => 'Orange',
  5906906 => 'Orange',
  59069066 => 'Dauphin Telecom',
  59069069 => 'Digicel',
  5906907 => 'Orange',
  59069077 => 'Dauphin Telecom',
  59069078 => 'SFR/Rife',
  59069079 => 'SFR/Rife',
  5906908 => 'Digicel',
  59069087 => 'UTS',
  59069088 => 'Dauphin Telecom',
  59069089 => 'SFR/Rife',
  5906909 => 'SFR/Rife',
  5906912 => 'Free Caraïbe',
  59069122 => 'Dauphin Telecom',
  59069129 => 'Digicel',
  59069130 => 'Digicel',
  59069131 => 'Orange',
  59069132 => 'Orange',
  59069133 => 'Orange',
  59069134 => 'Orange',
  59069135 => 'Orange',
  590694 => 'Digicel',
  590696 => 'Digicel',
);
