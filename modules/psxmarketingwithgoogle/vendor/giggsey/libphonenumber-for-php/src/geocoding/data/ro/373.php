<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  373210 => 'Grigoriopol',
  373215 => 'Dubăsari',
  373216 => 'Camenca',
  373219 => 'Dnestrovsk',
  37322 => 'Chişinău',
  373230 => 'Soroca',
  373231 => 'Bălţi',
  373235 => 'Orhei',
  373236 => 'Ungheni',
  373237 => 'Străşeni',
  373241 => 'Cimişlia',
  373242 => 'Ştefan Vodă',
  373243 => 'Căuşeni',
  373244 => 'Călăraşi',
  373246 => 'Ed<PERSON>ţ',
  373247 => '<PERSON>rice<PERSON>',
  373248 => '<PERSON><PERSON><PERSON><PERSON>',
  373249 => 'Glodeni',
  373250 => '<PERSON>lore<PERSON><PERSON>',
  373251 => 'Donduşeni',
  373252 => 'Drochia',
  373254 => 'Rezina',
  373256 => 'Rîşcani',
  373258 => 'Teleneşti',
  373259 => 'Făleşti',
  373262 => 'Sîngerei',
  373263 => 'Leova',
  373264 => 'Nisporeni',
  373265 => 'Anenii Noi',
  373268 => 'Ialoveni',
  373269 => 'Hînceşti',
  373271 => 'Ocniţa',
  373272 => 'Şoldăneşti',
  373273 => 'Cantemir',
  373291 => 'Ceadîr Lunga',
  373293 => 'Vulcăneşti',
  373294 => 'Taraclia',
  373297 => 'Basarabeasca',
  373298 => 'Comrat',
  373299 => 'Cahul',
  37353 => 'Tiraspol',
  373552 => 'Bender',
  373555 => 'Rîbniţa',
  373557 => 'Slobozia',
);
