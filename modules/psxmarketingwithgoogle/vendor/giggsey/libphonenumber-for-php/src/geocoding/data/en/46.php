<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  4611 => 'Norrköping',
  46120 => 'Åtvidaberg',
  46121 => 'Söderköping',
  46122 => 'Finspång',
  46123 => 'Valdemarsvik',
  46125 => 'Vikbolandet',
  4613 => 'Linköping',
  46140 => 'Tranås',
  46141 => 'Motala',
  46142 => 'Mjölby-Skänninge-Boxholm',
  46143 => 'Vadstena',
  46144 => 'Ödeshög',
  46150 => 'Katrineholm',
  46151 => 'Vingåker',
  46152 => 'Strängnäs',
  46155 => 'Nyköping-Oxelösund',
  46156 => 'Trosa-Vagnhärad',
  46157 => '<PERSON>len-Malmköping',
  46158 => '<PERSON>nesta',
  46159 => '<PERSON>fred',
  4616 => 'Eskilstuna-Torshälla',
  46171 => 'Enköping',
  46173 => 'Öregrund-Östhammar',
  46174 => 'Alunda',
  46175 => 'Hallstavik-Rimbo',
  46176 => 'Norrtälje',
  4618 => 'Uppsala',
  4619 => 'Örebro-Kumla',
  4621 => 'Västerås',
  46220 => 'Hallstahammar-Surahammar',
  46221 => 'Köping',
  46222 => 'Skinnskatteberg',
  46223 => 'Fagersta-Norberg',
  46224 => 'Sala-Heby',
  46225 => 'Hedemora-Säter',
  46226 => 'Avesta-Krylbo',
  46227 => 'Kungsör',
  4623 => 'Falun',
  46240 => 'Ludvika-Smedjebacken',
  46241 => 'Gagnef-Floda',
  46243 => 'Borlänge',
  46246 => 'Svärdsjö-Enviken',
  46247 => 'Leksand-Insjön',
  46248 => 'Rättvik',
  46250 => 'Mora-Orsa',
  46251 => 'Älvdalen',
  46253 => 'Idre-Särna',
  46258 => 'Furudal',
  4626 => 'Gävle-Sandviken',
  46270 => 'Söderhamn',
  46271 => 'Alfta-Edsbyn',
  46278 => 'Bollnäs',
  46280 => 'Malung',
  46281 => 'Vansbro',
  46290 => 'Hofors-Storvik',
  46291 => 'Hedesunda-Österfärnebo',
  46292 => 'Tärnsjö-Östervåla',
  46293 => 'Tierp-Söderfors',
  46294 => 'Karlholmsbruk-Skärplinge',
  46295 => 'Örbyhus-Dannemora',
  46297 => 'Ockelbo-Hamrånge',
  46300 => 'Kungsbacka',
  46301 => 'Hindås',
  46302 => 'Lerum',
  46303 => 'Kungälv',
  46304 => 'Orust-Tjörn',
  4631 => 'Gothenburg',
  46320 => 'Kinna',
  46321 => 'Ulricehamn',
  46322 => 'Alingsås-Vårgårda',
  46325 => 'Svenljunga-Tranemo',
  4633 => 'Borås',
  46340 => 'Varberg',
  46345 => 'Hyltebruk-Torup',
  46346 => 'Falkenberg',
  4635 => 'Halmstad',
  4636 => 'Jönköping-Huskvarna',
  46370 => 'Värnamo',
  46371 => 'Gislaved-Anderstorp',
  46372 => 'Ljungby',
  46380 => 'Nässjö',
  46381 => 'Eksjö',
  46382 => 'Sävsjö',
  46383 => 'Vetlanda',
  46390 => 'Gränna',
  46392 => 'Mullsjö',
  46393 => 'Vaggeryd',
  4640 => 'Malmö',
  46410 => 'Trelleborg',
  46411 => 'Ystad',
  46413 => 'Eslöv-Höör',
  46414 => 'Simrishamn',
  46415 => 'Hörby',
  46416 => 'Sjöbo',
  46417 => 'Tomelilla',
  46418 => 'Landskrona-Svalöv',
  4642 => 'Helsingborg-Höganäs',
  46430 => 'Laholm',
  46431 => 'Ängelholm-Båstad',
  46433 => 'Markaryd-Strömsnäsbruk',
  46435 => 'Klippan-Perstorp',
  4644 => 'Kristianstad',
  46451 => 'Hässleholm',
  46454 => 'Karlshamn-Olofström',
  46455 => 'Karlskrona',
  46456 => 'Sölvesborg-Bromölla',
  46457 => 'Ronneby',
  46459 => 'Ryd',
  4646 => 'Lund',
  46470 => 'Växjö',
  46471 => 'Emmaboda',
  46472 => 'Alvesta-Rydaholm',
  46474 => 'Åseda-Lenhovda',
  46476 => 'Älmhult',
  46477 => 'Tingsryd',
  46478 => 'Lessebo',
  46479 => 'Osby',
  46480 => 'Kalmar',
  46481 => 'Nybro',
  46485 => 'Öland',
  46486 => 'Torsås',
  46490 => 'Västervik',
  46491 => 'Oskarshamn-Högsby',
  46492 => 'Vimmerby',
  46493 => 'Gamleby',
  46494 => 'Kisa',
  46495 => 'Hultsfred-Virserum',
  46496 => 'Mariannelund',
  46498 => 'Gotland',
  46499 => 'Mönsterås',
  46500 => 'Skövde',
  46501 => 'Mariestad',
  46502 => 'Tidaholm',
  46503 => 'Hjo',
  46504 => 'Tibro',
  46505 => 'Karlsborg',
  46506 => 'Töreboda-Hova',
  46510 => 'Lidköping',
  46511 => 'Skara-Götene',
  46512 => 'Vara-Nossebro',
  46513 => 'Herrljunga',
  46514 => 'Grästorp',
  46515 => 'Falköping',
  46520 => 'Trollhättan',
  46521 => 'Vänersborg',
  46522 => 'Uddevalla',
  46523 => 'Lysekil',
  46524 => 'Munkedal',
  46525 => 'Grebbestad',
  46526 => 'Strömstad',
  46528 => 'Färgelanda',
  46530 => 'Mellerud',
  46531 => 'Bengtsfors',
  46532 => 'Åmål',
  46533 => 'Säffle',
  46534 => 'Ed',
  4654 => 'Karlstad',
  46550 => 'Kristinehamn',
  46551 => 'Gullspång',
  46552 => 'Deje',
  46553 => 'Molkom',
  46554 => 'Kil',
  46555 => 'Grums',
  46560 => 'Torsby',
  46563 => 'Hagfors-Munkfors',
  46564 => 'Sysslebäck',
  46565 => 'Sunne',
  46570 => 'Arvika',
  46571 => 'Charlottenberg-Åmotfors',
  46573 => 'Årjäng',
  46580 => 'Kopparberg',
  46581 => 'Lindesberg',
  46582 => 'Hallsberg',
  46583 => 'Askersund',
  46584 => 'Laxå',
  46585 => 'Fjugesta-Svartå',
  46586 => 'Karlskoga-Degerfors',
  46587 => 'Nora',
  46589 => 'Arboga',
  46590 => 'Filipstad',
  46591 => 'Hällefors-Grythyttan',
  4660 => 'Sundsvall-Timrå',
  46611 => 'Härnösand',
  46612 => 'Kramfors',
  46613 => 'Ullånger',
  46620 => 'Sollefteå',
  46621 => 'Junsele',
  46622 => 'Näsåker',
  46623 => 'Ramsele',
  46624 => 'Backe',
  4663 => 'Östersund',
  46640 => 'Krokom',
  46642 => 'Lit',
  46643 => 'Hallen-Oviken',
  46644 => 'Hammerdal',
  46645 => 'Föllinge',
  46647 => 'Åre-Järpen',
  46650 => 'Hudiksvall',
  46651 => 'Ljusdal',
  46652 => 'Bergsjö',
  46653 => 'Delsbo',
  46657 => 'Los',
  46660 => 'Örnsköldsvik',
  46661 => 'Bredbyn',
  46662 => 'Björna',
  46663 => 'Husum',
  46670 => 'Strömsund',
  46671 => 'Hoting',
  46672 => 'Gäddede',
  46680 => 'Sveg',
  46682 => 'Rätan',
  46684 => 'Hede-Funäsdalen',
  46687 => 'Svenstavik',
  46690 => 'Ånge',
  46691 => 'Torpshammar',
  46692 => 'Liden',
  46693 => 'Bräcke-Gällö',
  46695 => 'Stugun',
  46696 => 'Hammarstrand',
  468 => 'Stockholm',
  46901 => 'Umeå',
  46902 => 'Umeå',
  46903 => 'Umeå',
  46904 => 'Umeå',
  46905 => 'Umeå',
  46906 => 'Umeå',
  46907 => 'Umeå',
  46908 => 'Umeå',
  46909 => 'Umeå',
  46910 => 'Skellefteå',
  46911 => 'Piteå',
  46912 => 'Byske',
  46913 => 'Lövånger',
  46914 => 'Burträsk',
  46915 => 'Bastuträsk',
  46916 => 'Jörn',
  46918 => 'Norsjö',
  46920 => 'Luleå',
  46921 => 'Boden',
  46922 => 'Haparanda',
  46923 => 'Kalix',
  46924 => 'Råneå',
  46925 => 'Lakaträsk',
  46926 => 'Överkalix',
  46927 => 'Övertorneå',
  46928 => 'Harads',
  46929 => 'Älvsbyn',
  46930 => 'Nordmaling',
  46932 => 'Bjurholm',
  46933 => 'Vindeln',
  46934 => 'Robertsfors',
  46935 => 'Vännäs',
  46940 => 'Vilhelmina',
  46941 => 'Åsele',
  46942 => 'Dorotea',
  46943 => 'Fredrika',
  46950 => 'Lycksele',
  46951 => 'Storuman',
  46952 => 'Sorsele',
  46953 => 'Malå',
  46954 => 'Tärnaby',
  46960 => 'Arvidsjaur',
  46961 => 'Arjeplog',
  46970 => 'Gällivare',
  46971 => 'Jokkmokk',
  46973 => 'Porjus',
  46975 => 'Hakkas',
  46976 => 'Vuollerim',
  46977 => 'Korpilombolo',
  46978 => 'Pajala',
  46980 => 'Kiruna',
  46981 => 'Vittangi',
);
