<?php
/**
 * Locale @generated from CLDR version 44.0.0
 * See README.md for more information.
 *
 * @internal
 *
 * Do not modify or use this file directly!
 */

return array (
  'AD' => 'Emetab Andorra',
  'AE' => 'Emetab kibagenge nebo arabuk',
  'AF' => 'Emetab Afghanistan',
  'AG' => 'Emetab Antigua ak Barbuda',
  'AI' => 'Emetab Anguilla',
  'AL' => 'Emetab Albania',
  'AM' => 'Emetab Armenia',
  'AO' => 'Emetab Angola',
  'AR' => 'Emetab Argentina',
  'AS' => 'Emetab American Samoa',
  'AT' => 'Emetab Austria',
  'AU' => 'Emetab Australia',
  'AW' => 'Emetab Aruba',
  'AZ' => 'Emetab Azerbaijan',
  'BA' => 'Emetab Bosnia ak Herzegovina',
  'BB' => 'Emetab Barbados',
  'BD' => 'Emetab Bangladesh',
  'BE' => 'Emetab Belgium',
  'BF' => 'Emetab Burkina Faso',
  'BG' => 'Emetab Bulgaria',
  'BH' => 'Emetab Bahrain',
  'BI' => 'Emetab Burundi',
  'BJ' => 'Emetab Benin',
  'BM' => 'Emetab Bermuda',
  'BN' => 'Emetab Brunei',
  'BO' => 'Emetab Bolivia',
  'BR' => 'Emetab Brazil',
  'BS' => 'Emetab Bahamas',
  'BT' => 'Emetab Bhutan',
  'BW' => 'Emetab Botswana',
  'BY' => 'Emetab Belarus',
  'BZ' => 'Emetab Belize',
  'CA' => 'Emetab Canada',
  'CD' => 'Emetab Congo - Kinshasa',
  'CF' => 'Emetab Afrika nebo Kwen',
  'CG' => 'Emetab Congo - Brazzaville',
  'CH' => 'Emetab Switzerland',
  'CI' => 'Emetab Côte d’Ivoire',
  'CK' => 'Ikwembeyotab Cook',
  'CL' => 'Emetab Chile',
  'CM' => 'Emetab Cameroon',
  'CN' => 'Emetab China',
  'CO' => 'Emetab Colombia',
  'CR' => 'Emetab Costa Rica',
  'CU' => 'Emetab Cuba',
  'CV' => 'Ikwembeyotab Cape Verde',
  'CY' => 'Emetab Cyprus',
  'CZ' => 'Emetab Czech Republic',
  'DE' => 'Emetab Geruman',
  'DJ' => 'Emetab Djibouti',
  'DK' => 'Emetab Denmark',
  'DM' => 'Emetab Dominica',
  'DO' => 'Emetab Dominican Republic',
  'DZ' => 'Emetab Algeria',
  'EC' => 'Emetab Ecuador',
  'EE' => 'Emetab Estonia',
  'EG' => 'Emetab Misiri',
  'ER' => 'Emetab Eritrea',
  'ES' => 'Emetab Spain',
  'ET' => 'Emetab Ethiopia',
  'FI' => 'Emetab Finland',
  'FJ' => 'Emetab Fiji',
  'FK' => 'Ikwembeyotab Falkland',
  'FM' => 'Emetab Micronesia',
  'FR' => 'Emetab France',
  'GA' => 'Emetab Gabon',
  'GB' => 'Emetab Kibagenge nebo Uingereza',
  'GD' => 'Emetab Grenada',
  'GE' => 'Emetab Georgia',
  'GF' => 'Emetab Guiana nebo Ufaransa',
  'GH' => 'Emetab Ghana',
  'GI' => 'Emetab Gibraltar',
  'GL' => 'Emetab Greenland',
  'GM' => 'Emetab Gambia',
  'GN' => 'Emetab Guinea',
  'GP' => 'Emetab Guadeloupe',
  'GQ' => 'Emetab Equatorial Guinea',
  'GR' => 'Emetab Greece',
  'GT' => 'Emetab Guatemala',
  'GU' => 'Emetab Guam',
  'GW' => 'Emetab Guinea-Bissau',
  'GY' => 'Emetab Guyana',
  'HN' => 'Emetab Honduras',
  'HR' => 'Emetab Croatia',
  'HT' => 'Emetab Haiti',
  'HU' => 'Emetab Hungary',
  'ID' => 'Emetab Indonesia',
  'IE' => 'Emetab Ireland',
  'IL' => 'Emetab Israel',
  'IN' => 'Emetab India',
  'IQ' => 'Emetab Iraq',
  'IR' => 'Emetab Iran',
  'IS' => 'Emetab Iceland',
  'IT' => 'Emetab Italy',
  'JM' => 'Emetab Jamaica',
  'JO' => 'Emetab Jordan',
  'JP' => 'Emetab Japan',
  'KE' => 'Emetab Kenya',
  'KG' => 'Emetab Kyrgyzstan',
  'KH' => 'Emetab Cambodia',
  'KI' => 'Emetab Kiribati',
  'KM' => 'Emetab Comoros',
  'KN' => 'Emetab Saint Kitts ak Nevis',
  'KP' => 'Emetab Korea nebo murot katam',
  'KR' => 'Emetab korea nebo murot tai',
  'KW' => 'Emetab Kuwait',
  'KY' => 'Ikwembeyotab Cayman',
  'KZ' => 'Emetab Kazakhstan',
  'LA' => 'Emetab Laos',
  'LB' => 'Emetab Lebanon',
  'LC' => 'Emetab Lucia Ne',
  'LI' => 'Emetab Liechtenstein',
  'LK' => 'Emetab Sri Lanka',
  'LR' => 'Emetab Liberia',
  'LS' => 'Emetab Lesotho',
  'LT' => 'Emetab Lithuania',
  'LU' => 'Emetab Luxembourg',
  'LV' => 'Emetab Latvia',
  'LY' => 'Emetab Libya',
  'MA' => 'Emetab Morocco',
  'MC' => 'Emetab Monaco',
  'MD' => 'Emetab Moldova',
  'MG' => 'Emetab Madagascar',
  'MH' => 'Ikwembeiyotab Marshall',
  'ML' => 'Emetab Mali',
  'MM' => 'Emetab Myanmar',
  'MN' => 'Emetab Mongolia',
  'MP' => 'Ikwembeiyotab Mariana nebo murot katam',
  'MQ' => 'Emetab Martinique',
  'MR' => 'Emetab Mauritania',
  'MS' => 'Emetab Montserrat',
  'MT' => 'Emetab Malta',
  'MU' => 'Emetab Mauritius',
  'MV' => 'Emetab Maldives',
  'MW' => 'Emetab Malawi',
  'MX' => 'Emetab Mexico',
  'MY' => 'Emetab Malaysia',
  'MZ' => 'Emetab Mozambique',
  'NA' => 'Emetab Namibia',
  'NC' => 'Emetab New Caledonia',
  'NE' => 'Emetab niger',
  'NF' => 'Ikwembeiyotab Norfork',
  'NG' => 'Emetab Nigeria',
  'NI' => 'Emetab Nicaragua',
  'NL' => 'Emetab Holand',
  'NO' => 'Emetab Norway',
  'NP' => 'Emetab Nepal',
  'NR' => 'Emetab Nauru',
  'NU' => 'Emetab Niue',
  'NZ' => 'Emetab New Zealand',
  'OM' => 'Emetab Oman',
  'PA' => 'Emetab Panama',
  'PE' => 'Emetab Peru',
  'PF' => 'Emetab Polynesia nebo ufaransa',
  'PG' => 'Emetab Papua New Guinea',
  'PH' => 'Emetab Philippines',
  'PK' => 'Emetab Pakistan',
  'PL' => 'Emetab Poland',
  'PM' => 'Emetab Peter Ne titil ak Miquelon',
  'PN' => 'Emetab Pitcairn',
  'PR' => 'Emetab Puerto Rico',
  'PS' => 'Emetab Palestine',
  'PT' => 'Emetab Portugal',
  'PW' => 'Emetab Palau',
  'PY' => 'Emetab Paraguay',
  'QA' => 'Emetab Qatar',
  'RE' => 'Emetab Réunion',
  'RO' => 'Emetab Romania',
  'RU' => 'Emetab Russia',
  'RW' => 'Emetab Rwanda',
  'SA' => 'Emetab Saudi Arabia',
  'SB' => 'Ikwembeiyotab Solomon',
  'SC' => 'Emetab Seychelles',
  'SD' => 'Emetab Sudan',
  'SE' => 'Emetab Sweden',
  'SG' => 'Emetab Singapore',
  'SH' => 'Emetab Helena Ne tilil',
  'SI' => 'Emetab Slovenia',
  'SK' => 'Emetab Slovakia',
  'SL' => 'Emetab Sierra Leone',
  'SM' => 'Emetab San Marino',
  'SN' => 'Emetab Senegal',
  'SO' => 'Emetab Somalia',
  'SR' => 'Emetab Suriname',
  'ST' => 'Emetab São Tomé and Príncipe',
  'SV' => 'Emetab El Salvador',
  'SY' => 'Emetab Syria',
  'SZ' => 'Emetab Swaziland',
  'TC' => 'Ikwembeiyotab Turks ak Caicos',
  'TD' => 'Emetab Chad',
  'TG' => 'Emetab Togo',
  'TH' => 'Emetab Thailand',
  'TJ' => 'Emetab Tajikistan',
  'TK' => 'Emetab Tokelau',
  'TL' => 'Emetab Timor nebo Murot tai',
  'TM' => 'Emetab Turkmenistan',
  'TN' => 'Emetab Tunisia',
  'TO' => 'Emetab Tonga',
  'TR' => 'Emetab Turkey',
  'TT' => 'Emetab Trinidad ak Tobago',
  'TV' => 'Emetab Tuvalu',
  'TW' => 'Emetab Taiwan',
  'TZ' => 'Emetab Tanzania',
  'UA' => 'Emetab Ukrainie',
  'UG' => 'Emetab Uganda',
  'US' => 'Emetab amerika',
  'UY' => 'Emetab Uruguay',
  'UZ' => 'Emetab Uzibekistani',
  'VA' => 'Emetab Vatican',
  'VC' => 'Emetab Vincent netilil ak Grenadines',
  'VE' => 'Emetab Venezuela',
  'VG' => 'Ikwembeyotab British Virgin',
  'VI' => 'Ikwemweiyotab Amerika',
  'VN' => 'Emetab Vietnam',
  'VU' => 'Emetab Vanuatu',
  'WF' => 'Emetab Walis ak Futuna',
  'WS' => 'Emetab Samoa',
  'YE' => 'Emetab Yemen',
  'YT' => 'Emetab Mayotte',
  'ZA' => 'Emetab Afrika nebo Murot tai',
  'ZM' => 'Emetab Zambia',
  'ZW' => 'Emetab Zimbabwe',
);
