<?php return array(
    'root' => array(
        'name' => 'prestashopcorp/psxmarketingwithgoogle',
        'pretty_version' => 'v1.74.9',
        'version' => '1.74.9.0',
        'reference' => 'b72c5d27a1488c4aab703a5ce2e5e63ed7cddf23',
        'type' => 'prestashop-module',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'brick/phonenumber' => array(
            'pretty_version' => '0.4.1',
            'version' => '0.4.1.0',
            'reference' => '31886072e6ebfe44f027c63dd29d53913fac802b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/phonenumber',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'clue/stream-filter' => array(
            'pretty_version' => 'v1.7.0',
            'version' => '1.7.0.0',
            'reference' => '049509fef80032cb3f051595029ab75b49a3c2f7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clue/stream-filter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'giggsey/libphonenumber-for-php' => array(
            'pretty_version' => '8.13.32',
            'version' => '8.13.32.0',
            'reference' => '11aa286e82f2bb567815a89da1e63e2848b81e66',
            'type' => 'library',
            'install_path' => __DIR__ . '/../giggsey/libphonenumber-for-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'giggsey/locale' => array(
            'pretty_version' => '2.5',
            'version' => '2.5.0.0',
            'reference' => 'e6d4540109a01dd2bc7334cdc842d6a6a67cf239',
            'type' => 'library',
            'install_path' => __DIR__ . '/../giggsey/locale',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '1.9.1',
            'version' => '1.9.1.0',
            'reference' => 'e4490cabc77465aaee90b20cfc9a770f8c04be6b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-http/httplug' => array(
            'pretty_version' => 'v1.1.0',
            'version' => '1.1.0.0',
            'reference' => '1c6381726c18579c4ca2ef1ec1498fdae8bdf018',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/httplug',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message' => array(
            'pretty_version' => '1.16.1',
            'version' => '1.16.1.0',
            'reference' => '5997f3289332c699fa2545c427826272498a2088',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-http/promise' => array(
            'pretty_version' => '1.3.1',
            'version' => '1.3.1.0',
            'reference' => 'fc85b1fba37c169a69a07ef0d5a8075770cc1f83',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/promise',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.2',
            'version' => '1.9.2.0',
            'reference' => '80735db690fe4fc5c76dfa7f9b770634285fa820',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'prestashop/module-lib-cache-directory-provider' => array(
            'pretty_version' => 'v1.0.0',
            'version' => '1.0.0.0',
            'reference' => '34a577b66a7e52ae16d6f40efd1db17290bad453',
            'type' => 'project',
            'install_path' => __DIR__ . '/../prestashop/module-lib-cache-directory-provider',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'prestashop/module-lib-faq' => array(
            'pretty_version' => 'v2.2',
            'version' => '2.2.0.0',
            'reference' => '23be3438f0764c9ceb712ce07bd309124e2a355f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../prestashop/module-lib-faq',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'prestashop/module-lib-guzzle-adapter' => array(
            'pretty_version' => 'v0.6',
            'version' => '0.6.0.0',
            'reference' => '451477b899b6fae8865a0face5b3362b07a1f947',
            'type' => 'library',
            'install_path' => __DIR__ . '/../prestashop/module-lib-guzzle-adapter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'prestashop/module-lib-service-container' => array(
            'pretty_version' => 'v2.0',
            'version' => '2.0.0.0',
            'reference' => '5525b56513d9ddad6e4232dfd93a24e028efdca7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../prestashop/module-lib-service-container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'prestashop/prestashop-accounts-installer' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '47f1fb78d3a494cc424361e0895112989f46ef5b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../prestashop/prestashop-accounts-installer',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'prestashopcorp/module-lib-billing' => array(
            'pretty_version' => '3.3.0',
            'version' => '3.3.0.0',
            'reference' => '5645899f02562d8df2e2a05b144347c1b1681272',
            'type' => 'library',
            'install_path' => __DIR__ . '/../prestashopcorp/module-lib-billing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'prestashopcorp/psxmarketingwithgoogle' => array(
            'pretty_version' => 'v1.74.9',
            'version' => '1.74.9.0',
            'reference' => 'b72c5d27a1488c4aab703a5ce2e5e63ed7cddf23',
            'type' => 'prestashop-module',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'segmentio/analytics-php' => array(
            'pretty_version' => '1.8.0',
            'version' => '1.8.0.0',
            'reference' => '7e25b2f6094632bbfb79e33ca024d533899a2ffe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../segmentio/analytics-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sentry/sentry' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '159eeaa02bb2ef8a8ec669f3c88e4bff7e6a7ffe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sentry/sentry',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'ef4d7e442ca910c4764bce785146269b30cb5fc4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '9773676c8a1bb1f8d4340a62efe641cf76eda7ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '87b68208d5c1188808dd7839ee1e6c8ec3b02f1b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v3.6.10',
            'version' => '3.6.10.0',
            'reference' => '5b547cdb25825f10251370f57ba5d9d924e6f68e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
