/*! For license information please see fetchVerificationTag.js.LICENSE.txt */
(()=>{var t,e,n,r,i={162:(t,e,n)=>{"use strict";n.d(e,{v:()=>r});var r=function(){function t(){this.callbacks={}}return t.prototype.on=function(t,e){return this.callbacks[t]?this.callbacks[t].push(e):this.callbacks[t]=[e],this},t.prototype.once=function(t,e){var n=this,r=function(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];n.off(t,r),e.apply(n,i)};return this.on(t,r),this},t.prototype.off=function(t,e){var n,r=(null!==(n=this.callbacks[t])&&void 0!==n?n:[]).filter((function(t){return t!==e}));return this.callbacks[t]=r,this},t.prototype.emit=function(t){for(var e,n=this,r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return(null!==(e=this.callbacks[t])&&void 0!==e?e:[]).forEach((function(t){t.apply(n,r)})),this},t}()},9483:(t,e,n)=>{"use strict";n.d(e,{s:()=>i,w:()=>o});var r=n(9663);function i(t,e){return new Promise((function(n,r){var i=setTimeout((function(){r(Error("Promise timed out"))}),e);t.then((function(t){return clearTimeout(i),n(t)})).catch(r)}))}function o(t,e,n,o){var s;return(s=n,new Promise((function(t){return setTimeout(t,s)}))).then((function(){return i(function(){try{return(0,r.m)(e(t))}catch(t){return Promise.reject(t)}}(),null!=o?o:1e3)})).catch((function(e){null==t||t.log("warn","Callback Error",{error:e}),null==t||t.stats.increment("callback_error")})).then((function(){return t}))}},4660:(t,e,n)=>{"use strict";n.d(e,{a:()=>o,s:()=>i});var r=n(1341);function i(){return!(0,r.B)()||window.navigator.onLine}function o(){return!i()}},9320:(t,e,n)=>{"use strict";n.d(e,{o:()=>g,d:()=>m});var r=n(8973),i=n(7407),o=n(5450);const s=function(){function t(){var t=this;this._logs=[],this.log=function(e,n,r){var i=new Date;t._logs.push({level:e,message:n,time:i,extras:r})}}return Object.defineProperty(t.prototype,"logs",{get:function(){return this._logs},enumerable:!1,configurable:!0}),t.prototype.flush=function(){if(this.logs.length>1){var t=this._logs.reduce((function(t,e){var n,r,i,s=(0,o.Cl)((0,o.Cl)({},e),{json:JSON.stringify(e.extras,null," "),extras:e.extras});delete s.time;var a=null!==(i=null===(r=e.time)||void 0===r?void 0:r.toISOString())&&void 0!==i?i:"";return t[a]&&(a="".concat(a,"-").concat(Math.random())),(0,o.Cl)((0,o.Cl)({},t),((n={})[a]=s,n))}),{});console.table?console.table(t):console.log(t)}else this.logs.forEach((function(t){var e=t.level,n=t.message,r=t.extras;"info"===e||"debug"===e?console.log(n,null!=r?r:""):console[e](n,null!=r?r:"")}));this._logs=[]},t}();var a=function(){function t(t){this.metrics=[],this.remoteMetrics=t}return t.prototype.increment=function(t,e,n){var r;void 0===e&&(e=1),this.metrics.push({metric:t,value:e,tags:null!=n?n:[],type:"counter",timestamp:Date.now()}),null===(r=this.remoteMetrics)||void 0===r||r.increment(t,null!=n?n:[])},t.prototype.gauge=function(t,e,n){this.metrics.push({metric:t,value:e,tags:null!=n?n:[],type:"gauge",timestamp:Date.now()})},t.prototype.flush=function(){var t=this.metrics.map((function(t){return(0,o.Cl)((0,o.Cl)({},t),{tags:t.tags.join(",")})}));console.table?console.table(t):console.log(t),this.metrics=[]},t.prototype.serialize=function(){return this.metrics.map((function(t){return{m:t.metric,v:t.value,t:t.tags,k:(e=t.type,{gauge:"g",counter:"c"}[e]),e:t.timestamp};var e}))},t}();const c=a;var u=n(3866),l=n(3605),d=n(6205);function p(t){console.error("Error sending segment performance metrics",t)}var h,f=function(){function t(t){var e,n,r,i,o=this;if(this.host=null!==(e=null==t?void 0:t.host)&&void 0!==e?e:"api.segment.io/v1",this.sampleRate=null!==(n=null==t?void 0:t.sampleRate)&&void 0!==n?n:1,this.flushTimer=null!==(r=null==t?void 0:t.flushTimer)&&void 0!==r?r:3e4,this.maxQueueSize=null!==(i=null==t?void 0:t.maxQueueSize)&&void 0!==i?i:20,this.queue=[],this.sampleRate>0){var s=!1,a=function(){s||(s=!0,o.flush().catch(p),s=!1,setTimeout(a,o.flushTimer))};a()}}return t.prototype.increment=function(t,e){if(t.includes("analytics_js.")&&0!==e.length&&!(Math.random()>this.sampleRate||this.queue.length>=this.maxQueueSize)){var n=e.reduce((function(t,e){var n=e.split(":"),r=n[0],i=n[1];return t[r]=i,t}),{});n.library="analytics.js";var r=(0,d.XZ)();n.library_version="web"===r?"next-".concat(l.r):"npm:next-".concat(l.r),this.queue.push({type:"Counter",metric:t,value:1,tags:n}),t.includes("error")&&this.flush().catch(p)}},t.prototype.flush=function(){return(0,o.sH)(this,void 0,void 0,(function(){var t=this;return(0,o.YH)(this,(function(e){switch(e.label){case 0:return this.queue.length<=0?[2]:[4,this.send().catch((function(e){p(e),t.sampleRate=0}))];case 1:return e.sent(),[2]}}))}))},t.prototype.send=function(){return(0,o.sH)(this,void 0,void 0,(function(){var t,e,n;return(0,o.YH)(this,(function(r){return t={series:this.queue},this.queue=[],e={"Content-Type":"text/plain"},n="https://".concat(this.host,"/m"),[2,(0,u.A)(n,{headers:e,body:JSON.stringify(t),method:"POST"})]}))}))},t}(),m=function(t){var e,n,r;this.retry=null===(e=t.retry)||void 0===e||e,this.type=null!==(n=t.type)&&void 0!==n?n:"plugin Error",this.reason=null!==(r=t.reason)&&void 0!==r?r:""},g=function(){function t(t,e){this.logger=new s,this.cancel=function(t){if(t)throw t;throw new m({reason:"Context Cancel"})},this._attempts=0,this._event=t,this._id=null!=e?e:(0,r.v4)(),this.stats=new c(h)}return t.initMetrics=function(t){h=new f(t)},t.system=function(){return new t({type:"track",event:"system"})},t.prototype.isSame=function(t){return t._id===this._id},t.prototype.log=function(t,e,n){this.logger.log(t,e,n)},Object.defineProperty(t.prototype,"id",{get:function(){return this._id},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"event",{get:function(){return this._event},set:function(t){this._event=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attempts",{get:function(){return this._attempts},set:function(t){this._attempts=t},enumerable:!1,configurable:!0}),t.prototype.updateEvent=function(t,e){var n;if("integrations"===t.split(".")[0]){var r=t.split(".")[1];if(!1===(null===(n=this._event.integrations)||void 0===n?void 0:n[r]))return this._event}return(0,i.J)(this._event,t,e),this._event},t.prototype.failedDelivery=function(){return this._failedDelivery},t.prototype.setFailedDelivery=function(t){this._failedDelivery=t},t.prototype.logs=function(){return this.logger.logs},t.prototype.flush=function(){this.logger.flush(),this.stats.flush()},t.prototype.toJSON=function(){return{id:this._id,event:this._event,logs:this.logger.logs,metrics:this.stats.metrics}},t}()},1341:(t,e,n)=>{"use strict";function r(){return"undefined"!=typeof window}function i(){return!r()}n.d(e,{B:()=>r,S:()=>i})},4360:(t,e,n)=>{"use strict";function r(t){try{return decodeURIComponent(t.replace(/\+/g," "))}catch(e){return t}}n.d(e,{p:()=>r})},4347:(t,e,n)=>{"use strict";n.d(e,{C:()=>o,D:()=>s});var r=n(5450),i=n(9320);function o(t,e){var n="action"in e?e.action.name:e.name;t.log("debug","plugin",{plugin:n});var o=(new Date).getTime(),s=e[t.event.type];return void 0===s?Promise.resolve(t):function(n){return(0,r.sH)(this,void 0,void 0,(function(){var n;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,s.apply(e,[t])];case 1:return[2,r.sent()];case 2:return n=r.sent(),[2,Promise.reject(n)];case 3:return[2]}}))}))}().then((function(t){var e=(new Date).getTime()-o;return t.stats.gauge("plugin_time",e,["plugin:".concat(n)]),t})).catch((function(e){if(e instanceof i.d&&"middleware_cancellation"===e.type)throw e;return e instanceof i.d?(t.log("warn",e.type,{plugin:n,error:e}),e):(t.log("error","plugin Error",{plugin:n,error:e}),t.stats.increment("plugin_error",1,["plugin:".concat(n)]),e)}))}function s(t,e){return o(t,e).then((function(e){if(e instanceof i.o)return e;t.log("debug","Context canceled"),t.stats.increment("context_canceled"),t.cancel(e)}))}},3587:(t,e,n)=>{"use strict";n.d(e,{f:()=>i});var r=n(1558);function i(t){var e=function(t){try{return new URL(t)}catch(t){return}}(t);if(e)for(var n=function(t){var e=t.hostname.split("."),n=e[e.length-1],r=[];if(4===e.length&&parseInt(n,10)>0)return r;if(e.length<=1)return r;for(var i=e.length-2;i>=0;--i)r.push(e.slice(i).join("."));return r}(e),i=0;i<n.length;++i){var o="__tld__",s=n[i],a={domain:"."+s};try{if(r.A.set(o,"1",a),r.A.get(o))return r.A.remove(o,a),s}catch(t){return}}}},3605:(t,e,n)=>{"use strict";n.d(e,{r:()=>r});var r="1.44.0"},817:(t,e,n)=>{"use strict";n.r(e),n.d(e,{Analytics:()=>V,AnalyticsBrowser:()=>Lt,AnalyticsNode:()=>Pt,Context:()=>_.o,ContextCancelation:()=>_.d,Cookie:()=>j,EventFactory:()=>C,Group:()=>q,LocalStorage:()=>H,User:()=>B,loadLegacySettings:()=>At});var r=n(5450);function i(t){return"string"==typeof t}function o(t){return"number"==typeof t}function s(t){return"function"==typeof t}function a(t){return"object"===Object.prototype.toString.call(t).slice(8,-1).toLowerCase()}var c=function(t){function e(e,n){var r=t.call(this,n)||this;return r.field=e,r}return(0,r.C6)(e,t),e}(Error);function u(t){var e,n=t&&t.event&&t.event.type,r=t.event;if(void 0===r)throw new c("event","Event is missing");if(!i(n))throw new c("event","Event is not a string");if("track"===n&&!i(r.event))throw new c("event","Event is not a string");var o=null!==(e=r.properties)&&void 0!==e?e:r.traits;if("alias"!==n&&!a(o))throw new c("properties","properties is not an object");if(!function(t){var e,n,r;return i(null!==(r=null!==(n=null!==(e=t.userId)&&void 0!==e?e:t.anonymousId)&&void 0!==n?n:t.groupId)&&void 0!==r?r:t.previousId)}(r))throw new c("userId","Missing userId or anonymousId");return t}var l={name:"Event Validation",type:"before",version:"1.0.0",isLoaded:function(){return!0},load:function(){return Promise.resolve()},track:u,identify:u,page:u,alias:u,group:u,screen:u};function d(t,e,n,r){var o,c=[t,e,n,r],u=a(t)?t.event:t;if(!u||!i(u))throw new Error("Event missing");var l=a(t)?null!==(o=t.properties)&&void 0!==o?o:{}:a(e)?e:{},d={};return a(e)&&!s(n)&&(d=null!=n?n:{}),a(t)&&!s(e)&&(d=null!=e?e:{}),[u,l,d,c.find(s)]}function p(t,e,n,r,o){var c,u,l=null,d=null,p=[t,e,n,r,o],h=p.filter(i);void 0!==h[0]&&void 0!==h[1]&&(l=h[0],d=h[1]),1===h.length&&(l=null,d=h[0]);var f=p.find(s),m=p.filter((function(t){return null===d?a(t):a(t)||null===t})),g=null!==(c=m[0])&&void 0!==c?c:{},y=null!==(u=m[1])&&void 0!==u?u:{};return[l,d,g,y,f]}var h=function(t){return function(){for(var e,n,r,c,u,l=[],d=0;d<arguments.length;d++)l[d]=arguments[d];var p;p=null!==(r=null!==(e=l.find(i))&&void 0!==e?e:null===(n=l.find(o))||void 0===n?void 0:n.toString())&&void 0!==r?r:t.id();var h=l.filter((function(t){return null===p?a(t):a(t)||null===t})),f=null!==(c=h[0])&&void 0!==c?c:{},m=null!==(u=h[1])&&void 0!==u?u:{},g=l.find(s);return[p,f,m,g]}};function f(t,e,n,r){o(t)&&(t=t.toString()),o(e)&&(e=e.toString());var c=[t,e,n,r],u=c.filter(i),l=u[0],d=void 0===l?t:l,p=u[1],h=void 0===p?null:p,f=c.filter(a)[0];return[d,h,void 0===f?{}:f,c.find(s)]}var m,g,y=n(9483),v=n(4660),_=n(9320),b=n(162),S=n(8973),w=n(7407),k=n(5123),x=n.n(k),C=function(){function t(t){this.user=t}return t.prototype.track=function(t,e,n,i){return this.normalize((0,r.Cl)((0,r.Cl)({},this.baseEvent()),{event:t,type:"track",properties:e,options:(0,r.Cl)({},n),integrations:(0,r.Cl)({},i)}))},t.prototype.page=function(t,e,n,i,o){var s,a={type:"page",properties:(0,r.Cl)({},n),options:(0,r.Cl)({},i),integrations:(0,r.Cl)({},o)};return null!==t&&(a.category=t,a.properties=null!==(s=a.properties)&&void 0!==s?s:{},a.properties.category=t),null!==e&&(a.name=e),this.normalize((0,r.Cl)((0,r.Cl)({},this.baseEvent()),a))},t.prototype.screen=function(t,e,n,i,o){var s={type:"screen",properties:(0,r.Cl)({},n),options:(0,r.Cl)({},i),integrations:(0,r.Cl)({},o)};return null!==t&&(s.category=t),null!==e&&(s.name=e),this.normalize((0,r.Cl)((0,r.Cl)({},this.baseEvent()),s))},t.prototype.identify=function(t,e,n,i){return this.normalize((0,r.Cl)((0,r.Cl)({},this.baseEvent()),{type:"identify",userId:t,traits:e,options:(0,r.Cl)({},n),integrations:(0,r.Cl)({},i)}))},t.prototype.group=function(t,e,n,i){return this.normalize((0,r.Cl)((0,r.Cl)({},this.baseEvent()),{type:"group",traits:e,options:(0,r.Cl)({},n),integrations:(0,r.Cl)({},i),groupId:t}))},t.prototype.alias=function(t,e,n,i){var o={userId:t,type:"alias",options:(0,r.Cl)({},n),integrations:(0,r.Cl)({},i)};return null!==e&&(o.previousId=e),void 0===t?this.normalize((0,r.Cl)((0,r.Cl)({},o),this.baseEvent())):this.normalize((0,r.Cl)((0,r.Cl)({},this.baseEvent()),o))},t.prototype.baseEvent=function(){var t={integrations:{},options:{}},e=this.user;return e.id()&&(t.userId=e.id()),e.anonymousId()&&(t.anonymousId=e.anonymousId()),t},t.prototype.context=function(t){var e,n,r,i=["integrations","anonymousId","timestamp","userId"],o=null!==(e=t.options)&&void 0!==e?e:{};delete o.integrations;var s=Object.keys(o),a=null!==(r=null===(n=t.options)||void 0===n?void 0:n.context)&&void 0!==r?r:{},c={};return s.forEach((function(t){"context"!==t&&(i.includes(t)?(0,w.J)(c,t,o[t]):(0,w.J)(a,t,o[t]))})),[a,c]},t.prototype.normalize=function(t){var e,n,i=Object.keys(null!==(e=t.integrations)&&void 0!==e?e:{}).reduce((function(e,n){var i,o;return(0,r.Cl)((0,r.Cl)({},e),((i={})[n]=Boolean(null===(o=t.integrations)||void 0===o?void 0:o[n]),i))}),{}),o=(0,r.Cl)((0,r.Cl)({},i),null===(n=t.options)||void 0===n?void 0:n.integrations),s=this.context(t),a=s[0],c=s[1],u=(t.options,(0,r.Tt)(t,["options"])),l=(0,r.Cl)((0,r.Cl)((0,r.Cl)({timestamp:new Date},u),{context:a,integrations:o}),c),d="ajs-next-"+x().hash(JSON.stringify(l)+(0,S.v4)());return(0,r.Cl)((0,r.Cl)({},l),{messageId:d})},t}(),E=n(4702),T=n(5307),I=function(t){return"object"==typeof t&&null!==t&&"then"in t&&"function"==typeof t.then},R=n(4347),O=function(){return"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n.g?n.g:null},M=null!==(m=(g=O()).__SEGMENT_INSPECTOR__)&&void 0!==m?m:g.__SEGMENT_INSPECTOR__={},A=function(t){function e(e){var n,r,i,o=t.call(this)||this;return o.criticalTasks=(i=0,{done:function(){return n},run:function(t){var e=t();return I(e)&&(1==++i&&(n=new Promise((function(t){return r=t}))),e.finally((function(){return 0==--i&&r()}))),e}}),o.plugins=[],o.failedInitializations=[],o.flushing=!1,o.queue=null!=e?e:new T.x(4,"event-queue"),o.queue.on(E.g,(function(){o.scheduleFlush(0)})),o}return(0,r.C6)(e,t),e.prototype.register=function(t,e,n){return(0,r.sH)(this,void 0,void 0,(function(){var i=this;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return[4,Promise.resolve(e.load(t,n)).then((function(){i.plugins.push(e)})).catch((function(n){if("destination"===e.type)return i.failedInitializations.push(e.name),console.warn(e.name,n),void t.log("warn","Failed to load destination",{plugin:e.name,error:n});throw n}))];case 1:return r.sent(),[2]}}))}))},e.prototype.deregister=function(t,e,n){return(0,r.sH)(this,void 0,void 0,(function(){var i;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return r.trys.push([0,3,,4]),e.unload?[4,Promise.resolve(e.unload(t,n))]:[3,2];case 1:r.sent(),r.label=2;case 2:return this.plugins=this.plugins.filter((function(t){return t.name!==e.name})),[3,4];case 3:return i=r.sent(),t.log("warn","Failed to unload destination",{plugin:e.name,error:i}),[3,4];case 4:return[2]}}))}))},e.prototype.dispatch=function(t){return(0,r.sH)(this,void 0,void 0,(function(){var e;return(0,r.YH)(this,(function(n){return t.log("debug","Dispatching"),t.stats.increment("message_dispatched"),this.queue.push(t),e=this.subscribeToDelivery(t),this.scheduleFlush(0),[2,e]}))}))},e.prototype.subscribeToDelivery=function(t){return(0,r.sH)(this,void 0,void 0,(function(){var e=this;return(0,r.YH)(this,(function(n){return[2,new Promise((function(n){var r=function(i,o){i.isSame(t)&&(e.off("flush",r),n(i))};e.on("flush",r)}))]}))}))},e.prototype.dispatchSingle=function(t){return(0,r.sH)(this,void 0,void 0,(function(){var e=this;return(0,r.YH)(this,(function(n){return t.log("debug","Dispatching"),t.stats.increment("message_dispatched"),this.queue.updateAttempts(t),t.attempts=1,[2,this.deliver(t).catch((function(n){return n instanceof _.d&&!1===n.retry?(t.setFailedDelivery({reason:n}),t):e.enqueuRetry(n,t)?e.subscribeToDelivery(t):(t.setFailedDelivery({reason:n}),t)}))]}))}))},e.prototype.isEmpty=function(){return 0===this.queue.length},e.prototype.scheduleFlush=function(t){var e=this;void 0===t&&(t=500),this.flushing||(this.flushing=!0,setTimeout((function(){e.flush().then((function(){setTimeout((function(){e.flushing=!1,e.queue.length&&e.scheduleFlush(0)}),0)}))}),t))},e.prototype.deliver=function(t){return(0,r.sH)(this,void 0,void 0,(function(){var e,n,i;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return[4,this.criticalTasks.done()];case 1:r.sent(),e=Date.now(),r.label=2;case 2:return r.trys.push([2,4,,5]),[4,this.flushOne(t)];case 3:return t=r.sent(),n=Date.now()-e,t.stats.gauge("delivered",n),t.log("debug","Delivered",t.event),[2,t];case 4:throw i=r.sent(),t.log("error","Failed to deliver",i),t.stats.increment("delivery_failed"),i;case 5:return[2]}}))}))},e.prototype.enqueuRetry=function(t,e){return!(t instanceof _.d&&!1===t.retry)&&this.queue.pushWithBackoff(e)},e.prototype.flush=function(){return(0,r.sH)(this,void 0,void 0,(function(){var t,e;return(0,r.YH)(this,(function(n){switch(n.label){case 0:if(0===this.queue.length||!(0,v.s)())return[2,[]];if(!(t=this.queue.pop()))return[2,[]];t.attempts=this.queue.getAttempts(t),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.deliver(t)];case 2:return t=n.sent(),this.emit("flush",t,!0),[3,4];case 3:return e=n.sent(),this.enqueuRetry(e,t)||(t.setFailedDelivery({reason:e}),this.emit("flush",t,!1)),[2,[]];case 4:return[2,[t]]}}))}))},e.prototype.isReady=function(){return!0},e.prototype.availableExtensions=function(t){var e,n,i=this.plugins.filter((function(e){var n,r,i;if("destination"!==e.type&&"Segment.io"!==e.name)return!0;var o=void 0;return null===(n=e.alternativeNames)||void 0===n||n.forEach((function(e){void 0!==t[e]&&(o=t[e])})),null!==(i=null!==(r=t[e.name])&&void 0!==r?r:o)&&void 0!==i?i:!1!==("Segment.io"===e.name||t.All)})),o=(e="type",n={},i.forEach((function(t){var i,o=void 0,s=t[e];void 0!==(o="string"!=typeof s?JSON.stringify(s):s)&&(n[o]=(0,r.fX)((0,r.fX)([],null!==(i=n[o])&&void 0!==i?i:[],!0),[t],!1))})),n),s=o.before,a=void 0===s?[]:s,c=o.enrichment,u=void 0===c?[]:c,l=o.destination,d=void 0===l?[]:l,p=o.after;return{before:a,enrichment:u,destinations:d,after:void 0===p?[]:p}},e.prototype.flushOne=function(t){var e,n,i,o;return(0,r.sH)(this,void 0,void 0,(function(){var s,a,c,u,l,d,p,h,f,m,g,y,v,b;return(0,r.YH)(this,(function(r){switch(r.label){case 0:if(!this.isReady())throw new Error("Not ready");s=this.availableExtensions(null!==(e=t.event.integrations)&&void 0!==e?e:{}),a=s.before,c=s.enrichment,u=0,l=a,r.label=1;case 1:return u<l.length?(d=l[u],[4,(0,R.D)(t,d)]):[3,4];case 2:(m=r.sent())instanceof _.o&&(t=m),r.label=3;case 3:return u++,[3,1];case 4:p=0,h=c,r.label=5;case 5:return p<h.length?(f=h[p],[4,(0,R.C)(t,f)]):[3,8];case 6:(m=r.sent())instanceof _.o&&(t=m),r.label=7;case 7:return p++,[3,5];case 8:return null===(n=M.enriched)||void 0===n||n.call(M,t),g=this.availableExtensions(null!==(i=t.event.integrations)&&void 0!==i?i:{}),y=g.destinations,v=g.after,[4,new Promise((function(e,n){setTimeout((function(){var r=y.map((function(e){return(0,R.C)(t,e)}));Promise.all(r).then(e).catch(n)}),0)}))];case 9:return r.sent(),t.stats.increment("message_delivered"),null===(o=M.delivered)||void 0===o||o.call(M,t,["segment.io"]),b=v.map((function(e){return(0,R.C)(t,e)})),[4,Promise.all(b)];case 10:return r.sent(),[2,t]}}))}))},e}(b.v),D=n(1558),N=n(3587);function L(t){for(var e=t.constructor.prototype,n=0,r=Object.getOwnPropertyNames(e);n<r.length;n++){var i=r[n];if("constructor"!==i){var o=Object.getOwnPropertyDescriptor(t.constructor.prototype,i);o&&"function"==typeof o.value&&(t[i]=t[i].bind(t))}}return t}var P={persist:!0,cookie:{key:"ajs_user_id",oldKey:"ajs_user"},localStorage:{key:"ajs_user_traits"}},F=function(){function t(){this.cache={}}return t.prototype.get=function(t){return this.cache[t]},t.prototype.set=function(t,e){return this.cache[t]=e,e},t.prototype.remove=function(t){delete this.cache[t]},t}(),j=function(t){function e(n){void 0===n&&(n=e.defaults);var i=t.call(this)||this;return i.options=(0,r.Cl)((0,r.Cl)({},e.defaults),n),i}return(0,r.C6)(e,t),e.available=function(){var t=window.navigator.cookieEnabled;return t||(D.A.set("ajs:cookies","test"),t=document.cookie.includes("ajs:cookies"),D.A.remove("ajs:cookies")),t},Object.defineProperty(e,"defaults",{get:function(){return{maxage:365,domain:(0,N.f)(window.location.href),path:"/",sameSite:"Lax"}},enumerable:!1,configurable:!0}),e.prototype.opts=function(){return{sameSite:this.options.sameSite,expires:this.options.maxage,domain:this.options.domain,path:this.options.path,secure:this.options.secure}},e.prototype.get=function(t){try{var e=D.A.get(t);if(!e)return null;try{return JSON.parse(e)}catch(t){return e}}catch(t){return null}},e.prototype.set=function(t,e){return"string"==typeof e?D.A.set(t,e,this.opts()):null===e?D.A.remove(t,this.opts()):D.A.set(t,JSON.stringify(e),this.opts()),e},e.prototype.remove=function(t){return D.A.remove(t,this.opts())},e}(F),$=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.get=function(t){return null},e.set=function(t,e){return null},e.remove=function(t){},e}return(0,r.C6)(e,t),e}(F),H=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.C6)(e,t),e.available=function(){var t="test";try{return localStorage.setItem(t,t),localStorage.removeItem(t),!0}catch(t){return!1}},e.prototype.get=function(t){var e=localStorage.getItem(t);if(e)try{return JSON.parse(e)}catch(t){return JSON.parse(JSON.stringify(e))}return null},e.prototype.set=function(t,e){try{localStorage.setItem(t,JSON.stringify(e))}catch(e){console.warn("Unable to set ".concat(t," in localStorage, storage may be full."))}return e},e.prototype.remove=function(t){return localStorage.removeItem(t)},e}(F),B=function(){function t(t,e){void 0===t&&(t=P);var n,r,i,o,s=this;this.options={},this.id=function(t){var e,n;if(s.options.disable)return null;var r=s.chainGet(s.idKey);return void 0!==t&&(s.trySet(s.idKey,t),t!==r&&null!==r&&null!==t&&s.anonymousId(null)),null!==(n=null!==(e=s.chainGet(s.idKey))&&void 0!==e?e:s.cookies.get(P.cookie.oldKey))&&void 0!==n?n:null},this.anonymousId=function(t){var e,n;if(s.options.disable)return null;if(void 0===t){var r=null!==(e=s.chainGet(s.anonKey))&&void 0!==e?e:null===(n=s.legacySIO())||void 0===n?void 0:n[0];if(r)return r}return null===t?(s.trySet(s.anonKey,null),s.chainGet(s.anonKey)):(s.trySet(s.anonKey,null!=t?t:(0,S.v4)()),s.chainGet(s.anonKey))},this.traits=function(t){var e,n;if(!s.options.disable)return null===t&&(t={}),t&&(s.mem.set(s.traitsKey,null!=t?t:{}),s.localStorage.set(s.traitsKey,null!=t?t:{})),null!==(n=null!==(e=s.localStorage.get(s.traitsKey))&&void 0!==e?e:s.mem.get(s.traitsKey))&&void 0!==n?n:{}},this.options=t,this.cookieOptions=e,this.idKey=null!==(r=null===(n=t.cookie)||void 0===n?void 0:n.key)&&void 0!==r?r:P.cookie.key,this.traitsKey=null!==(o=null===(i=t.localStorage)||void 0===i?void 0:i.key)&&void 0!==o?o:P.localStorage.key,this.anonKey="ajs_anonymous_id";var a=!0===t.disable,c=!1!==t.persist;this.localStorage=a||t.localStorageFallbackDisabled||!c||!H.available()?new $:new H,this.cookies=!a&&c&&j.available()?new j(e):new $,this.mem=a?new $:new F;var u=this.cookies.get(P.cookie.oldKey);u&&(u.id&&this.id(u.id),u.traits&&this.traits(u.traits)),L(this)}return t.prototype.chainGet=function(t){var e,n,r,i=null!==(r=null!==(n=null!==(e=this.localStorage.get(t))&&void 0!==e?e:this.cookies.get(t))&&void 0!==n?n:this.mem.get(t))&&void 0!==r?r:null;return this.trySet(t,"number"==typeof i?i.toString():i)},t.prototype.trySet=function(t,e){return this.localStorage.set(t,e),this.cookies.set(t,e),this.mem.set(t,e),e},t.prototype.chainClear=function(t){this.localStorage.remove(t),this.cookies.remove(t),this.mem.remove(t)},t.prototype.legacySIO=function(){var t=this.cookies.get("_sio");if(!t)return null;var e=t.split("----");return[e[0],e[1]]},t.prototype.identify=function(t,e){if(!this.options.disable){e=null!=e?e:{};var n=this.id();null!==n&&n!==t||(e=(0,r.Cl)((0,r.Cl)({},this.traits()),e)),t&&this.id(t),this.traits(e)}},t.prototype.logout=function(){this.anonymousId(null),this.id(null),this.traits({})},t.prototype.reset=function(){this.logout(),this.chainClear(this.idKey),this.chainClear(this.anonKey),this.chainClear(this.traitsKey)},t.prototype.load=function(){return new t(this.options,this.cookieOptions)},t.prototype.save=function(){return!0},t.defaults=P,t}(),U={persist:!0,cookie:{key:"ajs_group_id"},localStorage:{key:"ajs_group_properties"}},q=function(t){function e(e,n){void 0===e&&(e=U);var r=t.call(this,e,n)||this;return r.anonymousId=function(t){},L(r),r}return(0,r.C6)(e,t),e}(B),z=n(3605),W="This is being deprecated and will be not be available in future releases of Analytics JS",Y=O(),G=null==Y?void 0:Y.analytics;function J(){console.warn(W)}var V=function(t){function e(e,n,i,o,s){var a,c,u,l=this;(l=t.call(this)||this)._debug=!1,l.initialized=!1,l.user=function(){return l._user},l.init=l.initialize.bind(l),l.log=J,l.addIntegrationMiddleware=J,l.listeners=J,l.addEventListener=J,l.removeAllListeners=J,l.removeListener=J,l.removeEventListener=J,l.hasListeners=J,l.add=J,l.addIntegration=J;var d=null==n?void 0:n.cookie,p=null!==(a=null==n?void 0:n.disableClientPersistence)&&void 0!==a&&a;return l.settings=e,l.settings.timeout=null!==(c=l.settings.timeout)&&void 0!==c?c:300,l.queue=null!=i?i:function(t,e){void 0===t&&(t=!1),void 0===e&&(e=!1);var n=t?4:1,r=e?new E.M(n,[]):new T.x(n,"event-queue");return new A(r)}(null==n?void 0:n.retryQueue,p),l._user=null!=o?o:new B(p?(0,r.Cl)((0,r.Cl)({},null==n?void 0:n.user),{persist:!1}):null==n?void 0:n.user,d).load(),l._group=null!=s?s:new q(p?(0,r.Cl)((0,r.Cl)({},null==n?void 0:n.group),{persist:!1}):null==n?void 0:n.group,d).load(),l.eventFactory=new C(l._user),l.integrations=null!==(u=null==n?void 0:n.integrations)&&void 0!==u?u:{},l.options=null!=n?n:{},L(l),l}return(0,r.C6)(e,t),e.prototype.track=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,n,i,o,s,a,c=this;return(0,r.YH)(this,(function(r){return e=d.apply(void 0,t),n=e[0],i=e[1],o=e[2],s=e[3],a=this.eventFactory.track(n,i,o,this.integrations),[2,this.dispatch(a,s).then((function(t){return c.emit("track",n,t.event.properties,t.event.options),t}))]}))}))},e.prototype.page=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,n,i,o,s,a,c,u=this;return(0,r.YH)(this,(function(r){return e=p.apply(void 0,t),n=e[0],i=e[1],o=e[2],s=e[3],a=e[4],c=this.eventFactory.page(n,i,o,s,this.integrations),[2,this.dispatch(c,a).then((function(t){return u.emit("page",n,i,t.event.properties,t.event.options),t}))]}))}))},e.prototype.identify=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,n,i,o,s,a,c=this;return(0,r.YH)(this,(function(r){return e=h(this._user).apply(void 0,t),n=e[0],i=e[1],o=e[2],s=e[3],this._user.identify(n,i),a=this.eventFactory.identify(this._user.id(),this._user.traits(),o,this.integrations),[2,this.dispatch(a,s).then((function(t){return c.emit("identify",t.event.userId,t.event.traits,t.event.options),t}))]}))}))},e.prototype.group=function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(0===e.length)return this._group;var r=h(this._group).apply(void 0,e),i=r[0],o=r[1],s=r[2],a=r[3];this._group.identify(i,o);var c=this._group.id(),u=this._group.traits(),l=this.eventFactory.group(c,u,s,this.integrations);return this.dispatch(l,a).then((function(e){return t.emit("group",e.event.groupId,e.event.traits,e.event.options),e}))},e.prototype.alias=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,n,i,o,s,a,c=this;return(0,r.YH)(this,(function(r){return e=f.apply(void 0,t),n=e[0],i=e[1],o=e[2],s=e[3],a=this.eventFactory.alias(n,i,o,this.integrations),[2,this.dispatch(a,s).then((function(t){return c.emit("alias",n,i,t.event.options),t}))]}))}))},e.prototype.screen=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,n,i,o,s,a,c,u=this;return(0,r.YH)(this,(function(r){return e=p.apply(void 0,t),n=e[0],i=e[1],o=e[2],s=e[3],a=e[4],c=this.eventFactory.screen(n,i,o,s,this.integrations),[2,this.dispatch(c,a).then((function(t){return u.emit("screen",n,i,t.event.properties,t.event.options),t}))]}))}))},e.prototype.trackClick=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,i;return(0,r.YH)(this,(function(o){switch(o.label){case 0:return[4,n.e(248).then(n.bind(n,6130))];case 1:return e=o.sent(),[2,(i=e.link).call.apply(i,(0,r.fX)([this],t,!1))]}}))}))},e.prototype.trackLink=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,i;return(0,r.YH)(this,(function(o){switch(o.label){case 0:return[4,n.e(248).then(n.bind(n,6130))];case 1:return e=o.sent(),[2,(i=e.link).call.apply(i,(0,r.fX)([this],t,!1))]}}))}))},e.prototype.trackSubmit=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,i;return(0,r.YH)(this,(function(o){switch(o.label){case 0:return[4,n.e(248).then(n.bind(n,6130))];case 1:return e=o.sent(),[2,(i=e.form).call.apply(i,(0,r.fX)([this],t,!1))]}}))}))},e.prototype.trackForm=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,i;return(0,r.YH)(this,(function(o){switch(o.label){case 0:return[4,n.e(248).then(n.bind(n,6130))];case 1:return e=o.sent(),[2,(i=e.form).call.apply(i,(0,r.fX)([this],t,!1))]}}))}))},e.prototype.register=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,n,i=this;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return e=_.o.system(),n=t.map((function(t){return i.queue.register(e,t,i)})),[4,Promise.all(n)];case 1:return r.sent(),[2,e]}}))}))},e.prototype.deregister=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,r.sH)(this,void 0,void 0,(function(){var e,n,i=this;return(0,r.YH)(this,(function(o){switch(o.label){case 0:return e=_.o.system(),n=t.map((function(t){return(0,r.sH)(i,void 0,void 0,(function(){var n;return(0,r.YH)(this,(function(r){return(n=this.queue.plugins.find((function(e){return e.name===t})))?[2,this.queue.deregister(e,n,this)]:(e.log("warn","plugin ".concat(t," not found")),[2])}))}))})),[4,Promise.all(n)];case 1:return o.sent(),[2,e]}}))}))},e.prototype.debug=function(t){return!1===t&&localStorage.getItem("debug")&&localStorage.removeItem("debug"),this._debug=t,this},e.prototype.reset=function(){this._user.reset(),this._group.reset()},e.prototype.timeout=function(t){this.settings.timeout=t},e.prototype.dispatch=function(t,e){var n;return(0,r.sH)(this,void 0,void 0,(function(){var i,o,s,a,c;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return i=new _.o(t),null===(n=M.triggered)||void 0===n||n.call(M,i),(0,v.a)()&&!this.options.retryQueue?[2,i]:(o=Date.now(),this.queue.isEmpty()?[4,this.queue.dispatchSingle(i)]:[3,2]);case 1:return s=r.sent(),[3,4];case 2:return[4,this.queue.dispatch(i)];case 3:s=r.sent(),r.label=4;case 4:return a=Date.now()-o,c=this.settings.timeout,e?[4,(0,y.w)(s,e,Math.max((null!=c?c:300)-a,0),c)]:[3,6];case 5:s=r.sent(),r.label=6;case 6:return this._debug&&s.flush(),[2,s]}}))}))},e.prototype.addSourceMiddleware=function(t){return(0,r.sH)(this,void 0,void 0,(function(){var e=this;return(0,r.YH)(this,(function(i){switch(i.label){case 0:return[4,this.queue.criticalTasks.run((function(){return(0,r.sH)(e,void 0,void 0,(function(){var e,i,o;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return[4,Promise.resolve().then(n.bind(n,1986))];case 1:return e=r.sent().sourceMiddlewarePlugin,i={},this.queue.plugins.forEach((function(t){if("destination"===t.type)return i[t.name]=!0})),o=e(t,i),[4,this.register(o)];case 2:return r.sent(),[2]}}))}))}))];case 1:return i.sent(),[2,this]}}))}))},e.prototype.addDestinationMiddleware=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return this.queue.plugins.filter((function(e){return e.name.toLowerCase()===t.toLowerCase()})).forEach((function(t){t.addMiddleware.apply(t,e)})),Promise.resolve(this)},e.prototype.setAnonymousId=function(t){return this._user.anonymousId(t)},e.prototype.queryString=function(t){return(0,r.sH)(this,void 0,void 0,(function(){return(0,r.YH)(this,(function(e){switch(e.label){case 0:return[4,n.e(538).then(n.bind(n,1066))];case 1:return[2,(0,e.sent().queryString)(this,t)]}}))}))},e.prototype.use=function(t){return t(this),this},e.prototype.ready=function(t){return void 0===t&&(t=function(t){return t}),(0,r.sH)(this,void 0,void 0,(function(){return(0,r.YH)(this,(function(e){return[2,Promise.all(this.queue.plugins.map((function(t){return t.ready?t.ready():Promise.resolve()}))).then((function(e){return t(e),e}))]}))}))},e.prototype.noConflict=function(){return console.warn(W),window.analytics=null!=G?G:this,this},e.prototype.normalize=function(t){return console.warn(W),this.eventFactory.normalize(t)},Object.defineProperty(e.prototype,"failedInitializations",{get:function(){return console.warn(W),this.queue.failedInitializations},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"VERSION",{get:function(){return z.r},enumerable:!1,configurable:!0}),e.prototype.initialize=function(t,e){return(0,r.sH)(this,void 0,void 0,(function(){return(0,r.YH)(this,(function(t){return console.warn(W),[2,Promise.resolve(this)]}))}))},e.prototype.pageview=function(t){return(0,r.sH)(this,void 0,void 0,(function(){return(0,r.YH)(this,(function(e){switch(e.label){case 0:return console.warn(W),[4,this.page({path:t})];case 1:return e.sent(),[2,this]}}))}))},Object.defineProperty(e.prototype,"plugins",{get:function(){var t;return console.warn(W),null!==(t=this._plugins)&&void 0!==t?t:{}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"Integrations",{get:function(){return console.warn(W),this.queue.plugins.filter((function(t){return"destination"===t.type})).reduce((function(t,e){var n="".concat(e.name.toLowerCase().replace(".","").split(" ").join("-"),"Integration"),r=window[n];if(!r)return t;var i=r.Integration;return i?(t[e.name]=i,t):(t[e.name]=r,t)}),{})},enumerable:!1,configurable:!0}),e.prototype.push=function(t){var e=t.shift();e&&!this[e]||this[e].apply(this,t)},e}(b.v);function K(){return"undefined"!=typeof process&&process.env?process.env:{}}var X=n(5634),Q=n(3866),Z=n(8596);function tt(){var t=document.getElementsByTagName("link"),e="";return Array.prototype.slice.call(t).forEach((function(t){"canonical"===t.getAttribute("rel")&&(e=t.getAttribute("href"))})),e}function et(){var t=tt();if(!t)return window.location.pathname;var e=document.createElement("a");return e.href=t,e.pathname.startsWith("/")?e.pathname:"/"+e.pathname}function nt(t){void 0===t&&(t="");var e=tt();if(e)return e.includes("?")?e:"".concat(e).concat(t);var n=window.location.href,r=n.indexOf("#");return-1===r?n:n.slice(0,r)}function rt(){return{path:et(),referrer:document.referrer,search:location.search,title:document.title,url:nt(location.search)}}function it(t){var e,n=t.event;n.context=n.context||{};var r=rt(),i=null!==(e=n.properties)&&void 0!==e?e:{};return Object.keys(r).forEach((function(t){i[t]&&(r[t]=i[t])})),n.context.page&&(r=Object.assign({},r,n.context.page)),n.context=Object.assign({},n.context,{page:r}),t.event=n,t}var ot={name:"Page Enrichment",version:"0.1.0",isLoaded:function(){return!0},load:function(){return Promise.resolve()},type:"before",page:function(t){return t.event.properties=Object.assign({},rt(),t.event.properties),t.event.name&&(t.event.properties.name=t.event.name),it(t)},alias:it,track:it,identify:it,group:it},st=n(9663),at=n(1679),ct=n(1986),ut=function(){function t(t,e){this.version="1.0.0",this.alternativeNames=[],this.middleware=[],this.alias=this._createMethod("alias"),this.group=this._createMethod("group"),this.identify=this._createMethod("identify"),this.page=this._createMethod("page"),this.screen=this._createMethod("screen"),this.track=this._createMethod("track"),this.action=e,this.name=t,this.type=e.type,this.alternativeNames.push(e.name)}return t.prototype.addMiddleware=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];(t=this.middleware).push.apply(t,e)},t.prototype.transform=function(t){return(0,r.sH)(this,void 0,void 0,(function(){var e;return(0,r.YH)(this,(function(n){switch(n.label){case 0:return[4,(0,ct.applyDestinationMiddleware)(this.name,t.event,this.middleware)];case 1:return null===(e=n.sent())&&t.cancel(new _.d({retry:!1,reason:"dropped by destination middleware"})),[2,new _.o(e)]}}))}))},t.prototype._createMethod=function(t){var e=this;return function(n){return(0,r.sH)(e,void 0,void 0,(function(){var e;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return this.action[t]?[4,this.transform(n)]:[2,n];case 1:return e=r.sent(),[4,this.action[t](e)];case 2:return r.sent(),[2,n]}}))}))}},t.prototype.isLoaded=function(){return this.action.isLoaded()},t.prototype.ready=function(){return this.action.ready?this.action.ready():Promise.resolve()},t.prototype.load=function(t,e){return this.action.load(t,e)},t.prototype.unload=function(t,e){var n,r;return null===(r=(n=this.action).unload)||void 0===r?void 0:r.call(n,t,e)},t}();function lt(t,e,n,i,o){var s,a,c;return(0,r.sH)(this,void 0,void 0,(function(){var u,l,d,p,h=this;return(0,r.YH)(this,(function(f){switch(f.label){case 0:return u=[],l=(0,X.I2)(),d=null!==(a=null===(s=t.middlewareSettings)||void 0===s?void 0:s.routingRules)&&void 0!==a?a:[],p=(null!==(c=t.remotePlugins)&&void 0!==c?c:[]).map((function(t){return(0,r.sH)(h,void 0,void 0,(function(){var s,a,c,p,h,f,m,g,y;return(0,r.YH)(this,(function(v){switch(v.label){case 0:if(!1===e.All&&!e[t.name]||!1===e[t.name])return[2];v.label=1;case 1:if(v.trys.push([1,12,,13]),!i)return[3,7];s=t.url.split("/"),a=s[s.length-2],c=t.url.replace(a,btoa(a).replace(/=/g,"")),v.label=2;case 2:return v.trys.push([2,4,,6]),[4,(0,at.k)(c.replace("https://cdn.segment.com",l))];case 3:return v.sent(),[3,6];case 4:return v.sent(),[4,(0,at.k)(t.url.replace("https://cdn.segment.com",l))];case 5:return v.sent(),[3,6];case 6:return[3,9];case 7:return[4,(0,at.k)(t.url.replace("https://cdn.segment.com",l))];case 8:v.sent(),v.label=9;case 9:return p=t.libraryName,"function"!=typeof window[p]?[3,11]:(h=window[p],[4,(0,st.m)(h((0,r.Cl)((0,r.Cl)({},t.settings),n[t.name])))]);case 10:f=v.sent(),function(t){if(!Array.isArray(t))throw new Error("Not a valid list of plugins");var e=["load","isLoaded","name","version","type"];t.forEach((function(t){e.forEach((function(e){var n;if(void 0===t[e])throw new Error("Plugin: ".concat(null!==(n=t.name)&&void 0!==n?n:"unknown"," missing required function ").concat(e))}))}))}(m=Array.isArray(f)?f:[f]),g=d.filter((function(e){return e.destinationName===t.creationName})),m.forEach((function(e){var n=new ut(t.creationName,e);g.length&&o&&"destination"===e.type&&n.addMiddleware(o),u.push(n)})),v.label=11;case 11:return[3,13];case 12:return y=v.sent(),console.warn("Failed to load Remote Plugin",y),[3,13];case 13:return[2]}}))}))})),[4,Promise.all(p)];case 1:return f.sent(),[2,u.filter(Boolean)]}}))}))}var dt=n(8781),pt=function(t){var e=!1;document.addEventListener("pagehide",(function(){e||(e=!0,t())})),document.addEventListener("visibilitychange",(function(){if("hidden"==document.visibilityState){if(e)return;e=!0,t()}else e=!1}))},ht=Q.A;"undefined"!=typeof window&&(ht=window.fetch||Q.A);var ft=500;function mt(t){return(encodeURI(JSON.stringify(t)).split(/%..|./).length-1)/1024}var gt=Q.A;"undefined"!=typeof window&&(gt=window.fetch||Q.A);var yt=n(6205),vt=n(6315);function _t(t,e){return(0,r.sH)(this,void 0,void 0,(function(){var n,i=this;return(0,r.YH)(this,(function(o){switch(o.label){case 0:return n=[],(0,v.a)()?[2,e]:[4,(0,vt._)((function(){return e.length>0&&!(0,v.a)()}),(function(){return(0,r.sH)(i,void 0,void 0,(function(){var i;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return(i=e.pop())?[4,(0,R.C)(i,t)]:[2];case 1:return r.sent()instanceof _.o||n.push(i),[2]}}))}))}))];case 1:return o.sent(),n.map((function(t){return e.pushWithBackoff(t)})),[2,e]}}))}))}function bt(t,e,n,i){var o=this;t||setTimeout((function(){return(0,r.sH)(o,void 0,void 0,(function(){var t,o;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return t=!0,[4,_t(n,e)];case 1:return o=r.sent(),t=!1,e.todo>0&&i(t,o,n,i),[2]}}))}))}),5e3*Math.random())}function St(t,e,n){var i,o,s,a,c=t.options.disableClientPersistence?new E.M(t.queue.queue.maxAttempts,[]):new T.x(t.queue.queue.maxAttempts,"dest-Segment.io"),u=null!==(i=null==e?void 0:e.apiHost)&&void 0!==i?i:"api.segment.io/v1",l=null!==(o=null==e?void 0:e.protocol)&&void 0!==o?o:"https",d="".concat(l,"://").concat(u),p="batching"===(null===(s=null==e?void 0:e.deliveryStrategy)||void 0===s?void 0:s.strategy)?function(t,e){var n,i,o,s=[],a=!1,c=null!==(n=null==e?void 0:e.size)&&void 0!==n?n:10,u=null!==(i=null==e?void 0:e.timeout)&&void 0!==i?i:5e3;function l(e){var n;if(0!==e.length){var r=null===(n=e[0])||void 0===n?void 0:n.writeKey;return ht("https://".concat(t,"/b"),{keepalive:a,headers:{"Content-Type":"text/plain"},method:"post",body:JSON.stringify({batch:e,writeKey:r})})}}function d(){return(0,r.sH)(this,void 0,void 0,(function(){var t;return(0,r.YH)(this,(function(e){return s.length?(t=s,s=[],[2,l(t)]):[2]}))}))}return pt((function(){if(a=!0,s.length){var t=function(t){var e=[],n=0;return t.forEach((function(t){mt(e[n])>=64&&n++,e[n]?e[n].push(t):e[n]=[t]})),e}(s).map(l);Promise.all(t).catch(console.error)}})),{dispatch:function(t,e){return(0,r.sH)(this,void 0,void 0,(function(){var t;return(0,r.YH)(this,(function(n){return s.push(e),t=s.length>=c||function(t){return mt(t)>=ft-50}(s),[2,t||a?d():void(o||(o=setTimeout((function(){o=void 0,d().catch(console.error)}),u)))]}))}))}}}(u,null===(a=null==e?void 0:e.deliveryStrategy)||void 0===a?void 0:a.config):{dispatch:function(t,e){return gt(t,{headers:{"Content-Type":"text/plain"},method:"post",body:JSON.stringify(e)})}};function h(i){return(0,r.sH)(this,void 0,void 0,(function(){var o,s;return(0,r.YH)(this,(function(r){return(0,v.a)()?(c.push(i),bt(!1,c,f,bt),[2,i]):(o=i.event.type.charAt(0),s=(0,dt.W)(i.event).json(),"track"===i.event.type&&delete s.traits,"alias"===i.event.type&&(s=function(t,e){var n,r,i,o,s=t.user();return e.previousId=null!==(i=null!==(r=null!==(n=e.previousId)&&void 0!==n?n:e.from)&&void 0!==r?r:s.id())&&void 0!==i?i:s.anonymousId(),e.userId=null!==(o=e.userId)&&void 0!==o?o:e.to,delete e.from,delete e.to,e}(t,s)),[2,p.dispatch("".concat(d,"/").concat(o),(0,yt.S8)(t,s,e,n)).then((function(){return i})).catch((function(t){return"error"!==t.type&&"Failed to fetch"!==t.message||(c.push(i),bt(!1,c,f,bt)),i}))])}))}))}var f={name:"Segment.io",type:"after",version:"0.1.0",isLoaded:function(){return!0},load:function(){return Promise.resolve()},track:h,identify:h,page:h,alias:h,group:h};return f}var wt=function(t,e,n){n.getCalls(t).forEach((function(t){It(e,t).catch(console.error)}))},kt=function(t,e){return(0,r.sH)(void 0,void 0,void 0,(function(){var n,i,o;return(0,r.YH)(this,(function(r){switch(r.label){case 0:n=0,i=e.getCalls("addSourceMiddleware"),r.label=1;case 1:return n<i.length?(o=i[n],[4,It(t,o).catch(console.error)]):[3,4];case 2:r.sent(),r.label=3;case 3:return n++,[3,1];case 4:return[2]}}))}))},xt=wt.bind(void 0,"on"),Ct=wt.bind(void 0,"setAnonymousId"),Et=function(t,e){e.toArray().forEach((function(e){setTimeout((function(){It(t,e).catch(console.error)}),0)}))},Tt=function(){function t(){this._value={}}return t.prototype.toArray=function(){var t;return(t=[]).concat.apply(t,Object.values(this._value))},t.prototype.getCalls=function(t){var e;return null!==(e=this._value[t])&&void 0!==e?e:[]},t.prototype.push=function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return e.forEach((function(e){t._value[e.method]?t._value[e.method].push(e):t._value[e.method]=[e]})),this},t.prototype.clear=function(){return this._value={},this},t}();function It(t,e){return(0,r.sH)(this,void 0,void 0,(function(){var n,i;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return r.trys.push([0,3,,4]),e.called?[2,void 0]:(e.called=!0,n=t[e.method].apply(t,e.args),I(n)?[4,n]:[3,2]);case 1:r.sent(),r.label=2;case 2:return e.resolve(n),[3,4];case 3:return i=r.sent(),e.reject(i),[3,4];case 4:return[2]}}))}))}var Rt=function(){function t(t){var e=this;this._preInitBuffer=new Tt,this.trackSubmit=this._createMethod("trackSubmit"),this.trackClick=this._createMethod("trackClick"),this.trackLink=this._createMethod("trackLink"),this.pageView=this._createMethod("pageview"),this.identify=this._createMethod("identify"),this.reset=this._createMethod("reset"),this.group=this._createMethod("group"),this.track=this._createMethod("track"),this.ready=this._createMethod("ready"),this.alias=this._createMethod("alias"),this.debug=this._createChainableMethod("debug"),this.page=this._createMethod("page"),this.once=this._createChainableMethod("once"),this.off=this._createChainableMethod("off"),this.on=this._createChainableMethod("on"),this.addSourceMiddleware=this._createMethod("addSourceMiddleware"),this.setAnonymousId=this._createMethod("setAnonymousId"),this.addDestinationMiddleware=this._createMethod("addDestinationMiddleware"),this.screen=this._createMethod("screen"),this.register=this._createMethod("register"),this.deregister=this._createMethod("deregister"),this.user=this._createMethod("user"),this.VERSION=z.r,this._promise=t(this._preInitBuffer),this._promise.then((function(t){var n=t[0],r=t[1];e.instance=n,e.ctx=r})).catch((function(){}))}return t.prototype.then=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this._promise).then.apply(t,e)},t.prototype.catch=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this._promise).catch.apply(t,e)},t.prototype.finally=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this._promise).finally.apply(t,e)},t.prototype._createMethod=function(t){var e=this;return function(){for(var n,r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];if(e.instance){var o=(n=e.instance)[t].apply(n,r);return Promise.resolve(o)}return new Promise((function(n,i){e._preInitBuffer.push({method:t,args:r,resolve:n,reject:i,called:!1})}))}},t.prototype._createChainableMethod=function(t){var e=this;return function(){for(var n,r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];return e.instance?((n=e.instance)[t].apply(n,r),e):(e._preInitBuffer.push({method:t,args:r,resolve:function(){},reject:console.error,called:!1}),e)}},t}();function Ot(t){var e=t[0],n=t.slice(1);return{method:e,resolve:function(){},reject:console.error,args:n,called:!1}}var Mt=function(){var t=window.analytics;return Array.isArray(t)?t.splice(0,t.length).map(Ot):[]};function At(t,e){var n=null!=e?e:(0,X.I2)();return(0,Q.A)("".concat(n,"/v1/projects/").concat(t,"/settings")).then((function(t){return t.ok?t.json():t.text().then((function(t){throw new Error(t)}))})).catch((function(t){throw console.error(t.message),t}))}function Dt(t,e){return(0,r.sH)(this,void 0,void 0,(function(){return(0,r.YH)(this,(function(n){switch(n.label){case 0:return e.push.apply(e,Mt()),[4,kt(t,e)];case 1:return n.sent(),e.push.apply(e,Mt()),Et(t,e),e.clear(),[2]}}))}))}function Nt(t,e,i,o,s){var a,c,u;return(0,r.sH)(this,void 0,void 0,(function(){var d,p,h,f,m,g,y,v,_,b,S=this;return(0,r.YH)(this,(function(w){switch(w.label){case 0:return function(t){var e,n,r;return"test"!==K().NODE_ENV&&(null!==(r=null===(n=null===(e=t.middlewareSettings)||void 0===e?void 0:e.routingRules)||void 0===n?void 0:n.length)&&void 0!==r?r:0)>0}(t)?[4,n.e(10).then(n.bind(n,5152)).then((function(e){return e.tsubMiddleware(t.middlewareSettings.routingRules)}))]:[3,2];case 1:return p=w.sent(),[3,3];case 2:p=void 0,w.label=3;case 3:return d=p,k=t,"test"!==K().NODE_ENV&&Object.keys(k.integrations).length>1?[4,n.e(50).then(n.bind(n,4805)).then((function(n){return n.ajsDestinations(t,e.integrations,i,d)}))]:[3,5];case 4:return f=w.sent(),[3,6];case 5:f=[],w.label=6;case 6:return h=f,t.legacyVideoPluginsEnabled?[4,n.e(694).then(n.bind(n,4081)).then((function(t){return t.loadLegacyVideoPlugins(e)}))]:[3,8];case 7:w.sent(),w.label=8;case 8:return(null===(a=i.plan)||void 0===a?void 0:a.track)?[4,n.e(104).then(n.bind(n,9563)).then((function(e){var n;return e.schemaFilter(null===(n=i.plan)||void 0===n?void 0:n.track,t)}))]:[3,10];case 9:return g=w.sent(),[3,11];case 10:g=void 0,w.label=11;case 11:return m=g,y=(0,Z.J)(t,o),[4,lt(t,e.integrations,y,o.obfuscate,d).catch((function(){return[]}))];case 12:return v=w.sent(),_=(0,r.fX)((0,r.fX)((0,r.fX)([l,ot],s,!0),h,!0),v,!0),m&&_.push(m),!1===(null===(c=i.integrations)||void 0===c?void 0:c.All)&&!i.integrations["Segment.io"]||i.integrations&&!1===i.integrations["Segment.io"]||_.push(St(e,y["Segment.io"],t.integrations)),[4,e.register.apply(e,_)];case 13:return b=w.sent(),Object.entries(null!==(u=t.enabledMiddleware)&&void 0!==u?u:{}).some((function(t){return t[1]}))?[4,n.e(521).then(n.bind(n,3938)).then((function(n){var i=n.remoteMiddlewares;return(0,r.sH)(S,void 0,void 0,(function(){var n,s;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return[4,i(b,t,o.obfuscate)];case 1:return n=r.sent(),s=n.map((function(t){return e.addSourceMiddleware(t)})),[2,Promise.all(s)]}}))}))}))]:[3,15];case 14:w.sent(),w.label=15;case 15:return[2,b]}var k}))}))}var Lt=function(t){function e(e){return t.call(this,e)||this}return(0,r.C6)(e,t),e.load=function(t,e){return void 0===e&&(e={}),new this((function(n){return function(t,e,n){var i,o,s,a,c,u;return void 0===e&&(e={}),(0,r.sH)(this,void 0,void 0,(function(){var l,d,p,h,f,m,g,y,v,b;return(0,r.YH)(this,(function(S){switch(S.label){case 0:return t.cdnURL&&(0,X.qQ)(t.cdnURL),null===(i=t.cdnSettings)||void 0===i?[3,1]:(d=i,[3,3]);case 1:return[4,At(t.writeKey,t.cdnURL)];case 2:d=S.sent(),S.label=3;case 3:return p=null===(s=null===(o=(l=d).integrations["Segment.io"])||void 0===o?void 0:o.retryQueue)||void 0===s||s,h=(0,r.Cl)({retryQueue:p},e),f=new V(t,h),m=null!==(a=t.plugins)&&void 0!==a?a:[],_.o.initMetrics(l.metrics),function(t,e){e.push.apply(e,Mt()),Ct(t,e),xt(t,e)}(f,n),[4,Nt(l,f,h,e,m)];case 4:return g=S.sent(),y=null!==(c=window.location.search)&&void 0!==c?c:"",v=null!==(u=window.location.hash)&&void 0!==u?u:"",(b=y.length?y:v.replace(/(?=#).*(?=\?)/,"")).includes("ajs_")?[4,f.queryString(b).catch(console.error)]:[3,6];case 5:S.sent(),S.label=6;case 6:return f.initialized=!0,f.emit("initialize",t,e),e.initialPageview&&f.page().catch(console.error),[4,Dt(f,n)];case 7:return S.sent(),[2,[f,g]]}}))}))}(t,e,n)}))},e.standalone=function(t,n){return e.load({writeKey:t},n).then((function(t){return t[0]}))},e}(Rt),Pt=function(){function t(){}return t.load=function(){return Promise.reject(new Error("AnalyticsNode is not available in browsers."))},t}()},9663:(t,e,n)=>{"use strict";function r(t){return Promise.resolve(t)}n.d(e,{m:()=>r})},1679:(t,e,n)=>{"use strict";function r(t){return Array.prototype.slice.call(window.document.querySelectorAll("script")).find((function(e){return e.src===t}))}function i(t,e){var n=r(t);if(void 0!==n){var i=null==n?void 0:n.getAttribute("status");if("loaded"===i)return Promise.resolve(n);if("loading"===i)return new Promise((function(t,e){n.addEventListener("load",(function(){return t(n)})),n.addEventListener("error",(function(t){return e(t)}))}))}return new Promise((function(n,r){var i,o=window.document.createElement("script");o.type="text/javascript",o.src=t,o.async=!0,o.setAttribute("status","loading");for(var s=0,a=Object.entries(null!=e?e:{});s<a.length;s++){var c=a[s],u=c[0],l=c[1];o.setAttribute(u,l)}o.onload=function(){o.onerror=o.onload=null,o.setAttribute("status","loaded"),n(o)},o.onerror=function(){o.onerror=o.onload=null,o.setAttribute("status","error"),r(new Error("Failed to load ".concat(t)))};var d=window.document.getElementsByTagName("script")[0];null===(i=d.parentElement)||void 0===i||i.insertBefore(o,d)}))}function o(t){var e=r(t);return void 0!==e&&e.remove(),Promise.resolve()}n.d(e,{d:()=>o,k:()=>i})},8596:(t,e,n)=>{"use strict";n.d(e,{J:()=>i});var r=n(5450);function i(t,e){var n,i=Object.entries(null!==(n=e.integrations)&&void 0!==n?n:{}).reduce((function(t,e){var n,i,o=e[0],s=e[1];return"object"==typeof s?(0,r.Cl)((0,r.Cl)({},t),((n={})[o]=s,n)):(0,r.Cl)((0,r.Cl)({},t),((i={})[o]={},i))}),{});return Object.entries(t.integrations).reduce((function(t,e){var n,o=e[0],s=e[1];return(0,r.Cl)((0,r.Cl)({},t),((n={})[o]=(0,r.Cl)((0,r.Cl)({},s),i[o]),n))}),{})}},6315:(t,e,n)=>{"use strict";n.d(e,{_:()=>i});var r=n(5450),i=function(t,e){return(0,r.sH)(void 0,void 0,void 0,(function(){var n;return(0,r.YH)(this,(function(i){return n=function(i){return(0,r.sH)(void 0,void 0,void 0,(function(){var o;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return t(i)?(o=n,[4,e()]):[3,2];case 1:return[2,o.apply(void 0,[r.sent()])];case 2:return[2]}}))}))},[2,n(void 0)]}))}))}},5634:(t,e,n)=>{"use strict";n.d(e,{I2:()=>s,YM:()=>a,qQ:()=>o});var r,i=/(https:\/\/.*)\/analytics\.js\/v1\/(?:.*?)\/(?:platform|analytics.*)?/,o=function(t){window.analytics&&(window.analytics._cdn=t),r=t},s=function(){var t,e=null!=r?r:null===(t=window.analytics)||void 0===t?void 0:t._cdn;if(e)return e;var n,o=(Array.prototype.slice.call(document.querySelectorAll("script")).forEach((function(t){var e,r=null!==(e=t.getAttribute("src"))&&void 0!==e?e:"",o=i.exec(r);o&&o[1]&&(n=o[1])})),n);return o||"https://cdn.segment.com"},a=function(){var t=s();return"".concat(t,"/next-integrations")}},4702:(t,e,n)=>{"use strict";n.d(e,{g:()=>o,M:()=>s});var r=n(5450),i=n(162),o="onRemoveFromFuture",s=function(t){function e(e,n,r){var i=t.call(this)||this;return i.future=[],i.maxAttempts=e,i.queue=n,i.seen=null!=r?r:{},i}return(0,r.C6)(e,t),e.prototype.push=function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=e.map((function(e){return!(t.updateAttempts(e)>t.maxAttempts||t.includes(e)||(t.queue.push(e),0))}));return this.queue=this.queue.sort((function(e,n){return t.getAttempts(e)-t.getAttempts(n)})),r},e.prototype.pushWithBackoff=function(t){var e=this;if(0===this.getAttempts(t))return this.push(t)[0];var n=this.updateAttempts(t);if(n>this.maxAttempts||this.includes(t))return!1;var r=function(t){var e=Math.random()+1,n=t.minTimeout,r=void 0===n?500:n,i=t.factor,o=void 0===i?2:i,s=t.attempt,a=t.maxTimeout,c=void 0===a?1/0:a;return Math.min(e*r*Math.pow(o,s),c)}({attempt:n-1});return setTimeout((function(){e.queue.push(t),e.future=e.future.filter((function(e){return e.id!==t.id})),e.emit(o)}),r),this.future.push(t),!0},e.prototype.getAttempts=function(t){var e;return null!==(e=this.seen[t.id])&&void 0!==e?e:0},e.prototype.updateAttempts=function(t){return this.seen[t.id]=this.getAttempts(t)+1,this.getAttempts(t)},e.prototype.includes=function(t){return this.queue.includes(t)||this.future.includes(t)||Boolean(this.queue.find((function(e){return e.id===t.id})))||Boolean(this.future.find((function(e){return e.id===t.id})))},e.prototype.pop=function(){return this.queue.shift()},Object.defineProperty(e.prototype,"length",{get:function(){return this.queue.length},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"todo",{get:function(){return this.queue.length+this.future.length},enumerable:!1,configurable:!0}),e}(i.v)},5307:(t,e,n)=>{"use strict";n.d(e,{x:()=>p});var r=n(5450),i=n(4702),o=n(9320),s=n(1341),a={getItem:function(){},setItem:function(){},removeItem:function(){}};try{a=(0,s.B)()&&window.localStorage?window.localStorage:a}catch(t){console.warn("Unable to access localStorage",t)}function c(t){var e=a.getItem(t);return(e?JSON.parse(e):[]).map((function(t){return new o.o(t.event,t.id)}))}function u(t){var e=a.getItem(t);return e?JSON.parse(e):{}}function l(t){a.removeItem(t)}function d(t,e,n){void 0===n&&(n=0);var r="persisted-queue:v1:".concat(t,":lock"),i=a.getItem(r),o=i?JSON.parse(i):null,s=null===o||function(t){return(new Date).getTime()>t}(o);if(s)return a.setItem(r,JSON.stringify((new Date).getTime()+50)),e(),void a.removeItem(r);!s&&n<3?setTimeout((function(){d(t,e,n+1)}),50):console.error("Unable to retrieve lock")}var p=function(t){function e(e,n){var i=t.call(this,e,[])||this,o="persisted-queue:v1:".concat(n,":items"),s="persisted-queue:v1:".concat(n,":seen"),p=[],h={};return d(n,(function(){try{p=c(o),h=u(s),l(o),l(s),i.queue=(0,r.fX)((0,r.fX)([],p,!0),i.queue,!0),i.seen=(0,r.Cl)((0,r.Cl)({},h),i.seen)}catch(t){console.error(t)}})),document.addEventListener("pagehide",(function(){if(i.todo>0){var t=(0,r.fX)((0,r.fX)([],i.queue,!0),i.future,!0);try{d(n,(function(){!function(t,e){var n=c(t),i=(0,r.fX)((0,r.fX)([],e,!0),n,!0).reduce((function(t,e){var n;return(0,r.Cl)((0,r.Cl)({},t),((n={})[e.id]=e,n))}),{});a.setItem(t,JSON.stringify(Object.values(i)))}(o,t),function(t,e){var n=u(t);a.setItem(t,JSON.stringify((0,r.Cl)((0,r.Cl)({},n),e)))}(s,i.seen)}))}catch(t){console.error(t)}}})),i}return(0,r.C6)(e,t),e}(i.M)},8781:(t,e,n)=>{"use strict";n.d(e,{W:()=>i});var r=n(6589);function i(t,e){var n=new r.Facade(t,e);return"track"===t.type&&(n=new r.Track(t,e)),"identify"===t.type&&(n=new r.Identify(t,e)),"page"===t.type&&(n=new r.Page(t,e)),"alias"===t.type&&(n=new r.Alias(t,e)),"group"===t.type&&(n=new r.Group(t,e)),"screen"===t.type&&(n=new r.Screen(t,e)),Object.defineProperty(n,"obj",{value:t,writable:!0}),n}},1986:(t,e,n)=>{"use strict";n.r(e),n.d(e,{applyDestinationMiddleware:()=>c,sourceMiddlewarePlugin:()=>u});var r=n(5450),i=n(9320),o=n(9663),s=n(8781),a=function(t){return JSON.parse(JSON.stringify(t))};function c(t,e,n){return(0,r.sH)(this,void 0,void 0,(function(){function i(e,n){return(0,r.sH)(this,void 0,void 0,(function(){var i,a,c;return(0,r.YH)(this,(function(u){switch(u.label){case 0:return i=!1,a=null,[4,(0,o.m)(n({payload:(0,s.W)(e,{clone:!0,traverse:!1}),integration:t,next:function(t){i=!0,null===t&&(a=null),t&&(a=t.obj)}}))];case 1:return u.sent(),i||null===a||(a.integrations=(0,r.Cl)((0,r.Cl)({},e.integrations),((c={})[t]=!1,c))),[2,a]}}))}))}var c,u,l,d,p;return(0,r.YH)(this,(function(t){switch(t.label){case 0:c=a(e),u=0,l=n,t.label=1;case 1:return u<l.length?(d=l[u],[4,i(c,d)]):[3,4];case 2:if(null===(p=t.sent()))return[2,null];c=p,t.label=3;case 3:return u++,[3,1];case 4:return[2,c]}}))}))}function u(t,e){function n(n){return(0,r.sH)(this,void 0,void 0,(function(){var a;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return a=!1,[4,(0,o.m)(t({payload:(0,s.W)(n.event,{clone:!0,traverse:!1}),integrations:null!=e?e:{},next:function(t){a=!0,t&&(n.event=t.obj)}}))];case 1:if(r.sent(),!a)throw new i.d({retry:!1,type:"middleware_cancellation",reason:"Middleware `next` function skipped"});return[2,n]}}))}))}return{name:"Source Middleware ".concat(t.name),type:"before",version:"0.1.0",isLoaded:function(){return!0},load:function(t){return Promise.resolve(t)},track:n,page:n,identify:n,alias:n,group:n}}},6205:(t,e,n)=>{"use strict";n.d(e,{S8:()=>d,XZ:()=>l});var r,i=n(5450),o=n(1558),s=n(4360),a=n(3587),c=n(3605),u="npm";function l(){return u}function d(t,e,n,u){var d,p,h,f,m=t.user(),g=window.location.search;e.context=null!==(p=null!==(d=e.context)&&void 0!==d?d:e.options)&&void 0!==p?p:{};var y=e.context,v=e.anonymousId;delete e.options,e.writeKey=null==n?void 0:n.apiKey,y.userAgent=window.navigator.userAgent;var _=navigator.userLanguage||navigator.language;if(void 0===y.locale&&void 0!==_&&(y.locale=_),!y.library){var b=l();y.library="web"===b?{name:"analytics.js",version:"next-".concat(c.r)}:{name:"analytics.js",version:"npm:next-".concat(c.r)}}g&&!y.campaign&&(y.campaign=function(t){return t.startsWith("?")&&(t=t.substring(1)),(t=t.replace(/\?/g,"&")).split("&").reduce((function(t,e){var n=e.split("="),r=n[0],i=n[1],o=void 0===i?"":i;if(r.includes("utm_")&&r.length>4){var a=r.substr(4);"campaign"===a&&(a="name"),t[a]=(0,s.p)(o)}return t}),{})}(g)),function(t,e,n){var s=o.A.get("s:context.referrer"),c=function(t){var e={btid:"dataxu",urid:"millennial-media"};t.startsWith("?")&&(t=t.substring(1));for(var n=0,r=(t=t.replace(/\?/g,"&")).split("&");n<r.length;n++){var i=r[n].split("="),o=i[0],s=i[1];if(e[o])return{id:s,type:e[o]}}}(t);s=s?JSON.parse(s):void 0,(c=null!=c?c:s)&&(e&&(e.referrer=(0,i.Cl)((0,i.Cl)({},e.referrer),c)),n||o.A.set("s:context.referrer",JSON.stringify(c),function(){if(r)return r;var t=(0,a.f)(window.location.href);return r={expires:31536e6,secure:!1,path:"/"},t&&(r.domain=t),r}()))}(g,y,null!==(h=t.options.disableClientPersistence)&&void 0!==h&&h),e.userId=e.userId||m.id(),e.anonymousId=m.anonymousId(v),e.sentAt=new Date;var S=t.queue.failedInitializations||[];S.length>0&&(e._metadata={failedInitializations:S});var w=[],k=[];for(var x in u){var C=u[x];"Segment.io"===x&&w.push(x),"bundled"===C.bundlingStatus&&w.push(x),"unbundled"===C.bundlingStatus&&k.push(x)}for(var E=0,T=(null==n?void 0:n.unbundledIntegrations)||[];E<T.length;E++){var I=T[E];k.includes(I)||k.push(I)}var R=null!==(f=null==n?void 0:n.maybeBundledConfigIds)&&void 0!==f?f:{},O=[];w.sort().forEach((function(t){var e;(null!==(e=R[t])&&void 0!==e?e:[]).forEach((function(t){O.push(t)}))})),!1!==(null==n?void 0:n.addBundledMetadata)&&(e._metadata=(0,i.Cl)((0,i.Cl)({},e._metadata),{bundled:w.sort(),unbundled:k.sort(),bundledIds:O}));var M=function(){var t=o.A.get("_ga");if(t&&t.startsWith("amp"))return t}();return M&&(y.amp={id:M}),e}},4985:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(n(6287));function o(t,e){return function(){var n=this.traits(),r=this.properties?this.properties():{};return i.default(n,"address."+t)||i.default(n,t)||(e?i.default(n,"address."+e):null)||(e?i.default(n,e):null)||i.default(r,"address."+t)||i.default(r,t)||(e?i.default(r,"address."+e):null)||(e?i.default(r,e):null)}}e.default=function(t){t.zip=o("postalCode","zip"),t.country=o("country"),t.street=o("street"),t.state=o("state"),t.city=o("city"),t.region=o("region")}},2405:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Alias=void 0;var i=r(n(1193)),o=n(5175);function s(t,e){o.Facade.call(this,t,e)}e.Alias=s,i.default(s,o.Facade),s.prototype.action=function(){return"alias"},s.prototype.type=s.prototype.action,s.prototype.previousId=function(){return this.field("previousId")||this.field("from")},s.prototype.from=s.prototype.previousId,s.prototype.userId=function(){return this.field("userId")||this.field("to")},s.prototype.to=s.prototype.userId},1472:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.clone=void 0,e.clone=function t(e){if("[object Object]"===Object.prototype.toString.call(e)){var n={};for(var r in e)n[r]=t(e[r]);return n}return Array.isArray(e)?e.map(t):e}},7530:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Delete=void 0;var i=r(n(1193)),o=n(5175);function s(t,e){o.Facade.call(this,t,e)}e.Delete=s,i.default(s,o.Facade),s.prototype.type=function(){return"delete"}},5175:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Facade=void 0;var i=r(n(4985)),o=n(1472),s=r(n(9897)),a=r(n(3220)),c=r(n(6287)),u=r(n(6127));function l(t,e){e=e||{},this.raw=o.clone(t),"clone"in e||(e.clone=!0),e.clone&&(t=o.clone(t)),"traverse"in e||(e.traverse=!0),t.timestamp="timestamp"in t?a.default(t.timestamp):new Date,e.traverse&&u.default(t),this.opts=e,this.obj=t}e.Facade=l;var d=l.prototype;function p(t){return o.clone(t)}d.proxy=function(t){var e=t.split("."),n=this[t=e.shift()]||this.field(t);return n?("function"==typeof n&&(n=n.call(this)||{}),0===e.length||(n=c.default(n,e.join("."))),this.opts.clone?p(n):n):n},d.field=function(t){var e=this.obj[t];return this.opts.clone?p(e):e},l.proxy=function(t){return function(){return this.proxy(t)}},l.field=function(t){return function(){return this.field(t)}},l.multi=function(t){return function(){var e=this.proxy(t+"s");if(Array.isArray(e))return e;var n=this.proxy(t);return n&&(n=[this.opts.clone?o.clone(n):n]),n||[]}},l.one=function(t){return function(){var e=this.proxy(t);if(e)return e;var n=this.proxy(t+"s");return Array.isArray(n)?n[0]:void 0}},d.json=function(){var t=this.opts.clone?o.clone(this.obj):this.obj;return this.type&&(t.type=this.type()),t},d.rawEvent=function(){return this.raw},d.options=function(t){var e=this.obj.options||this.obj.context||{},n=this.opts.clone?o.clone(e):e;if(!t)return n;if(this.enabled(t)){var r=this.integrations(),i=r[t]||c.default(r,t);return"object"!=typeof i&&(i=c.default(this.options(),t)),"object"==typeof i?i:{}}},d.context=d.options,d.enabled=function(t){var e=this.proxy("options.providers.all");"boolean"!=typeof e&&(e=this.proxy("options.all")),"boolean"!=typeof e&&(e=this.proxy("integrations.all")),"boolean"!=typeof e&&(e=!0);var n=e&&s.default(t),r=this.integrations();if(r.providers&&r.providers.hasOwnProperty(t)&&(n=r.providers[t]),r.hasOwnProperty(t)){var i=r[t];n="boolean"!=typeof i||i}return!!n},d.integrations=function(){return this.obj.integrations||this.proxy("options.providers")||this.options()},d.active=function(){var t=this.proxy("options.active");return null==t&&(t=!0),t},d.anonymousId=function(){return this.field("anonymousId")||this.field("sessionId")},d.sessionId=d.anonymousId,d.groupId=l.proxy("options.groupId"),d.traits=function(t){var e=this.proxy("options.traits")||{},n=this.userId();for(var r in t=t||{},n&&(e.id=n),t){var i=null==this[r]?this.proxy("options.traits."+r):this[r]();null!=i&&(e[t[r]]=i,delete e[r])}return e},d.library=function(){var t=this.proxy("options.library");return t?"string"==typeof t?{name:t,version:null}:t:{name:"unknown",version:null}},d.device=function(){var t=this.proxy("context.device");"object"==typeof t&&null!==t||(t={});var e=this.library().name;return t.type||(e.indexOf("ios")>-1&&(t.type="ios"),e.indexOf("android")>-1&&(t.type="android")),t},d.userAgent=l.proxy("context.userAgent"),d.timezone=l.proxy("context.timezone"),d.timestamp=l.field("timestamp"),d.channel=l.field("channel"),d.ip=l.proxy("context.ip"),d.userId=l.field("userId"),i.default(d)},8114:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Group=void 0;var i=r(n(1193)),o=r(n(3156)),s=r(n(3220)),a=n(5175);function c(t,e){a.Facade.call(this,t,e)}e.Group=c,i.default(c,a.Facade);var u=c.prototype;u.action=function(){return"group"},u.type=u.action,u.groupId=a.Facade.field("groupId"),u.created=function(){var t=this.proxy("traits.createdAt")||this.proxy("traits.created")||this.proxy("properties.createdAt")||this.proxy("properties.created");if(t)return s.default(t)},u.email=function(){var t=this.proxy("traits.email");if(t)return t;var e=this.groupId();return o.default(e)?e:void 0},u.traits=function(t){var e=this.properties(),n=this.groupId();for(var r in t=t||{},n&&(e.id=n),t){var i=null==this[r]?this.proxy("traits."+r):this[r]();null!=i&&(e[t[r]]=i,delete e[r])}return e},u.name=a.Facade.proxy("traits.name"),u.industry=a.Facade.proxy("traits.industry"),u.employees=a.Facade.proxy("traits.employees"),u.properties=function(){return this.field("traits")||this.field("properties")||{}}},8141:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Identify=void 0;var i=n(5175),o=r(n(6287)),s=r(n(1193)),a=r(n(3156)),c=r(n(3220)),u=function(t){return t.trim()};function l(t,e){i.Facade.call(this,t,e)}e.Identify=l,s.default(l,i.Facade);var d=l.prototype;d.action=function(){return"identify"},d.type=d.action,d.traits=function(t){var e=this.field("traits")||{},n=this.userId();for(var r in t=t||{},n&&(e.id=n),t){var i=null==this[r]?this.proxy("traits."+r):this[r]();null!=i&&(e[t[r]]=i,r!==t[r]&&delete e[r])}return e},d.email=function(){var t=this.proxy("traits.email");if(t)return t;var e=this.userId();return a.default(e)?e:void 0},d.created=function(){var t=this.proxy("traits.created")||this.proxy("traits.createdAt");if(t)return c.default(t)},d.companyCreated=function(){var t=this.proxy("traits.company.created")||this.proxy("traits.company.createdAt");if(t)return c.default(t)},d.companyName=function(){return this.proxy("traits.company.name")},d.name=function(){var t=this.proxy("traits.name");if("string"==typeof t)return u(t);var e=this.firstName(),n=this.lastName();return e&&n?u(e+" "+n):void 0},d.firstName=function(){var t=this.proxy("traits.firstName");if("string"==typeof t)return u(t);var e=this.proxy("traits.name");return"string"==typeof e?u(e).split(" ")[0]:void 0},d.lastName=function(){var t=this.proxy("traits.lastName");if("string"==typeof t)return u(t);var e=this.proxy("traits.name");if("string"==typeof e){var n=u(e).indexOf(" ");if(-1!==n)return u(e.substr(n+1))}},d.uid=function(){return this.userId()||this.username()||this.email()},d.description=function(){return this.proxy("traits.description")||this.proxy("traits.background")},d.age=function(){var t=this.birthday(),e=o.default(this.traits(),"age");return null!=e?e:t instanceof Date?(new Date).getFullYear()-t.getFullYear():void 0},d.avatar=function(){var t=this.traits();return o.default(t,"avatar")||o.default(t,"photoUrl")||o.default(t,"avatarUrl")},d.position=function(){var t=this.traits();return o.default(t,"position")||o.default(t,"jobTitle")},d.username=i.Facade.proxy("traits.username"),d.website=i.Facade.one("traits.website"),d.websites=i.Facade.multi("traits.website"),d.phone=i.Facade.one("traits.phone"),d.phones=i.Facade.multi("traits.phone"),d.address=i.Facade.proxy("traits.address"),d.gender=i.Facade.proxy("traits.gender"),d.birthday=i.Facade.proxy("traits.birthday")},6589:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},r.apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.Delete=e.Screen=e.Page=e.Track=e.Identify=e.Group=e.Alias=e.Facade=void 0;var i=n(5175);Object.defineProperty(e,"Facade",{enumerable:!0,get:function(){return i.Facade}});var o=n(2405);Object.defineProperty(e,"Alias",{enumerable:!0,get:function(){return o.Alias}});var s=n(8114);Object.defineProperty(e,"Group",{enumerable:!0,get:function(){return s.Group}});var a=n(8141);Object.defineProperty(e,"Identify",{enumerable:!0,get:function(){return a.Identify}});var c=n(4294);Object.defineProperty(e,"Track",{enumerable:!0,get:function(){return c.Track}});var u=n(2321);Object.defineProperty(e,"Page",{enumerable:!0,get:function(){return u.Page}});var l=n(4021);Object.defineProperty(e,"Screen",{enumerable:!0,get:function(){return l.Screen}});var d=n(7530);Object.defineProperty(e,"Delete",{enumerable:!0,get:function(){return d.Delete}}),e.default=r(r({},i.Facade),{Alias:o.Alias,Group:s.Group,Identify:a.Identify,Track:c.Track,Page:u.Page,Screen:l.Screen,Delete:d.Delete})},3156:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=/.+\@.+\..+/;e.default=function(t){return n.test(t)}},9897:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n={Salesforce:!0};e.default=function(t){return!n[t]}},2321:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Page=void 0;var i=r(n(1193)),o=n(5175),s=n(4294),a=r(n(3156));function c(t,e){o.Facade.call(this,t,e)}e.Page=c,i.default(c,o.Facade);var u=c.prototype;u.action=function(){return"page"},u.type=u.action,u.category=o.Facade.field("category"),u.name=o.Facade.field("name"),u.title=o.Facade.proxy("properties.title"),u.path=o.Facade.proxy("properties.path"),u.url=o.Facade.proxy("properties.url"),u.referrer=function(){return this.proxy("context.referrer.url")||this.proxy("context.page.referrer")||this.proxy("properties.referrer")},u.properties=function(t){var e=this.field("properties")||{},n=this.category(),r=this.name();for(var i in t=t||{},n&&(e.category=n),r&&(e.name=r),t){var o=null==this[i]?this.proxy("properties."+i):this[i]();null!=o&&(e[t[i]]=o,i!==t[i]&&delete e[i])}return e},u.email=function(){var t=this.proxy("context.traits.email")||this.proxy("properties.email");if(t)return t;var e=this.userId();return a.default(e)?e:void 0},u.fullName=function(){var t=this.category(),e=this.name();return e&&t?t+" "+e:e},u.event=function(t){return t?"Viewed "+t+" Page":"Loaded a Page"},u.track=function(t){var e=this.json();return e.event=this.event(t),e.timestamp=this.timestamp(),e.properties=this.properties(),new s.Track(e,this.opts)}},4021:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Screen=void 0;var i=r(n(1193)),o=n(2321),s=n(4294);function a(t,e){o.Page.call(this,t,e)}e.Screen=a,i.default(a,o.Page),a.prototype.action=function(){return"screen"},a.prototype.type=a.prototype.action,a.prototype.event=function(t){return t?"Viewed "+t+" Screen":"Loaded a Screen"},a.prototype.track=function(t){var e=this.json();return e.event=this.event(t),e.timestamp=this.timestamp(),e.properties=this.properties(),new s.Track(e,this.opts)}},4294:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Track=void 0;var i=r(n(1193)),o=n(5175),s=n(8141),a=r(n(3156)),c=r(n(6287));function u(t,e){o.Facade.call(this,t,e)}e.Track=u,i.default(u,o.Facade);var l=u.prototype;l.action=function(){return"track"},l.type=l.action,l.event=o.Facade.field("event"),l.value=o.Facade.proxy("properties.value"),l.category=o.Facade.proxy("properties.category"),l.id=o.Facade.proxy("properties.id"),l.productId=function(){return this.proxy("properties.product_id")||this.proxy("properties.productId")},l.promotionId=function(){return this.proxy("properties.promotion_id")||this.proxy("properties.promotionId")},l.cartId=function(){return this.proxy("properties.cart_id")||this.proxy("properties.cartId")},l.checkoutId=function(){return this.proxy("properties.checkout_id")||this.proxy("properties.checkoutId")},l.paymentId=function(){return this.proxy("properties.payment_id")||this.proxy("properties.paymentId")},l.couponId=function(){return this.proxy("properties.coupon_id")||this.proxy("properties.couponId")},l.wishlistId=function(){return this.proxy("properties.wishlist_id")||this.proxy("properties.wishlistId")},l.reviewId=function(){return this.proxy("properties.review_id")||this.proxy("properties.reviewId")},l.orderId=function(){return this.proxy("properties.id")||this.proxy("properties.order_id")||this.proxy("properties.orderId")},l.sku=o.Facade.proxy("properties.sku"),l.tax=o.Facade.proxy("properties.tax"),l.name=o.Facade.proxy("properties.name"),l.price=o.Facade.proxy("properties.price"),l.total=o.Facade.proxy("properties.total"),l.repeat=o.Facade.proxy("properties.repeat"),l.coupon=o.Facade.proxy("properties.coupon"),l.shipping=o.Facade.proxy("properties.shipping"),l.discount=o.Facade.proxy("properties.discount"),l.shippingMethod=function(){return this.proxy("properties.shipping_method")||this.proxy("properties.shippingMethod")},l.paymentMethod=function(){return this.proxy("properties.payment_method")||this.proxy("properties.paymentMethod")},l.description=o.Facade.proxy("properties.description"),l.plan=o.Facade.proxy("properties.plan"),l.subtotal=function(){var t=c.default(this.properties(),"subtotal"),e=this.total()||this.revenue();if(t)return t;if(!e)return 0;if(this.total()){var n=this.tax();n&&(e-=n),(n=this.shipping())&&(e-=n),(n=this.discount())&&(e+=n)}return e},l.products=function(){var t=this.properties(),e=c.default(t,"products");return Array.isArray(e)?e.filter((function(t){return null!==t})):[]},l.quantity=function(){return(this.obj.properties||{}).quantity||1},l.currency=function(){return(this.obj.properties||{}).currency||"USD"},l.referrer=function(){return this.proxy("context.referrer.url")||this.proxy("context.page.referrer")||this.proxy("properties.referrer")},l.query=o.Facade.proxy("options.query"),l.properties=function(t){var e=this.field("properties")||{};for(var n in t=t||{}){var r=null==this[n]?this.proxy("properties."+n):this[n]();null!=r&&(e[t[n]]=r,delete e[n])}return e},l.username=function(){return this.proxy("traits.username")||this.proxy("properties.username")||this.userId()||this.sessionId()},l.email=function(){var t=this.proxy("traits.email")||this.proxy("properties.email")||this.proxy("options.traits.email");if(t)return t;var e=this.userId();return a.default(e)?e:void 0},l.revenue=function(){var t=this.proxy("properties.revenue"),e=this.event();return!t&&e&&e.match(/^[ _]?completed[ _]?order[ _]?|^[ _]?order[ _]?completed[ _]?$/i)&&(t=this.proxy("properties.total")),function(t){if(t){if("number"==typeof t)return t;if("string"==typeof t)return t=t.replace(/\$/g,""),t=parseFloat(t),isNaN(t)?void 0:t}}(t)},l.cents=function(){var t=this.revenue();return"number"!=typeof t?this.value()||0:100*t},l.identify=function(){var t=this.json();return t.traits=this.traits(),new s.Identify(t,this.opts)}},6127:(t,e,n)=>{"use strict";var r=n(8508);t.exports=function t(e,n){return void 0===n&&(n=!0),e&&"object"==typeof e?function(e,n){return Object.keys(e).forEach((function(r){e[r]=t(e[r],n)})),e}(e,n):Array.isArray(e)?function(e,n){return e.forEach((function(r,i){e[i]=t(r,n)})),e}(e,n):r.is(e,n)?r.parse(e):e}},8508:(t,e)=>{"use strict";var n=/^(\d{4})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:([ T])(\d{2}):?(\d{2})(?::?(\d{2})(?:[,\.](\d{1,}))?)?(?:(Z)|([+\-])(\d{2})(?::?(\d{2}))?)?)?$/;e.parse=function(t){var e=[1,5,6,7,11,12],r=n.exec(t),i=0;if(!r)return new Date(t);for(var o,s=0;o=e[s];s++)r[o]=parseInt(r[o],10)||0;r[2]=parseInt(r[2],10)||1,r[3]=parseInt(r[3],10)||1,r[2]--,r[8]=r[8]?(r[8]+"00").substring(0,3):0," "===r[4]?i=(new Date).getTimezoneOffset():"Z"!==r[9]&&r[10]&&(i=60*r[11]+r[12],"+"===r[10]&&(i=0-i));var a=Date.UTC(r[1],r[2],r[3],r[5],r[6]+i,r[7],r[8]);return new Date(a)},e.is=function(t,e){return"string"==typeof t&&(!e||!1!==/^\d{4}-\d{2}-\d{2}/.test(t))&&n.test(t)}},2115:(t,e,n)=>{"use strict";n.r(e),n.d(e,{Breadcrumbs:()=>xo,BrowserClient:()=>ki,BrowserProfilingIntegration:()=>Ah,BrowserTracing:()=>Up,Dedupe:()=>To,Feedback:()=>Ld,FunctionToString:()=>Ve,GlobalHandlers:()=>No,HttpContext:()=>Ho,Hub:()=>Jt,InboundFilters:()=>tn,Integrations:()=>Nh,LinkedErrors:()=>qo,ModuleMetadata:()=>Mn,Replay:()=>Il,ReplayCanvas:()=>ad,SDK_VERSION:()=>Wt,SEMANTIC_ATTRIBUTE_SENTRY_OP:()=>Nn,SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN:()=>Ln,SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE:()=>Dn,SEMANTIC_ATTRIBUTE_SENTRY_SOURCE:()=>An,Scope:()=>qt,TryCatch:()=>Go,WINDOW:()=>si,addBreadcrumb:()=>ve,addEventProcessor:()=>Jn,addGlobalEventProcessor:()=>ht,addIntegration:()=>qe,addTracingExtensions:()=>Br,breadcrumbsIntegration:()=>ko,browserApiErrorsIntegration:()=>Yo,browserProfilingIntegration:()=>Mh,browserTracingIntegration:()=>Yp,captureConsoleIntegration:()=>Fd,captureEvent:()=>ge,captureException:()=>fe,captureMessage:()=>me,captureSession:()=>je,captureUserFeedback:()=>os,chromeStackLineParser:()=>Pi,close:()=>Oe,configureScope:()=>ye,contextLinesIntegration:()=>Hd,continueTrace:()=>yr,createTransport:()=>Gr,createUserFeedbackEnvelope:()=>wi,debugIntegration:()=>Ud,dedupeIntegration:()=>Wd,defaultIntegrations:()=>Qo,defaultRequestInstrumentationOptions:()=>Pp,defaultStackLineParsers:()=>Yi,defaultStackParser:()=>Gi,endSession:()=>Pe,eventFromException:()=>yi,eventFromMessage:()=>vi,exceptionFromError:()=>pi,extraErrorDataIntegration:()=>Xd,extractTraceparentData:()=>ar,feedbackIntegration:()=>Nd,flush:()=>Re,forceLoad:()=>ns,functionToStringIntegration:()=>Je,geckoStackLineParser:()=>$i,getActiveSpan:()=>gr,getActiveTransaction:()=>sr,getClient:()=>Ae,getCurrentHub:()=>Xt,getCurrentScope:()=>Ne,getDefaultIntegrations:()=>Zo,getHubFromCarrier:()=>te,getReplay:()=>Ol,getSpanStatusFromHttpCode:()=>Er,globalHandlersIntegration:()=>Do,httpClientIntegration:()=>Zd,httpContextIntegration:()=>$o,inboundFiltersIntegration:()=>Ze,init:()=>ts,instrumentOutgoingRequests:()=>Fp,isInitialized:()=>De,lastEventId:()=>Me,linkedErrorsIntegration:()=>Uo,makeBrowserOfflineTransport:()=>sh,makeFetchTransport:()=>Ri,makeMain:()=>Kt,makeMultiplexedTransport:()=>Kr,makeXHRTransport:()=>Mi,metrics:()=>ni,moduleMetadataIntegration:()=>On,onLoad:()=>rs,onProfilingStartRouteTransaction:()=>Ih,opera10StackLineParser:()=>qi,opera11StackLineParser:()=>Wi,parameterize:()=>ri,replayCanvasIntegration:()=>sd,replayIntegration:()=>Tl,reportingObserverIntegration:()=>cp,rewriteFramesIntegration:()=>hp,sendFeedback:()=>yd,sessionTimingIntegration:()=>mp,setContext:()=>_e,setCurrentClient:()=>ii,setExtra:()=>Se,setExtras:()=>be,setHttpStatus:()=>Ir,setMeasurement:()=>oi,setTag:()=>ke,setTags:()=>we,setUser:()=>xe,showReportDialog:()=>es,spanStatusfromHttpCode:()=>Tr,startBrowserTracingNavigationSpan:()=>Jp,startBrowserTracingPageLoadSpan:()=>Gp,startInactiveSpan:()=>mr,startSession:()=>Le,startSpan:()=>hr,startSpanManual:()=>fr,startTransaction:()=>Ie,trace:()=>pr,winjsStackLineParser:()=>Bi,withActiveSpan:()=>Te,withIsolationScope:()=>Ee,withScope:()=>Ce,wrap:()=>is});var r={};n.r(r),n.d(r,{FunctionToString:()=>Ve,InboundFilters:()=>tn,LinkedErrors:()=>un});var i={};n.r(i),n.d(i,{Breadcrumbs:()=>xo,Dedupe:()=>To,GlobalHandlers:()=>No,HttpContext:()=>Ho,LinkedErrors:()=>qo,TryCatch:()=>Go});const o=Object.prototype.toString;function s(t){switch(o.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":return!0;default:return y(t,Error)}}function a(t,e){return o.call(t)===`[object ${e}]`}function c(t){return a(t,"ErrorEvent")}function u(t){return a(t,"DOMError")}function l(t){return a(t,"String")}function d(t){return"object"==typeof t&&null!==t&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function p(t){return null===t||d(t)||"object"!=typeof t&&"function"!=typeof t}function h(t){return a(t,"Object")}function f(t){return"undefined"!=typeof Event&&y(t,Event)}function m(t){return Boolean(t&&t.then&&"function"==typeof t.then)}function g(t){return"number"==typeof t&&t!=t}function y(t,e){try{return t instanceof e}catch(t){return!1}}function v(t){return!("object"!=typeof t||null===t||!t.__isVue&&!t._isVue)}function _(t){return t&&t.Math==Math?t:void 0}const b="object"==typeof globalThis&&_(globalThis)||"object"==typeof window&&_(window)||"object"==typeof self&&_(self)||"object"==typeof n.g&&_(n.g)||function(){return this}()||{};function S(){return b}function w(t,e,n){const r=n||b,i=r.__SENTRY__=r.__SENTRY__||{};return i[t]||(i[t]=e())}const k=S(),x=80;function C(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,i=[];let o=0,s=0;const a=" > ",c=a.length;let u;const l=Array.isArray(e)?e:e.keyAttrs,d=!Array.isArray(e)&&e.maxStringLength||x;for(;n&&o++<r&&(u=E(n,l),!("html"===u||o>1&&s+i.length*c+u.length>=d));)i.push(u),s+=u.length,n=n.parentNode;return i.reverse().join(a)}catch(t){return"<unknown>"}}function E(t,e){const n=t,r=[];let i,o,s,a,c;if(!n||!n.tagName)return"";if(k.HTMLElement&&n instanceof HTMLElement&&n.dataset&&n.dataset.sentryComponent)return n.dataset.sentryComponent;r.push(n.tagName.toLowerCase());const u=e&&e.length?e.filter((t=>n.getAttribute(t))).map((t=>[t,n.getAttribute(t)])):null;if(u&&u.length)u.forEach((t=>{r.push(`[${t[0]}="${t[1]}"]`)}));else if(n.id&&r.push(`#${n.id}`),i=n.className,i&&l(i))for(o=i.split(/\s+/),c=0;c<o.length;c++)r.push(`.${o[c]}`);const d=["aria-label","type","name","title","alt"];for(c=0;c<d.length;c++)s=d[c],a=n.getAttribute(s),a&&r.push(`[${s}="${a}"]`);return r.join("")}function T(){try{return k.document.location.href}catch(t){return""}}function I(t){return k.document&&k.document.querySelector?k.document.querySelector(t):null}function R(t){if(!k.HTMLElement)return null;let e=t;for(let t=0;t<5;t++){if(!e)return null;if(e instanceof HTMLElement&&e.dataset.sentryComponent)return e.dataset.sentryComponent;e=e.parentNode}return null}const O="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,M=["debug","info","warn","error","log","assert","trace"],A={};function D(t){if(!("console"in b))return t();const e=b.console,n={},r=Object.keys(A);r.forEach((t=>{const r=A[t];n[t]=e[t],e[t]=r}));try{return t()}finally{r.forEach((t=>{e[t]=n[t]}))}}const N=function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return O?M.forEach((n=>{e[n]=(...e)=>{t&&D((()=>{b.console[n](`Sentry Logger [${n}]:`,...e)}))}})):M.forEach((t=>{e[t]=()=>{}})),e}();function L(t,e=0){return"string"!=typeof t||0===e||t.length<=e?t:`${t.slice(0,e)}...`}function P(t,e){let n=t;const r=n.length;if(r<=150)return n;e>r&&(e=r);let i=Math.max(e-60,0);i<5&&(i=0);let o=Math.min(i+140,r);return o>r-5&&(o=r),o===r&&(i=Math.max(o-140,0)),n=n.slice(i,o),i>0&&(n=`'{snip} ${n}`),o<r&&(n+=" {snip}"),n}function F(t,e){if(!Array.isArray(t))return"";const n=[];for(let e=0;e<t.length;e++){const r=t[e];try{v(r)?n.push("[VueViewModel]"):n.push(String(r))}catch(t){n.push("[value cannot be serialized]")}}return n.join(e)}function j(t,e=[],n=!1){return e.some((e=>function(t,e,n=!1){return!!l(t)&&(a(e,"RegExp")?e.test(t):!!l(e)&&(n?t===e:t.includes(e)))}(t,e,n)))}function $(t,e,n){if(!(e in t))return;const r=t[e],i=n(r);"function"==typeof i&&B(i,r),t[e]=i}function H(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch(n){O&&N.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function B(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,H(t,"__sentry_original__",e)}catch(t){}}function U(t){return t.__sentry_original__}function q(t){if(s(t))return{message:t.message,name:t.name,stack:t.stack,...W(t)};if(f(t)){const e={type:t.type,target:z(t.target),currentTarget:z(t.currentTarget),...W(t)};return"undefined"!=typeof CustomEvent&&y(t,CustomEvent)&&(e.detail=t.detail),e}return t}function z(t){try{return"undefined"!=typeof Element&&y(t,Element)?C(t):Object.prototype.toString.call(t)}catch(t){return"<unknown>"}}function W(t){if("object"==typeof t&&null!==t){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function Y(t){return G(t,new Map)}function G(t,e){if(function(t){if(!h(t))return!1;try{const e=Object.getPrototypeOf(t).constructor.name;return!e||"Object"===e}catch(t){return!0}}(t)){const n=e.get(t);if(void 0!==n)return n;const r={};e.set(t,r);for(const n of Object.keys(t))void 0!==t[n]&&(r[n]=G(t[n],e));return r}if(Array.isArray(t)){const n=e.get(t);if(void 0!==n)return n;const r=[];return e.set(t,r),t.forEach((t=>{r.push(G(t,e))})),r}return t}const J="production",V="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function K(){const t=b,e=t.crypto||t.msCrypto;let n=()=>16*Math.random();try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(n=()=>{const t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(t){}return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,(t=>(t^(15&n())>>t/4).toString(16)))}function X(t){return t.exception&&t.exception.values?t.exception.values[0]:void 0}function Q(t){const{message:e,event_id:n}=t;if(e)return e;const r=X(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function Z(t,e,n){const r=t.exception=t.exception||{},i=r.values=r.values||[],o=i[0]=i[0]||{};o.value||(o.value=e||""),o.type||(o.type=n||"Error")}function tt(t,e){const n=X(t);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){const t={...r&&r.data,...e.data};n.mechanism.data=t}}function et(t){if(t&&t.__sentry_captured__)return!0;try{H(t,"__sentry_captured__",!0)}catch(t){}return!1}function nt(t){return Array.isArray(t)?t:[t]}const rt=1e3;function it(){return Date.now()/rt}const ot=function(){const{performance:t}=b;if(!t||!t.now)return it;const e=Date.now()-t.now(),n=null==t.timeOrigin?e:t.timeOrigin;return()=>(n+t.now())/rt}();let st;const at=(()=>{const{performance:t}=b;if(!t||!t.now)return void(st="none");const e=36e5,n=t.now(),r=Date.now(),i=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,o=i<e,s=t.timing&&t.timing.navigationStart,a="number"==typeof s?Math.abs(s+n-r):e;return o||a<e?i<=a?(st="timeOrigin",t.timeOrigin):(st="navigationStart",s):(st="dateNow",r)})();var ct;function ut(t){return new dt((e=>{e(t)}))}function lt(t){return new dt(((e,n)=>{n(t)}))}!function(t){t[t.PENDING=0]="PENDING",t[t.RESOLVED=1]="RESOLVED",t[t.REJECTED=2]="REJECTED"}(ct||(ct={}));class dt{constructor(t){dt.prototype.__init.call(this),dt.prototype.__init2.call(this),dt.prototype.__init3.call(this),dt.prototype.__init4.call(this),this._state=ct.PENDING,this._handlers=[];try{t(this._resolve,this._reject)}catch(t){this._reject(t)}}then(t,e){return new dt(((n,r)=>{this._handlers.push([!1,e=>{if(t)try{n(t(e))}catch(t){r(t)}else n(e)},t=>{if(e)try{n(e(t))}catch(t){r(t)}else r(t)}]),this._executeHandlers()}))}catch(t){return this.then((t=>t),t)}finally(t){return new dt(((e,n)=>{let r,i;return this.then((e=>{i=!1,r=e,t&&t()}),(e=>{i=!0,r=e,t&&t()})).then((()=>{i?n(r):e(r)}))}))}__init(){this._resolve=t=>{this._setResult(ct.RESOLVED,t)}}__init2(){this._reject=t=>{this._setResult(ct.REJECTED,t)}}__init3(){this._setResult=(t,e)=>{this._state===ct.PENDING&&(m(e)?e.then(this._resolve,this._reject):(this._state=t,this._value=e,this._executeHandlers()))}}__init4(){this._executeHandlers=()=>{if(this._state===ct.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach((t=>{t[0]||(this._state===ct.RESOLVED&&t[1](this._value),this._state===ct.REJECTED&&t[2](this._value),t[0]=!0)}))}}}function pt(){return w("globalEventProcessors",(()=>[]))}function ht(t){pt().push(t)}function ft(t,e,n,r=0){return new dt(((i,o)=>{const s=t[r];if(null===e||"function"!=typeof s)i(e);else{const a=s({...e},n);V&&s.id&&null===a&&N.log(`Event processor "${s.id}" dropped event`),m(a)?a.then((e=>ft(t,e,n,r+1).then(i))).then(null,o):ft(t,a,n,r+1).then(i).then(null,o)}}))}function mt(t){const e=ot(),n={sid:K(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(t){return Y({sid:`${t.sid}`,init:t.init,started:new Date(1e3*t.started).toISOString(),timestamp:new Date(1e3*t.timestamp).toISOString(),status:t.status,errors:t.errors,did:"number"==typeof t.did||"string"==typeof t.did?`${t.did}`:void 0,duration:t.duration,abnormal_mechanism:t.abnormal_mechanism,attrs:{release:t.release,environment:t.environment,ip_address:t.ipAddress,user_agent:t.userAgent}})}(n)};return t&&gt(n,t),n}function gt(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||ot(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=32===e.sid.length?e.sid:K()),void 0!==e.init&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),"number"==typeof e.started&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if("number"==typeof e.duration)t.duration=e.duration;else{const e=t.timestamp-t.started;t.duration=e>=0?e:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),"number"==typeof e.errors&&(t.errors=e.errors),e.status&&(t.status=e.status)}function yt(t,e){let n={};e?n={status:e}:"ok"===t.status&&(n={status:"exited"}),gt(t,n)}function vt(t){return t.transaction}const _t="baggage",bt="sentry-",St=/^sentry-/,wt=8192;function kt(t){if(!l(t)&&!Array.isArray(t))return;let e={};if(Array.isArray(t))e=t.reduce(((t,e)=>{const n=Ct(e);for(const e of Object.keys(n))t[e]=n[e];return t}),{});else{if(!t)return;e=Ct(t)}const n=Object.entries(e).reduce(((t,[e,n])=>(e.match(St)&&(t[e.slice(bt.length)]=n),t)),{});return Object.keys(n).length>0?n:void 0}function xt(t){if(t)return function(t){if(0!==Object.keys(t).length)return Object.entries(t).reduce(((t,[e,n],r)=>{const i=`${encodeURIComponent(e)}=${encodeURIComponent(n)}`,o=0===r?i:`${t},${i}`;return o.length>wt?(O&&N.warn(`Not adding key: ${e} with val: ${n} to baggage header due to exceeding baggage size limits.`),t):o}),"")}(Object.entries(t).reduce(((t,[e,n])=>(n&&(t[`${bt}${e}`]=n),t)),{}))}function Ct(t){return t.split(",").map((t=>t.split("=").map((t=>decodeURIComponent(t.trim()))))).reduce(((t,[e,n])=>(t[e]=n,t)),{})}const Et=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Tt(t){if(!t)return;const e=t.match(Et);if(!e)return;let n;return"1"===e[3]?n=!0:"0"===e[3]&&(n=!1),{traceId:e[1],parentSampled:n,parentSpanId:e[2]}}function It(t,e){const n=Tt(t),r=kt(e),{traceId:i,parentSpanId:o,parentSampled:s}=n||{};return n?{traceId:i||K(),parentSpanId:o||K().substring(16),spanId:K().substring(16),sampled:s,dsc:r||{}}:{traceId:i||K(),spanId:K().substring(16)}}function Rt(t=K(),e=K().substring(16),n){let r="";return void 0!==n&&(r=n?"-1":"-0"),`${t}-${e}${r}`}const Ot=1;function Mt(t){const{spanId:e,traceId:n}=t.spanContext(),{data:r,op:i,parent_span_id:o,status:s,tags:a,origin:c}=Lt(t);return Y({data:r,op:i,parent_span_id:o,span_id:e,status:s,tags:a,trace_id:n,origin:c})}function At(t){const{traceId:e,spanId:n}=t.spanContext();return Rt(e,n,Pt(t))}function Dt(t){return"number"==typeof t?Nt(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?Nt(t.getTime()):ot()}function Nt(t){return t>9999999999?t/1e3:t}function Lt(t){return function(t){return"function"==typeof t.getSpanJSON}(t)?t.getSpanJSON():"function"==typeof t.toJSON?t.toJSON():{}}function Pt(t){const{traceFlags:e}=t.spanContext();return Boolean(e&Ot)}function Ft(t,e,n){const r=e.getOptions(),{publicKey:i}=e.getDsn()||{},{segment:o}=n&&n.getUser()||{},s=Y({environment:r.environment||J,release:r.release,user_segment:o,public_key:i,trace_id:t});return e.emit&&e.emit("createDsc",s),s}function jt(t){const e=Ae();if(!e)return{};const n=Ft(Lt(t).trace_id||"",e,Ne()),r=vt(t);if(!r)return n;const i=r&&r._frozenDynamicSamplingContext;if(i)return i;const{sampleRate:o,source:s}=r.metadata;null!=o&&(n.sample_rate=`${o}`);const a=Lt(r);return s&&"url"!==s&&(n.transaction=a.description),n.sampled=String(Pt(r)),e.emit&&e.emit("createDsc",n),n}function $t(t,e){const{fingerprint:n,span:r,breadcrumbs:i,sdkProcessingMetadata:o}=e;!function(t,e){const{extra:n,tags:r,user:i,contexts:o,level:s,transactionName:a}=e,c=Y(n);c&&Object.keys(c).length&&(t.extra={...c,...t.extra});const u=Y(r);u&&Object.keys(u).length&&(t.tags={...u,...t.tags});const l=Y(i);l&&Object.keys(l).length&&(t.user={...l,...t.user});const d=Y(o);d&&Object.keys(d).length&&(t.contexts={...d,...t.contexts}),s&&(t.level=s),a&&(t.transaction=a)}(t,e),r&&function(t,e){t.contexts={trace:Mt(e),...t.contexts};const n=vt(e);if(n){t.sdkProcessingMetadata={dynamicSamplingContext:jt(e),...t.sdkProcessingMetadata};const r=Lt(n).description;r&&(t.tags={transaction:r,...t.tags})}}(t,r),function(t,e){t.fingerprint=t.fingerprint?nt(t.fingerprint):[],e&&(t.fingerprint=t.fingerprint.concat(e)),t.fingerprint&&!t.fingerprint.length&&delete t.fingerprint}(t,n),function(t,e){const n=[...t.breadcrumbs||[],...e];t.breadcrumbs=n.length?n:void 0}(t,i),function(t,e){t.sdkProcessingMetadata={...t.sdkProcessingMetadata,...e}}(t,o)}function Ht(t,e){const{extra:n,tags:r,user:i,contexts:o,level:s,sdkProcessingMetadata:a,breadcrumbs:c,fingerprint:u,eventProcessors:l,attachments:d,propagationContext:p,transactionName:h,span:f}=e;Bt(t,"extra",n),Bt(t,"tags",r),Bt(t,"user",i),Bt(t,"contexts",o),Bt(t,"sdkProcessingMetadata",a),s&&(t.level=s),h&&(t.transactionName=h),f&&(t.span=f),c.length&&(t.breadcrumbs=[...t.breadcrumbs,...c]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),l.length&&(t.eventProcessors=[...t.eventProcessors,...l]),d.length&&(t.attachments=[...t.attachments,...d]),t.propagationContext={...t.propagationContext,...p}}function Bt(t,e,n){if(n&&Object.keys(n).length){t[e]={...t[e]};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[e][r]=n[r])}}let Ut;class qt{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext=zt()}static clone(t){return t?t.clone():new qt}clone(){const t=new qt;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},t._user=this._user,t._level=this._level,t._span=this._span,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._requestSession=this._requestSession,t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t}setClient(t){this._client=t}getClient(){return this._client}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,segment:void 0,username:void 0},this._session&&gt(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,e){return this._tags={...this._tags,[t]:e},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,e){return this._extra={...this._extra,[t]:e},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,e){return null===e?delete this._contexts[t]:this._contexts[t]=e,this._notifyScopeListeners(),this}setSpan(t){return this._span=t,this._notifyScopeListeners(),this}getSpan(){return this._span}getTransaction(){const t=this._span;return t&&t.transaction}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const e="function"==typeof t?t(this):t;if(e instanceof qt){const t=e.getScopeData();this._tags={...this._tags,...t.tags},this._extra={...this._extra,...t.extra},this._contexts={...this._contexts,...t.contexts},t.user&&Object.keys(t.user).length&&(this._user=t.user),t.level&&(this._level=t.level),t.fingerprint.length&&(this._fingerprint=t.fingerprint),e.getRequestSession()&&(this._requestSession=e.getRequestSession()),t.propagationContext&&(this._propagationContext=t.propagationContext)}else if(h(e)){const e=t;this._tags={...this._tags,...e.tags},this._extra={...this._extra,...e.extra},this._contexts={...this._contexts,...e.contexts},e.user&&(this._user=e.user),e.level&&(this._level=e.level),e.fingerprint&&(this._fingerprint=e.fingerprint),e.requestSession&&(this._requestSession=e.requestSession),e.propagationContext&&(this._propagationContext=e.propagationContext)}return this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._span=void 0,this._session=void 0,this._notifyScopeListeners(),this._attachments=[],this._propagationContext=zt(),this}addBreadcrumb(t,e){const n="number"==typeof e?e:100;if(n<=0)return this;const r={timestamp:it(),...t},i=this._breadcrumbs;return i.push(r),this._breadcrumbs=i.length>n?i.slice(-n):i,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}getAttachments(){return this.getScopeData().attachments}clearAttachments(){return this._attachments=[],this}getScopeData(){const{_breadcrumbs:t,_attachments:e,_contexts:n,_tags:r,_extra:i,_user:o,_level:s,_fingerprint:a,_eventProcessors:c,_propagationContext:u,_sdkProcessingMetadata:l,_transactionName:d,_span:p}=this;return{breadcrumbs:t,attachments:e,contexts:n,tags:r,extra:i,user:o,level:s,fingerprint:a||[],eventProcessors:c,propagationContext:u,sdkProcessingMetadata:l,transactionName:d,span:p}}applyToEvent(t,e={},n=[]){return $t(t,this.getScopeData()),ft([...n,...pt(),...this._eventProcessors],t,e)}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata={...this._sdkProcessingMetadata,...t},this}setPropagationContext(t){return this._propagationContext=t,this}getPropagationContext(){return this._propagationContext}captureException(t,e){const n=e&&e.event_id?e.event_id:K();if(!this._client)return N.warn("No client configured on scope - will not capture exception!"),n;const r=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:r,...e,event_id:n},this),n}captureMessage(t,e,n){const r=n&&n.event_id?n.event_id:K();if(!this._client)return N.warn("No client configured on scope - will not capture message!"),r;const i=new Error(t);return this._client.captureMessage(t,e,{originalException:t,syntheticException:i,...n,event_id:r},this),r}captureEvent(t,e){const n=e&&e.event_id?e.event_id:K();return this._client?(this._client.captureEvent(t,{...e,event_id:n},this),n):(N.warn("No client configured on scope - will not capture event!"),n)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach((t=>{t(this)})),this._notifyingListeners=!1)}}function zt(){return{traceId:K(),spanId:K().substring(16)}}const Wt="7.119.1",Yt=parseFloat(Wt),Gt=100;class Jt{constructor(t,e,n,r=Yt){let i,o;this._version=r,e?i=e:(i=new qt,i.setClient(t)),n?o=n:(o=new qt,o.setClient(t)),this._stack=[{scope:i}],t&&this.bindClient(t),this._isolationScope=o}isOlderThan(t){return this._version<t}bindClient(t){const e=this.getStackTop();e.client=t,e.scope.setClient(t),t&&t.setupIntegrations&&t.setupIntegrations()}pushScope(){const t=this.getScope().clone();return this.getStack().push({client:this.getClient(),scope:t}),t}popScope(){return!(this.getStack().length<=1||!this.getStack().pop())}withScope(t){const e=this.pushScope();let n;try{n=t(e)}catch(t){throw this.popScope(),t}return m(n)?n.then((t=>(this.popScope(),t)),(t=>{throw this.popScope(),t})):(this.popScope(),n)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStack(){return this._stack}getStackTop(){return this._stack[this._stack.length-1]}captureException(t,e){const n=this._lastEventId=e&&e.event_id?e.event_id:K(),r=new Error("Sentry syntheticException");return this.getScope().captureException(t,{originalException:t,syntheticException:r,...e,event_id:n}),n}captureMessage(t,e,n){const r=this._lastEventId=n&&n.event_id?n.event_id:K(),i=new Error(t);return this.getScope().captureMessage(t,e,{originalException:t,syntheticException:i,...n,event_id:r}),r}captureEvent(t,e){const n=e&&e.event_id?e.event_id:K();return t.type||(this._lastEventId=n),this.getScope().captureEvent(t,{...e,event_id:n}),n}lastEventId(){return this._lastEventId}addBreadcrumb(t,e){const{scope:n,client:r}=this.getStackTop();if(!r)return;const{beforeBreadcrumb:i=null,maxBreadcrumbs:o=Gt}=r.getOptions&&r.getOptions()||{};if(o<=0)return;const s={timestamp:it(),...t},a=i?D((()=>i(s,e))):s;null!==a&&(r.emit&&r.emit("beforeAddBreadcrumb",a,e),n.addBreadcrumb(a,o))}setUser(t){this.getScope().setUser(t),this.getIsolationScope().setUser(t)}setTags(t){this.getScope().setTags(t),this.getIsolationScope().setTags(t)}setExtras(t){this.getScope().setExtras(t),this.getIsolationScope().setExtras(t)}setTag(t,e){this.getScope().setTag(t,e),this.getIsolationScope().setTag(t,e)}setExtra(t,e){this.getScope().setExtra(t,e),this.getIsolationScope().setExtra(t,e)}setContext(t,e){this.getScope().setContext(t,e),this.getIsolationScope().setContext(t,e)}configureScope(t){const{scope:e,client:n}=this.getStackTop();n&&t(e)}run(t){const e=Kt(this);try{t(this)}finally{Kt(e)}}getIntegration(t){const e=this.getClient();if(!e)return null;try{return e.getIntegration(t)}catch(e){return V&&N.warn(`Cannot retrieve integration ${t.id} from the current Hub`),null}}startTransaction(t,e){const n=this._callExtensionMethod("startTransaction",t,e);return V&&!n&&(this.getClient()?N.warn("Tracing extension 'startTransaction' has not been added. Call 'addTracingExtensions' before calling 'init':\nSentry.addTracingExtensions();\nSentry.init({...});\n"):N.warn("Tracing extension 'startTransaction' is missing. You should 'init' the SDK before calling 'startTransaction'")),n}traceHeaders(){return this._callExtensionMethod("traceHeaders")}captureSession(t=!1){if(t)return this.endSession();this._sendSessionUpdate()}endSession(){const t=this.getStackTop().scope,e=t.getSession();e&&yt(e),this._sendSessionUpdate(),t.setSession()}startSession(t){const{scope:e,client:n}=this.getStackTop(),{release:r,environment:i=J}=n&&n.getOptions()||{},{userAgent:o}=b.navigator||{},s=mt({release:r,environment:i,user:e.getUser(),...o&&{userAgent:o},...t}),a=e.getSession&&e.getSession();return a&&"ok"===a.status&&gt(a,{status:"exited"}),this.endSession(),e.setSession(s),s}shouldSendDefaultPii(){const t=this.getClient(),e=t&&t.getOptions();return Boolean(e&&e.sendDefaultPii)}_sendSessionUpdate(){const{scope:t,client:e}=this.getStackTop(),n=t.getSession();n&&e&&e.captureSession&&e.captureSession(n)}_callExtensionMethod(t,...e){const n=Vt().__SENTRY__;if(n&&n.extensions&&"function"==typeof n.extensions[t])return n.extensions[t].apply(this,e);V&&N.warn(`Extension method ${t} couldn't be found, doing nothing.`)}}function Vt(){return b.__SENTRY__=b.__SENTRY__||{extensions:{},hub:void 0},b}function Kt(t){const e=Vt(),n=te(e);return ee(e,t),n}function Xt(){const t=Vt();if(t.__SENTRY__&&t.__SENTRY__.acs){const e=t.__SENTRY__.acs.getCurrentHub();if(e)return e}return function(t=Vt()){return e=t,!!(e&&e.__SENTRY__&&e.__SENTRY__.hub)&&!te(t).isOlderThan(Yt)||ee(t,new Jt),te(t);var e}(t)}function Qt(){return Xt().getIsolationScope()}function Zt(t,e={}){const n=Vt();return n.__SENTRY__&&n.__SENTRY__.acs?n.__SENTRY__.acs.runWithAsyncContext(t,e):t()}function te(t){return w("hub",(()=>new Jt),t)}function ee(t,e){return!!t&&((t.__SENTRY__=t.__SENTRY__||{}).hub=e,!0)}const ne=50,re=/\(error: (.*)\)/,ie=/captureMessage|captureException/;function oe(...t){const e=t.sort(((t,e)=>t[0]-e[0])).map((t=>t[1]));return(t,n=0)=>{const r=[],i=t.split("\n");for(let t=n;t<i.length;t++){const n=i[t];if(n.length>1024)continue;const o=re.test(n)?n.replace(re,"$1"):n;if(!o.match(/\S*Error: /)){for(const t of e){const e=t(o);if(e){r.push(e);break}}if(r.length>=ne)break}}return function(t){if(!t.length)return[];const e=Array.from(t);return/sentryWrapped/.test(e[e.length-1].function||"")&&e.pop(),e.reverse(),ie.test(e[e.length-1].function||"")&&(e.pop(),ie.test(e[e.length-1].function||"")&&e.pop()),e.slice(0,ne).map((t=>({...t,filename:t.filename||e[e.length-1].filename,function:t.function||"?"})))}(r)}}const se="<anonymous>";function ae(t){try{return t&&"function"==typeof t&&t.name||se}catch(t){return se}}function ce(t,e=100,n=1/0){try{return le("",t,e,n)}catch(t){return{ERROR:`**non-serializable** (${t})`}}}function ue(t,e=3,n=102400){const r=ce(t,e);return i=r,function(t){return~-encodeURI(t).split(/%..|./).length}(JSON.stringify(i))>n?ue(t,e-1,n):r;var i}function le(t,e,r=1/0,i=1/0,o=function(){const t="function"==typeof WeakSet,e=t?new WeakSet:[];return[function(n){if(t)return!!e.has(n)||(e.add(n),!1);for(let t=0;t<e.length;t++)if(e[t]===n)return!0;return e.push(n),!1},function(n){if(t)e.delete(n);else for(let t=0;t<e.length;t++)if(e[t]===n){e.splice(t,1);break}}]}()){const[s,a]=o;if(null==e||["number","boolean","string"].includes(typeof e)&&!g(e))return e;const c=function(t,e){try{if("domain"===t&&e&&"object"==typeof e&&e._events)return"[Domain]";if("domainEmitter"===t)return"[DomainEmitter]";if(void 0!==n.g&&e===n.g)return"[Global]";if("undefined"!=typeof window&&e===window)return"[Window]";if("undefined"!=typeof document&&e===document)return"[Document]";if(v(e))return"[VueViewModel]";if(h(r=e)&&"nativeEvent"in r&&"preventDefault"in r&&"stopPropagation"in r)return"[SyntheticEvent]";if("number"==typeof e&&e!=e)return"[NaN]";if("function"==typeof e)return`[Function: ${ae(e)}]`;if("symbol"==typeof e)return`[${String(e)}]`;if("bigint"==typeof e)return`[BigInt: ${String(e)}]`;const i=function(t){const e=Object.getPrototypeOf(t);return e?e.constructor.name:"null prototype"}(e);return/^HTML(\w*)Element$/.test(i)?`[HTMLElement: ${i}]`:`[object ${i}]`}catch(t){return`**non-serializable** (${t})`}var r}(t,e);if(!c.startsWith("[object "))return c;if(e.__sentry_skip_normalization__)return e;const u="number"==typeof e.__sentry_override_normalization_depth__?e.__sentry_override_normalization_depth__:r;if(0===u)return c.replace("object ","");if(s(e))return"[Circular ~]";const l=e;if(l&&"function"==typeof l.toJSON)try{return le("",l.toJSON(),u-1,i,o)}catch(t){}const d=Array.isArray(e)?[]:{};let p=0;const f=q(e);for(const t in f){if(!Object.prototype.hasOwnProperty.call(f,t))continue;if(p>=i){d[t]="[MaxProperties ~]";break}const e=f[t];d[t]=le(t,e,u-1,i,o),p++}return a(e),d}function de(t,e,n,r,i,o){const{normalizeDepth:s=3,normalizeMaxBreadth:a=1e3}=t,c={...e,event_id:e.event_id||n.event_id||K(),timestamp:e.timestamp||it()},u=n.integrations||t.integrations.map((t=>t.name));!function(t,e){const{environment:n,release:r,dist:i,maxValueLength:o=250}=e;"environment"in t||(t.environment="environment"in e?n:J),void 0===t.release&&void 0!==r&&(t.release=r),void 0===t.dist&&void 0!==i&&(t.dist=i),t.message&&(t.message=L(t.message,o));const s=t.exception&&t.exception.values&&t.exception.values[0];s&&s.value&&(s.value=L(s.value,o));const a=t.request;a&&a.url&&(a.url=L(a.url,o))}(c,t),function(t,e){e.length>0&&(t.sdk=t.sdk||{},t.sdk.integrations=[...t.sdk.integrations||[],...e])}(c,u),void 0===e.type&&function(t,e){const n=b._sentryDebugIds;if(!n)return;let r;const i=pe.get(e);i?r=i:(r=new Map,pe.set(e,r));const o=Object.keys(n).reduce(((t,i)=>{let o;const s=r.get(i);s?o=s:(o=e(i),r.set(i,o));for(let e=o.length-1;e>=0;e--){const r=o[e];if(r.filename){t[r.filename]=n[i];break}}return t}),{});try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.filename&&(t.debug_id=o[t.filename])}))}))}catch(t){}}(c,t.stackParser);const l=function(t,e){if(!e)return t;const n=t?t.clone():new qt;return n.update(e),n}(r,n.captureContext);n.mechanism&&tt(c,n.mechanism);const d=i&&i.getEventProcessors?i.getEventProcessors():[],p=(Ut||(Ut=new qt),Ut).getScopeData();o&&Ht(p,o.getScopeData()),l&&Ht(p,l.getScopeData());const h=[...n.attachments||[],...p.attachments];return h.length&&(n.attachments=h),$t(c,p),ft([...d,...pt(),...p.eventProcessors],c,n).then((t=>(t&&function(t){const e={};try{t.exception.values.forEach((t=>{t.stacktrace.frames.forEach((t=>{t.debug_id&&(t.abs_path?e[t.abs_path]=t.debug_id:t.filename&&(e[t.filename]=t.debug_id),delete t.debug_id)}))}))}catch(t){}if(0===Object.keys(e).length)return;t.debug_meta=t.debug_meta||{},t.debug_meta.images=t.debug_meta.images||[];const n=t.debug_meta.images;Object.keys(e).forEach((t=>{n.push({type:"sourcemap",code_file:t,debug_id:e[t]})}))}(t),"number"==typeof s&&s>0?function(t,e,n){if(!t)return null;const r={...t,...t.breadcrumbs&&{breadcrumbs:t.breadcrumbs.map((t=>({...t,...t.data&&{data:ce(t.data,e,n)}})))},...t.user&&{user:ce(t.user,e,n)},...t.contexts&&{contexts:ce(t.contexts,e,n)},...t.extra&&{extra:ce(t.extra,e,n)}};return t.contexts&&t.contexts.trace&&r.contexts&&(r.contexts.trace=t.contexts.trace,t.contexts.trace.data&&(r.contexts.trace.data=ce(t.contexts.trace.data,e,n))),t.spans&&(r.spans=t.spans.map((t=>{const r=Lt(t).data;return r&&(t.data=ce(r,e,n)),t}))),r}(t,s,a):t)))}const pe=new WeakMap;const he=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function fe(t,e){return Xt().captureException(t,function(t){if(t)return function(t){return t instanceof qt||"function"==typeof t}(t)||function(t){return Object.keys(t).some((t=>he.includes(t)))}(t)?{captureContext:t}:t}(e))}function me(t,e){const n="string"==typeof e?e:void 0,r="string"!=typeof e?{captureContext:e}:void 0;return Xt().captureMessage(t,n,r)}function ge(t,e){return Xt().captureEvent(t,e)}function ye(t){Xt().configureScope(t)}function ve(t,e){Xt().addBreadcrumb(t,e)}function _e(t,e){Xt().setContext(t,e)}function be(t){Xt().setExtras(t)}function Se(t,e){Xt().setExtra(t,e)}function we(t){Xt().setTags(t)}function ke(t,e){Xt().setTag(t,e)}function xe(t){Xt().setUser(t)}function Ce(...t){const e=Xt();if(2===t.length){const[n,r]=t;return n?e.withScope((()=>(e.getStackTop().scope=n,r(n)))):e.withScope(r)}return e.withScope(t[0])}function Ee(t){return Zt((()=>t(Qt())))}function Te(t,e){return Ce((n=>(n.setSpan(t),e(n))))}function Ie(t,e){return Xt().startTransaction({...t},e)}async function Re(t){const e=Ae();return e?e.flush(t):(V&&N.warn("Cannot flush events. No client defined."),Promise.resolve(!1))}async function Oe(t){const e=Ae();return e?e.close(t):(V&&N.warn("Cannot flush events and disable SDK. No client defined."),Promise.resolve(!1))}function Me(){return Xt().lastEventId()}function Ae(){return Xt().getClient()}function De(){return!!Ae()}function Ne(){return Xt().getScope()}function Le(t){const e=Ae(),n=Qt(),r=Ne(),{release:i,environment:o=J}=e&&e.getOptions()||{},{userAgent:s}=b.navigator||{},a=mt({release:i,environment:o,user:r.getUser()||n.getUser(),...s&&{userAgent:s},...t}),c=n.getSession();return c&&"ok"===c.status&&gt(c,{status:"exited"}),Pe(),n.setSession(a),r.setSession(a),a}function Pe(){const t=Qt(),e=Ne(),n=e.getSession()||t.getSession();n&&yt(n),Fe(),t.setSession(),e.setSession()}function Fe(){const t=Qt(),e=Ne(),n=Ae(),r=e.getSession()||t.getSession();r&&n&&n.captureSession&&n.captureSession(r)}function je(t=!1){t?Pe():Fe()}const $e=[];function He(t){const e=t.defaultIntegrations||[],n=t.integrations;let r;e.forEach((t=>{t.isDefaultInstance=!0})),r=Array.isArray(n)?[...e,...n]:"function"==typeof n?nt(n(e)):e;const i=function(t){const e={};return t.forEach((t=>{const{name:n}=t,r=e[n];r&&!r.isDefaultInstance&&t.isDefaultInstance||(e[n]=t)})),Object.keys(e).map((t=>e[t]))}(r),o=function(t,e){for(let e=0;e<t.length;e++)if(!0==("Debug"===t[e].name))return e;return-1}(i);if(-1!==o){const[t]=i.splice(o,1);i.push(t)}return i}function Be(t,e){for(const n of e)n&&n.afterAllSetup&&n.afterAllSetup(t)}function Ue(t,e,n){if(n[e.name])V&&N.log(`Integration skipped because it was already installed: ${e.name}`);else{if(n[e.name]=e,-1===$e.indexOf(e.name)&&(e.setupOnce(ht,Xt),$e.push(e.name)),e.setup&&"function"==typeof e.setup&&e.setup(t),t.on&&"function"==typeof e.preprocessEvent){const n=e.preprocessEvent.bind(e);t.on("preprocessEvent",((e,r)=>n(e,r,t)))}if(t.addEventProcessor&&"function"==typeof e.processEvent){const n=e.processEvent.bind(e),r=Object.assign(((e,r)=>n(e,r,t)),{id:e.name});t.addEventProcessor(r)}V&&N.log(`Integration installed: ${e.name}`)}}function qe(t){const e=Ae();e&&e.addIntegration?e.addIntegration(t):V&&N.warn(`Cannot add integration "${t.name}" because no SDK Client is available.`)}function ze(t,e){return Object.assign((function(...t){return e(...t)}),{id:t})}let We;const Ye="FunctionToString",Ge=new WeakMap,Je=()=>({name:Ye,setupOnce(){We=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=U(this),n=Ge.has(Ae())&&void 0!==e?e:this;return We.apply(n,t)}}catch(t){}},setup(t){Ge.set(t,!0)}}),Ve=ze(Ye,Je),Ke=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/],Xe=[/^.*\/healthcheck$/,/^.*\/healthy$/,/^.*\/live$/,/^.*\/ready$/,/^.*\/heartbeat$/,/^.*\/health$/,/^.*\/healthz$/],Qe="InboundFilters",Ze=(t={})=>({name:Qe,setupOnce(){},processEvent(e,n,r){const i=r.getOptions(),o=function(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:Ke],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[],...t.disableTransactionDefaults?[]:Xe],ignoreInternal:void 0===t.ignoreInternal||t.ignoreInternal}}(t,i);return function(t,e){return e.ignoreInternal&&function(t){try{return"SentryError"===t.exception.values[0].type}catch(t){}return!1}(t)?(V&&N.warn(`Event dropped due to being internal Sentry Error.\nEvent: ${Q(t)}`),!0):function(t,e){return!(t.type||!e||!e.length)&&function(t){const e=[];let n;t.message&&e.push(t.message);try{n=t.exception.values[t.exception.values.length-1]}catch(t){}return n&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`)),V&&0===e.length&&N.error(`Could not extract message for event ${Q(t)}`),e}(t).some((t=>j(t,e)))}(t,e.ignoreErrors)?(V&&N.warn(`Event dropped due to being matched by \`ignoreErrors\` option.\nEvent: ${Q(t)}`),!0):function(t,e){if("transaction"!==t.type||!e||!e.length)return!1;const n=t.transaction;return!!n&&j(n,e)}(t,e.ignoreTransactions)?(V&&N.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.\nEvent: ${Q(t)}`),!0):function(t,e){if(!e||!e.length)return!1;const n=en(t);return!!n&&j(n,e)}(t,e.denyUrls)?(V&&N.warn(`Event dropped due to being matched by \`denyUrls\` option.\nEvent: ${Q(t)}.\nUrl: ${en(t)}`),!0):!function(t,e){if(!e||!e.length)return!0;const n=en(t);return!n||j(n,e)}(t,e.allowUrls)&&(V&&N.warn(`Event dropped due to not being matched by \`allowUrls\` option.\nEvent: ${Q(t)}.\nUrl: ${en(t)}`),!0)}(e,o)?null:e}}),tn=ze(Qe,Ze);function en(t){try{let e;try{e=t.exception.values[0].stacktrace.frames}catch(t){}return e?function(t=[]){for(let e=t.length-1;e>=0;e--){const n=t[e];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(e):null}catch(e){return V&&N.error(`Cannot extract url for event ${Q(t)}`),null}}function nn(t,e,n=250,r,i,o,s){if(!(o.exception&&o.exception.values&&s&&y(s.originalException,Error)))return;const a=o.exception.values.length>0?o.exception.values[o.exception.values.length-1]:void 0;var c,u;a&&(o.exception.values=(c=rn(t,e,i,s.originalException,r,o.exception.values,a,0),u=n,c.map((t=>(t.value&&(t.value=L(t.value,u)),t)))))}function rn(t,e,n,r,i,o,s,a){if(o.length>=n+1)return o;let c=[...o];if(y(r[i],Error)){on(s,a);const o=t(e,r[i]),u=c.length;sn(o,i,u,a),c=rn(t,e,n,r[i],i,[o,...c],o,u)}return Array.isArray(r.errors)&&r.errors.forEach(((r,o)=>{if(y(r,Error)){on(s,a);const u=t(e,r),l=c.length;sn(u,`errors[${o}]`,l,a),c=rn(t,e,n,r,i,[u,...c],u,l)}})),c}function on(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,..."AggregateError"===t.type&&{is_exception_group:!0},exception_id:e}}function sn(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function an(t,e){const n={type:e.name||e.constructor.name,value:e.message},r=function(t,e){return t(e.stack||"",1)}(t,e);return r.length&&(n.stacktrace={frames:r}),n}const cn="LinkedErrors",un=ze(cn,((t={})=>{const e=t.limit||5,n=t.key||"cause";return{name:cn,setupOnce(){},preprocessEvent(t,r,i){const o=i.getOptions();nn(an,o.stackParser,o.maxValueLength,n,e,t,r)}}})),ln=r,dn=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function pn(t,e=!1){const{host:n,path:r,pass:i,port:o,projectId:s,protocol:a,publicKey:c}=t;return`${a}://${c}${e&&i?`:${i}`:""}@${n}${o?`:${o}`:""}/${r?`${r}/`:r}${s}`}function hn(t){const e=dn.exec(t);if(!e)return void D((()=>{console.error(`Invalid Sentry Dsn: ${t}`)}));const[n,r,i="",o,s="",a]=e.slice(1);let c="",u=a;const l=u.split("/");if(l.length>1&&(c=l.slice(0,-1).join("/"),u=l.pop()),u){const t=u.match(/^\d+/);t&&(u=t[0])}return fn({host:o,pass:i,path:c,projectId:u,port:s,protocol:n,publicKey:r})}function fn(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function mn(t){const e="string"==typeof t?hn(t):fn(t);if(e&&function(t){if(!O)return!0;const{port:e,projectId:n,protocol:r}=t;return!(["protocol","publicKey","host","projectId"].find((e=>!t[e]&&(N.error(`Invalid Sentry Dsn: ${e} missing`),!0)))||(n.match(/^\d+$/)?function(t){return"http"===t||"https"===t}(r)?e&&isNaN(parseInt(e,10))&&(N.error(`Invalid Sentry Dsn: Invalid port ${e}`),1):(N.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),1):(N.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),1)))}(e))return e}function gn(t,e=[]){return[t,e]}function yn(t,e){const[n,r]=t;return[n,[...r,e]]}function vn(t,e){const n=t[1];for(const t of n)if(e(t,t[0].type))return!0;return!1}function _n(t,e){return(e||new TextEncoder).encode(t)}function bn(t,e){const[n,r]=t;let i=JSON.stringify(n);function o(t){"string"==typeof i?i="string"==typeof t?i+t:[_n(i,e),t]:i.push("string"==typeof t?_n(t,e):t)}for(const t of r){const[e,n]=t;if(o(`\n${JSON.stringify(e)}\n`),"string"==typeof n||n instanceof Uint8Array)o(n);else{let t;try{t=JSON.stringify(n)}catch(e){t=JSON.stringify(ce(n))}o(t)}}return"string"==typeof i?i:function(t){const e=t.reduce(((t,e)=>t+e.length),0),n=new Uint8Array(e);let r=0;for(const e of t)n.set(e,r),r+=e.length;return n}(i)}function Sn(t,e){const n="string"==typeof t.data?_n(t.data,e):t.data;return[Y({type:"attachment",length:n.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType}),n]}const wn={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket"};function kn(t){return wn[t]}function xn(t){if(!t||!t.sdk)return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function Cn(t,e,n,r){const i=t.sdkProcessingMetadata&&t.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:t.event_id,sent_at:(new Date).toISOString(),...e&&{sdk:e},...!!n&&r&&{dsn:pn(r)},...i&&{trace:Y({...i})}}}const En=new Map,Tn=new Set;function In(t,e){return function(t){if(b._sentryModuleMetadata)for(const e of Object.keys(b._sentryModuleMetadata)){const n=b._sentryModuleMetadata[e];if(Tn.has(e))continue;Tn.add(e);const r=t(e);for(const t of r.reverse())if(t.filename){En.set(t.filename,n);break}}}(t),En.get(e)}const Rn="ModuleMetadata",On=()=>({name:Rn,setupOnce(){},setup(t){"function"==typeof t.on&&t.on("beforeEnvelope",(t=>{vn(t,((t,e)=>{if("event"===e){const e=Array.isArray(t)?t[1]:void 0;e&&(function(t){try{t.exception.values.forEach((t=>{if(t.stacktrace)for(const e of t.stacktrace.frames||[])delete e.module_metadata}))}catch(t){}}(e),t[1]=e)}}))}))},processEvent:(t,e,n)=>(function(t,e){try{e.exception.values.forEach((e=>{if(e.stacktrace)for(const n of e.stacktrace.frames||[]){if(!n.filename)continue;const e=In(t,n.filename);e&&(n.module_metadata=e)}}))}catch(t){}}(n.getOptions().stackParser,t),t)}),Mn=ze(Rn,On),An="sentry.source",Dn="sentry.sample_rate",Nn="sentry.op",Ln="sentry.origin";class Pn extends Error{constructor(t,e="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=e}}const Fn="7";function jn(t){const e=t.protocol?`${t.protocol}:`:"",n=t.port?`:${t.port}`:"";return`${e}//${t.host}${n}${t.path?`/${t.path}`:""}/api/`}function $n(t,e={}){const n="string"==typeof e?e:e.tunnel,r="string"!=typeof e&&e._metadata?e._metadata.sdk:void 0;return n||`${function(t){return`${jn(t)}${t.projectId}/envelope/`}(t)}?${function(t,e){return n={sentry_key:t.publicKey,sentry_version:Fn,...e&&{sentry_client:`${e.name}/${e.version}`}},Object.keys(n).map((t=>`${encodeURIComponent(t)}=${encodeURIComponent(n[t])}`)).join("&");var n}(t,r)}`}function Hn(t,e,n,r){const i=xn(n),o=t.type&&"replay_event"!==t.type?t.type:"event";!function(t,e){e&&(t.sdk=t.sdk||{},t.sdk.name=t.sdk.name||e.name,t.sdk.version=t.sdk.version||e.version,t.sdk.integrations=[...t.sdk.integrations||[],...e.integrations||[]],t.sdk.packages=[...t.sdk.packages||[],...e.packages||[]])}(t,n&&n.sdk);const s=Cn(t,i,r,e);return delete t.sdkProcessingMetadata,gn(s,[[{type:o},t]])}function Bn(t){return t.replace(/[^\w\-./]+/gi,"")}const Un=[["\n","\\n"],["\r","\\r"],["\t","\\t"],["\\","\\\\"],["|","\\u{7c}"],[",","\\u{2c}"]];function qn(t){return[...t].reduce(((t,e)=>t+function(t){for(const[e,n]of Un)if(t===e)return n;return t}(e)),"")}const zn="Not capturing exception because it's already been captured.";class Wn{constructor(t){if(this._options=t,this._integrations={},this._integrationsInitialized=!1,this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=mn(t.dsn):V&&N.warn("No DSN provided, client will not send events."),this._dsn){const e=$n(this._dsn,t);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:e})}}captureException(t,e,n){if(et(t))return void(V&&N.log(zn));let r=e&&e.event_id;return this._process(this.eventFromException(t,e).then((t=>this._captureEvent(t,e,n))).then((t=>{r=t}))),r}captureMessage(t,e,n,r){let i=n&&n.event_id;const o=d(t)?t:String(t),s=p(t)?this.eventFromMessage(o,e,n):this.eventFromException(t,n);return this._process(s.then((t=>this._captureEvent(t,n,r))).then((t=>{i=t}))),i}captureEvent(t,e,n){if(e&&e.originalException&&et(e.originalException))return void(V&&N.log(zn));let r=e&&e.event_id;const i=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(t,e,i||n).then((t=>{r=t}))),r}captureSession(t){"string"!=typeof t.release?V&&N.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),gt(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const e=this._transport;return e?(this.metricsAggregator&&this.metricsAggregator.flush(),this._isClientDoneProcessing(t).then((n=>e.flush(t).then((t=>n&&t))))):ut(!0)}close(t){return this.flush(t).then((t=>(this.getOptions().enabled=!1,this.metricsAggregator&&this.metricsAggregator.close(),t)))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}setupIntegrations(t){(t&&!this._integrationsInitialized||this._isEnabled()&&!this._integrationsInitialized)&&this._setupIntegrations()}init(){this._isEnabled()&&this._setupIntegrations()}getIntegrationById(t){return this.getIntegrationByName(t)}getIntegrationByName(t){return this._integrations[t]}getIntegration(t){try{return this._integrations[t.id]||null}catch(e){return V&&N.warn(`Cannot retrieve integration ${t.id} from the current Client`),null}}addIntegration(t){const e=this._integrations[t.name];Ue(this,t,this._integrations),e||Be(this,[t])}sendEvent(t,e={}){this.emit("beforeSendEvent",t,e);let n=Hn(t,this._dsn,this._options._metadata,this._options.tunnel);for(const t of e.attachments||[])n=yn(n,Sn(t,this._options.transportOptions&&this._options.transportOptions.textEncoder));const r=this._sendEnvelope(n);r&&r.then((e=>this.emit("afterSendEvent",t,e)),null)}sendSession(t){const e=function(t,e,n,r){const i=xn(n);return gn({sent_at:(new Date).toISOString(),...i&&{sdk:i},...!!r&&e&&{dsn:pn(e)}},["aggregates"in t?[{type:"sessions"},t]:[{type:"session"},t.toJSON()]])}(t,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(e)}recordDroppedEvent(t,e,n){if(this._options.sendClientReports){const r="number"==typeof n?n:1,i=`${t}:${e}`;V&&N.log(`Recording outcome: "${i}"${r>1?` (${r} times)`:""}`),this._outcomes[i]=(this._outcomes[i]||0)+r}}captureAggregateMetrics(t){V&&N.log(`Flushing aggregated metrics, number of metrics: ${t.length}`);const e=function(t,e,n,r){const i={sent_at:(new Date).toISOString()};n&&n.sdk&&(i.sdk={name:n.sdk.name,version:n.sdk.version}),r&&e&&(i.dsn=pn(e));const o=function(t){const e=function(t){let e="";for(const n of t){const t=Object.entries(n.tags),r=t.length>0?`|#${t.map((([t,e])=>`${t}:${e}`)).join(",")}`:"";e+=`${n.name}@${n.unit}:${n.metric}|${n.metricType}${r}|T${n.timestamp}\n`}return e}(t);return[{type:"statsd",length:e.length},e]}(t);return gn(i,[o])}(t,this._dsn,this._options._metadata,this._options.tunnel);this._sendEnvelope(e)}on(t,e){this._hooks[t]||(this._hooks[t]=[]),this._hooks[t].push(e)}emit(t,...e){this._hooks[t]&&this._hooks[t].forEach((t=>t(...e)))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=function(t,e){const n={};return e.forEach((e=>{e&&Ue(t,e,n)})),n}(this,t),Be(this,t),this._integrationsInitialized=!0}_updateSessionFromEvent(t,e){let n=!1,r=!1;const i=e.exception&&e.exception.values;if(i){r=!0;for(const t of i){const e=t.mechanism;if(e&&!1===e.handled){n=!0;break}}}const o="ok"===t.status;(o&&0===t.errors||o&&n)&&(gt(t,{...n&&{status:"crashed"},errors:t.errors||Number(r||n)}),this.captureSession(t))}_isClientDoneProcessing(t){return new dt((e=>{let n=0;const r=setInterval((()=>{0==this._numProcessing?(clearInterval(r),e(!0)):(n+=1,t&&n>=t&&(clearInterval(r),e(!1)))}),1)}))}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(t,e,n,r=Qt()){const i=this.getOptions(),o=Object.keys(this._integrations);return!e.integrations&&o.length>0&&(e.integrations=o),this.emit("preprocessEvent",t,e),de(i,t,e,n,this,r).then((t=>{if(null===t)return t;const e={...r.getPropagationContext(),...n?n.getPropagationContext():void 0};if((!t.contexts||!t.contexts.trace)&&e){const{traceId:r,spanId:i,parentSpanId:o,dsc:s}=e;t.contexts={trace:{trace_id:r,span_id:i,parent_span_id:o},...t.contexts};const a=s||Ft(r,this,n);t.sdkProcessingMetadata={dynamicSamplingContext:a,...t.sdkProcessingMetadata}}return t}))}_captureEvent(t,e={},n){return this._processEvent(t,e,n).then((t=>t.event_id),(t=>{if(V){const e=t;"log"===e.logLevel?N.log(e.message):N.warn(e)}}))}_processEvent(t,e,n){const r=this.getOptions(),{sampleRate:i}=r,o=Gn(t),s=Yn(t),a=t.type||"error",c=`before send for type \`${a}\``;if(s&&"number"==typeof i&&Math.random()>i)return this.recordDroppedEvent("sample_rate","error",t),lt(new Pn(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));const u="replay_event"===a?"replay":a,l=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(t,e,n,l).then((n=>{if(null===n)throw this.recordDroppedEvent("event_processor",u,t),new Pn("An event processor returned `null`, will not send event.","log");if(e.data&&!0===e.data.__sentry__)return n;const i=function(t,e,n){const{beforeSend:r,beforeSendTransaction:i}=t;if(Yn(e)&&r)return r(e,n);if(Gn(e)&&i){if(e.spans){const t=e.spans.length;e.sdkProcessingMetadata={...e.sdkProcessingMetadata,spanCountBeforeProcessing:t}}return i(e,n)}return e}(r,n,e);return function(t,e){const n=`${e} must return \`null\` or a valid event.`;if(m(t))return t.then((t=>{if(!h(t)&&null!==t)throw new Pn(n);return t}),(t=>{throw new Pn(`${e} rejected with ${t}`)}));if(!h(t)&&null!==t)throw new Pn(n);return t}(i,c)})).then((r=>{if(null===r){if(this.recordDroppedEvent("before_send",u,t),o){const e=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",e)}throw new Pn(`${c} returned \`null\`, will not send event.`,"log")}const i=n&&n.getSession();if(!o&&i&&this._updateSessionFromEvent(i,r),o){const t=(r.sdkProcessingMetadata&&r.sdkProcessingMetadata.spanCountBeforeProcessing||0)-(r.spans?r.spans.length:0);t>0&&this.recordDroppedEvent("before_send","span",t)}const s=r.transaction_info;if(o&&s&&r.transaction!==t.transaction){const t="custom";r.transaction_info={...s,source:t}}return this.sendEvent(r,e),r})).then(null,(t=>{if(t instanceof Pn)throw t;throw this.captureException(t,{data:{__sentry__:!0},originalException:t}),new Pn(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\nReason: ${t}`)}))}_process(t){this._numProcessing++,t.then((t=>(this._numProcessing--,t)),(t=>(this._numProcessing--,t)))}_sendEnvelope(t){if(this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport)return this._transport.send(t).then(null,(t=>{V&&N.error("Error while sending event:",t)}));V&&N.error("Transport disabled")}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.keys(t).map((e=>{const[n,r]=e.split(":");return{reason:n,category:r,quantity:t[e]}}))}}function Yn(t){return void 0===t.type}function Gn(t){return"transaction"===t.type}function Jn(t){const e=Ae();e&&e.addEventProcessor&&e.addEventProcessor(t)}const Vn={},Kn={};function Xn(t,e){Vn[t]=Vn[t]||[],Vn[t].push(e)}function Qn(t,e){Kn[t]||(e(),Kn[t]=!0)}function Zn(t,e){const n=t&&Vn[t];if(n)for(const r of n)try{r(e)}catch(e){O&&N.error(`Error while triggering instrumentation handler.\nType: ${t}\nName: ${ae(r)}\nError:`,e)}}let tr=null;function er(t){const e="error";Xn(e,t),Qn(e,nr)}function nr(){tr=b.onerror,b.onerror=function(t,e,n,r,i){return Zn("error",{column:r,error:i,line:n,msg:t,url:e}),!(!tr||tr.__SENTRY_LOADER__)&&tr.apply(this,arguments)},b.onerror.__SENTRY_INSTRUMENTED__=!0}let rr=null;function ir(t){const e="unhandledrejection";Xn(e,t),Qn(e,or)}function or(){rr=b.onunhandledrejection,b.onunhandledrejection=function(t){return Zn("unhandledrejection",t),!(rr&&!rr.__SENTRY_LOADER__)||rr.apply(this,arguments)},b.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function sr(t){return(t||Xt()).getScope().getTransaction()}const ar=Tt;let cr=!1;function ur(){const t=sr();if(t){const e="internal_error";V&&N.log(`[Tracing] Transaction: ${e} -> Global error occured`),t.setStatus(e)}}function lr(t,e,n=(()=>{})){let r;try{r=t()}catch(t){throw e(t),n(),t}return function(t,e,n){return m(t)?t.then((t=>(n(),t)),(t=>{throw e(t),n(),t})):(n(),t)}(r,e,n)}function dr(t){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;const e=Ae(),n=t||e&&e.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}function pr(t,e,n=(()=>{}),r=(()=>{})){const i=Xt(),o=Ne(),s=o.getSpan(),a=_r(t),c=vr(i,{parentSpan:s,spanContext:a,forceTransaction:!1,scope:o});return o.setSpan(c),lr((()=>e(c)),(t=>{c&&c.setStatus("internal_error"),n(t,c)}),(()=>{c&&c.end(),o.setSpan(s),r()}))}function hr(t,e){const n=_r(t);return Zt((()=>Ce(t.scope,(r=>{const i=Xt(),o=r.getSpan(),s=t.onlyIfParent&&!o?void 0:vr(i,{parentSpan:o,spanContext:n,forceTransaction:t.forceTransaction,scope:r});return lr((()=>e(s)),(()=>{if(s){const{status:t}=Lt(s);t&&"ok"!==t||s.setStatus("internal_error")}}),(()=>s&&s.end()))}))))}function fr(t,e){const n=_r(t);return Zt((()=>Ce(t.scope,(r=>{const i=Xt(),o=r.getSpan(),s=t.onlyIfParent&&!o?void 0:vr(i,{parentSpan:o,spanContext:n,forceTransaction:t.forceTransaction,scope:r});function a(){s&&s.end()}return lr((()=>e(s,a)),(()=>{if(s&&s.isRecording()){const{status:t}=Lt(s);t&&"ok"!==t||s.setStatus("internal_error")}}))}))))}function mr(t){if(!dr())return;const e=_r(t),n=Xt(),r=t.scope?t.scope.getSpan():gr();if(t.onlyIfParent&&!r)return;const i=(t.scope||Ne()).clone();return vr(n,{parentSpan:r,spanContext:e,forceTransaction:t.forceTransaction,scope:i})}function gr(){return Ne().getSpan()}ur.tag="sentry_tracingErrorCallback";const yr=({sentryTrace:t,baggage:e},n)=>{const r=Ne(),{traceparentData:i,dynamicSamplingContext:o,propagationContext:s}=function(t,e){const n=Tt(t),r=kt(e),{traceId:i,parentSpanId:o,parentSampled:s}=n||{};return n?{traceparentData:n,dynamicSamplingContext:r||{},propagationContext:{traceId:i||K(),parentSpanId:o||K().substring(16),spanId:K().substring(16),sampled:s,dsc:r||{}}}:{traceparentData:n,dynamicSamplingContext:void 0,propagationContext:{traceId:i||K(),spanId:K().substring(16)}}}(t,e);r.setPropagationContext(s),V&&i&&N.log(`[Tracing] Continuing trace ${i.traceId}.`);const a={...i,metadata:Y({dynamicSamplingContext:o})};return n?Zt((()=>n(a))):a};function vr(t,{parentSpan:e,spanContext:n,forceTransaction:r,scope:i}){if(!dr())return;const o=Qt();let s;if(e&&!r)s=e.startChild(n);else if(e){const r=jt(e),{traceId:i,spanId:o}=e.spanContext(),a=Pt(e);s=t.startTransaction({traceId:i,parentSpanId:o,parentSampled:a,...n,metadata:{dynamicSamplingContext:r,...n.metadata}})}else{const{traceId:e,dsc:r,parentSpanId:a,sampled:c}={...o.getPropagationContext(),...i.getPropagationContext()};s=t.startTransaction({traceId:e,parentSpanId:a,parentSampled:c,...n,metadata:{dynamicSamplingContext:r,...n.metadata}})}return i.setSpan(s),function(t,e,n){t&&(H(t,Sr,n),H(t,br,e))}(s,i,o),s}function _r(t){if(t.startTime){const e={...t};return e.startTimestamp=Dt(t.startTime),delete e.startTime,e}return t}const br="_sentryScope",Sr="_sentryIsolationScope";let wr;function kr(t){return wr?wr.get(t):void 0}function xr(t){const e=kr(t);if(!e)return;const n={};for(const[,[t,r]]of e)n[t]||(n[t]=[]),n[t].push(Y(r));return n}var Cr;function Er(t){if(t<400&&t>=100)return"ok";if(t>=400&&t<500)switch(t){case 401:return"unauthenticated";case 403:return"permission_denied";case 404:return"not_found";case 409:return"already_exists";case 413:return"failed_precondition";case 429:return"resource_exhausted";default:return"invalid_argument"}if(t>=500&&t<600)switch(t){case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline_exceeded";default:return"internal_error"}return"unknown_error"}!function(t){t.Ok="ok",t.DeadlineExceeded="deadline_exceeded",t.Unauthenticated="unauthenticated",t.PermissionDenied="permission_denied",t.NotFound="not_found",t.ResourceExhausted="resource_exhausted",t.InvalidArgument="invalid_argument",t.Unimplemented="unimplemented",t.Unavailable="unavailable",t.InternalError="internal_error",t.UnknownError="unknown_error",t.Cancelled="cancelled",t.AlreadyExists="already_exists",t.FailedPrecondition="failed_precondition",t.Aborted="aborted",t.OutOfRange="out_of_range",t.DataLoss="data_loss"}(Cr||(Cr={}));const Tr=Er;function Ir(t,e){t.setTag("http.status_code",String(e)),t.setData("http.response.status_code",e);const n=Er(e);"unknown_error"!==n&&t.setStatus(n)}class Rr{constructor(t=1e3){this._maxlen=t,this.spans=[]}add(t){this.spans.length>this._maxlen?t.spanRecorder=void 0:this.spans.push(t)}}class Or{constructor(t={}){this._traceId=t.traceId||K(),this._spanId=t.spanId||K().substring(16),this._startTime=t.startTimestamp||ot(),this.tags=t.tags?{...t.tags}:{},this.data=t.data?{...t.data}:{},this.instrumenter=t.instrumenter||"sentry",this._attributes={},this.setAttributes({[Ln]:t.origin||"manual",[Nn]:t.op,...t.attributes}),this._name=t.name||t.description,t.parentSpanId&&(this._parentSpanId=t.parentSpanId),"sampled"in t&&(this._sampled=t.sampled),t.status&&(this._status=t.status),t.endTimestamp&&(this._endTime=t.endTimestamp),void 0!==t.exclusiveTime&&(this._exclusiveTime=t.exclusiveTime),this._measurements=t.measurements?{...t.measurements}:{}}get name(){return this._name||""}set name(t){this.updateName(t)}get description(){return this._name}set description(t){this._name=t}get traceId(){return this._traceId}set traceId(t){this._traceId=t}get spanId(){return this._spanId}set spanId(t){this._spanId=t}set parentSpanId(t){this._parentSpanId=t}get parentSpanId(){return this._parentSpanId}get sampled(){return this._sampled}set sampled(t){this._sampled=t}get attributes(){return this._attributes}set attributes(t){this._attributes=t}get startTimestamp(){return this._startTime}set startTimestamp(t){this._startTime=t}get endTimestamp(){return this._endTime}set endTimestamp(t){this._endTime=t}get status(){return this._status}set status(t){this._status=t}get op(){return this._attributes[Nn]}set op(t){this.setAttribute(Nn,t)}get origin(){return this._attributes[Ln]}set origin(t){this.setAttribute(Ln,t)}spanContext(){const{_spanId:t,_traceId:e,_sampled:n}=this;return{spanId:t,traceId:e,traceFlags:n?Ot:0}}startChild(t){const e=new Or({...t,parentSpanId:this._spanId,sampled:this._sampled,traceId:this._traceId});e.spanRecorder=this.spanRecorder,e.spanRecorder&&e.spanRecorder.add(e);const n=vt(this);if(e.transaction=n,V&&n){const r=`[Tracing] Starting '${t&&t.op||"< unknown op >"}' span on transaction '${Lt(e).description||"< unknown name >"}' (${n.spanContext().spanId}).`;N.log(r),this._logMessage=r}return e}setTag(t,e){return this.tags={...this.tags,[t]:e},this}setData(t,e){return this.data={...this.data,[t]:e},this}setAttribute(t,e){void 0===e?delete this._attributes[t]:this._attributes[t]=e}setAttributes(t){Object.keys(t).forEach((e=>this.setAttribute(e,t[e])))}setStatus(t){return this._status=t,this}setHttpStatus(t){return Ir(this,t),this}setName(t){this.updateName(t)}updateName(t){return this._name=t,this}isSuccess(){return"ok"===this._status}finish(t){return this.end(t)}end(t){if(this._endTime)return;const e=vt(this);if(V&&e&&e.spanContext().spanId!==this._spanId){const t=this._logMessage;t&&N.log(t.replace("Starting","Finishing"))}this._endTime=Dt(t)}toTraceparent(){return At(this)}toContext(){return Y({data:this._getData(),description:this._name,endTimestamp:this._endTime,op:this.op,parentSpanId:this._parentSpanId,sampled:this._sampled,spanId:this._spanId,startTimestamp:this._startTime,status:this._status,tags:this.tags,traceId:this._traceId})}updateWithContext(t){return this.data=t.data||{},this._name=t.name||t.description,this._endTime=t.endTimestamp,this.op=t.op,this._parentSpanId=t.parentSpanId,this._sampled=t.sampled,this._spanId=t.spanId||this._spanId,this._startTime=t.startTimestamp||this._startTime,this._status=t.status,this.tags=t.tags||{},this._traceId=t.traceId||this._traceId,this}getTraceContext(){return Mt(this)}getSpanJSON(){return Y({data:this._getData(),description:this._name,op:this._attributes[Nn],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:this._status,tags:Object.keys(this.tags).length>0?this.tags:void 0,timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[Ln],_metrics_summary:xr(this),profile_id:this._attributes.profile_id,exclusive_time:this._exclusiveTime,measurements:Object.keys(this._measurements).length>0?this._measurements:void 0})}isRecording(){return!this._endTime&&!!this._sampled}toJSON(){return this.getSpanJSON()}_getData(){const{data:t,_attributes:e}=this,n=Object.keys(t).length>0,r=Object.keys(e).length>0;if(n||r)return n&&r?{...t,...e}:n?t:e}}class Mr extends Or{constructor(t,e){super(t),this._contexts={},this._hub=e||Xt(),this._name=t.name||"",this._metadata={...t.metadata},this._trimEnd=t.trimEnd,this.transaction=this;const n=this._metadata.dynamicSamplingContext;n&&(this._frozenDynamicSamplingContext={...n})}get name(){return this._name}set name(t){this.setName(t)}get metadata(){return{source:"custom",spanMetadata:{},...this._metadata,...this._attributes[An]&&{source:this._attributes[An]},...this._attributes[Dn]&&{sampleRate:this._attributes[Dn]}}}set metadata(t){this._metadata=t}setName(t,e="custom"){this._name=t,this.setAttribute(An,e)}updateName(t){return this._name=t,this}initSpanRecorder(t=1e3){this.spanRecorder||(this.spanRecorder=new Rr(t)),this.spanRecorder.add(this)}setContext(t,e){null===e?delete this._contexts[t]:this._contexts[t]=e}setMeasurement(t,e,n=""){this._measurements[t]={value:e,unit:n}}setMetadata(t){this._metadata={...this._metadata,...t}}end(t){const e=Dt(t),n=this._finishTransaction(e);if(n)return this._hub.captureEvent(n)}toContext(){return Y({...super.toContext(),name:this._name,trimEnd:this._trimEnd})}updateWithContext(t){return super.updateWithContext(t),this._name=t.name||"",this._trimEnd=t.trimEnd,this}getDynamicSamplingContext(){return jt(this)}setHub(t){this._hub=t}getProfileId(){if(void 0!==this._contexts&&void 0!==this._contexts.profile)return this._contexts.profile.profile_id}_finishTransaction(t){if(void 0!==this._endTime)return;this._name||(V&&N.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>"),super.end(t);const e=this._hub.getClient();if(e&&e.emit&&e.emit("finishTransaction",this),!0!==this._sampled)return V&&N.log("[Tracing] Discarding transaction because its trace was not chosen to be sampled."),void(e&&e.recordDroppedEvent("sample_rate","transaction"));const n=this.spanRecorder?this.spanRecorder.spans.filter((t=>t!==this&&Lt(t).timestamp)):[];if(this._trimEnd&&n.length>0){const t=n.map((t=>Lt(t).timestamp)).filter(Boolean);this._endTime=t.reduce(((t,e)=>t>e?t:e))}const{scope:r,isolationScope:i}={scope:this[br],isolationScope:this[Sr]};const{metadata:o}=this,{source:s}=o,a={contexts:{...this._contexts,trace:Mt(this)},spans:n,start_timestamp:this._startTime,tags:this.tags,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{...o,capturedSpanScope:r,capturedSpanIsolationScope:i,...Y({dynamicSamplingContext:jt(this)})},_metrics_summary:xr(this),...s&&{transaction_info:{source:s}}};return Object.keys(this._measurements).length>0&&(V&&N.log("[Measurements] Adding measurements to transaction",JSON.stringify(this._measurements,void 0,2)),a.measurements=this._measurements),V&&N.log(`[Tracing] Finishing ${this.op} transaction: ${this._name}.`),a}}const Ar={idleTimeout:1e3,finalTimeout:3e4,heartbeatInterval:5e3},Dr=["heartbeatFailed","idleTimeout","documentHidden","finalTimeout","externalFinish","cancelled"];class Nr extends Rr{constructor(t,e,n,r){super(r),this._pushActivity=t,this._popActivity=e,this.transactionSpanId=n}add(t){if(t.spanContext().spanId!==this.transactionSpanId){const e=t.end;t.end=(...n)=>(this._popActivity(t.spanContext().spanId),e.apply(t,n)),void 0===Lt(t).timestamp&&this._pushActivity(t.spanContext().spanId)}super.add(t)}}class Lr extends Mr{constructor(t,e,n=Ar.idleTimeout,r=Ar.finalTimeout,i=Ar.heartbeatInterval,o=!1,s=!1){super(t,e),this._idleHub=e,this._idleTimeout=n,this._finalTimeout=r,this._heartbeatInterval=i,this._onScope=o,this.activities={},this._heartbeatCounter=0,this._finished=!1,this._idleTimeoutCanceledPermanently=!1,this._beforeFinishCallbacks=[],this._finishReason=Dr[4],this._autoFinishAllowed=!s,o&&(V&&N.log(`Setting idle transaction on scope. Span ID: ${this.spanContext().spanId}`),e.getScope().setSpan(this)),s||this._restartIdleTimeout(),setTimeout((()=>{this._finished||(this.setStatus("deadline_exceeded"),this._finishReason=Dr[3],this.end())}),this._finalTimeout)}end(t){const e=Dt(t);if(this._finished=!0,this.activities={},"ui.action.click"===this.op&&this.setAttribute("finishReason",this._finishReason),this.spanRecorder){V&&N.log("[Tracing] finishing IdleTransaction",new Date(1e3*e).toISOString(),this.op);for(const t of this._beforeFinishCallbacks)t(this,e);this.spanRecorder.spans=this.spanRecorder.spans.filter((t=>{if(t.spanContext().spanId===this.spanContext().spanId)return!0;Lt(t).timestamp||(t.setStatus("cancelled"),t.end(e),V&&N.log("[Tracing] cancelling span since transaction ended early",JSON.stringify(t,void 0,2)));const{start_timestamp:n,timestamp:r}=Lt(t),i=n&&n<e,o=(this._finalTimeout+this._idleTimeout)/1e3,s=r&&n&&r-n<o;if(V){const e=JSON.stringify(t,void 0,2);i?s||N.log("[Tracing] discarding Span since it finished after Transaction final timeout",e):N.log("[Tracing] discarding Span since it happened after Transaction was finished",e)}return i&&s})),V&&N.log("[Tracing] flushing IdleTransaction")}else V&&N.log("[Tracing] No active IdleTransaction");if(this._onScope){const t=this._idleHub.getScope();t.getTransaction()===this&&t.setSpan(void 0)}return super.end(t)}registerBeforeFinishCallback(t){this._beforeFinishCallbacks.push(t)}initSpanRecorder(t){if(!this.spanRecorder){const e=t=>{this._finished||this._pushActivity(t)},n=t=>{this._finished||this._popActivity(t)};this.spanRecorder=new Nr(e,n,this.spanContext().spanId,t),V&&N.log("Starting heartbeat"),this._pingHeartbeat()}this.spanRecorder.add(this)}cancelIdleTimeout(t,{restartOnChildSpanChange:e}={restartOnChildSpanChange:!0}){this._idleTimeoutCanceledPermanently=!1===e,this._idleTimeoutID&&(clearTimeout(this._idleTimeoutID),this._idleTimeoutID=void 0,0===Object.keys(this.activities).length&&this._idleTimeoutCanceledPermanently&&(this._finishReason=Dr[5],this.end(t)))}setFinishReason(t){this._finishReason=t}sendAutoFinishSignal(){this._autoFinishAllowed||(V&&N.log("[Tracing] Received finish signal for idle transaction."),this._restartIdleTimeout(),this._autoFinishAllowed=!0)}_restartIdleTimeout(t){this.cancelIdleTimeout(),this._idleTimeoutID=setTimeout((()=>{this._finished||0!==Object.keys(this.activities).length||(this._finishReason=Dr[1],this.end(t))}),this._idleTimeout)}_pushActivity(t){this.cancelIdleTimeout(void 0,{restartOnChildSpanChange:!this._idleTimeoutCanceledPermanently}),V&&N.log(`[Tracing] pushActivity: ${t}`),this.activities[t]=!0,V&&N.log("[Tracing] new activities count",Object.keys(this.activities).length)}_popActivity(t){if(this.activities[t]&&(V&&N.log(`[Tracing] popActivity ${t}`),delete this.activities[t],V&&N.log("[Tracing] new activities count",Object.keys(this.activities).length)),0===Object.keys(this.activities).length){const t=ot();this._idleTimeoutCanceledPermanently?this._autoFinishAllowed&&(this._finishReason=Dr[5],this.end(t)):this._restartIdleTimeout(t+this._idleTimeout/1e3)}}_beat(){if(this._finished)return;const t=Object.keys(this.activities).join("");t===this._prevHeartbeatString?this._heartbeatCounter++:this._heartbeatCounter=1,this._prevHeartbeatString=t,this._heartbeatCounter>=3?this._autoFinishAllowed&&(V&&N.log("[Tracing] Transaction finished because of no change for 3 heart beats"),this.setStatus("deadline_exceeded"),this._finishReason=Dr[0],this.end()):this._pingHeartbeat()}_pingHeartbeat(){V&&N.log(`pinging Heartbeat -> current counter: ${this._heartbeatCounter}`),setTimeout((()=>{this._beat()}),this._heartbeatInterval)}}function Pr(t,e,n){if(!dr(e))return t.sampled=!1,t;if(void 0!==t.sampled)return t.setAttribute(Dn,Number(t.sampled)),t;let r;return"function"==typeof e.tracesSampler?(r=e.tracesSampler(n),t.setAttribute(Dn,Number(r))):void 0!==n.parentSampled?r=n.parentSampled:void 0!==e.tracesSampleRate?(r=e.tracesSampleRate,t.setAttribute(Dn,Number(r))):(r=1,t.setAttribute(Dn,r)),Fr(r)?r?(t.sampled=Math.random()<r,t.sampled?(V&&N.log(`[Tracing] starting ${t.op} transaction - ${Lt(t).description}`),t):(V&&N.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(r)})`),t)):(V&&N.log("[Tracing] Discarding transaction because "+("function"==typeof e.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),t.sampled=!1,t):(V&&N.warn("[Tracing] Discarding transaction because of invalid sample rate."),t.sampled=!1,t)}function Fr(t){return g(t)||"number"!=typeof t&&"boolean"!=typeof t?(V&&N.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(t)} of type ${JSON.stringify(typeof t)}.`),!1):!(t<0||t>1)||(V&&N.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${t}.`),!1)}function jr(){const t=this.getScope().getSpan();return t?{"sentry-trace":At(t)}:{}}function $r(t,e){const n=this.getClient(),r=n&&n.getOptions()||{},i=r.instrumenter||"sentry",o=t.instrumenter||"sentry";i!==o&&(V&&N.error(`A transaction was started with instrumenter=\`${o}\`, but the SDK is configured with the \`${i}\` instrumenter.\nThe transaction will not be sampled. Please use the ${i} instrumentation to start transactions.`),t.sampled=!1);let s=new Mr(t,this);return s=Pr(s,r,{name:t.name,parentSampled:t.parentSampled,transactionContext:t,attributes:{...t.data,...t.attributes},...e}),s.isRecording()&&s.initSpanRecorder(r._experiments&&r._experiments.maxSpans),n&&n.emit&&n.emit("startTransaction",s),s}function Hr(t,e,n,r,i,o,s,a=!1){const c=t.getClient(),u=c&&c.getOptions()||{};let l=new Lr(e,t,n,r,s,i,a);return l=Pr(l,u,{name:e.name,parentSampled:e.parentSampled,transactionContext:e,attributes:{...e.data,...e.attributes},...o}),l.isRecording()&&l.initSpanRecorder(u._experiments&&u._experiments.maxSpans),c&&c.emit&&c.emit("startTransaction",l),l}function Br(){const t=Vt();t.__SENTRY__&&(t.__SENTRY__.extensions=t.__SENTRY__.extensions||{},t.__SENTRY__.extensions.startTransaction||(t.__SENTRY__.extensions.startTransaction=$r),t.__SENTRY__.extensions.traceHeaders||(t.__SENTRY__.extensions.traceHeaders=jr),cr||(cr=!0,er(ur),ir(ur)))}const Ur=6e4;function qr(t,e=Date.now()){const n=parseInt(`${t}`,10);if(!isNaN(n))return 1e3*n;const r=Date.parse(`${t}`);return isNaN(r)?Ur:r-e}function zr(t,e,n=Date.now()){return function(t,e){return t[e]||t.all||0}(t,e)>n}function Wr(t,{statusCode:e,headers:n},r=Date.now()){const i={...t},o=n&&n["x-sentry-rate-limits"],s=n&&n["retry-after"];if(o)for(const t of o.trim().split(",")){const[e,n,,,o]=t.split(":",5),s=parseInt(e,10),a=1e3*(isNaN(s)?60:s);if(n)for(const t of n.split(";"))"metric_bucket"===t&&o&&!o.split(";").includes("custom")||(i[t]=r+a);else i.all=r+a}else s?i.all=r+qr(s,r):429===e&&(i.all=r+6e4);return i}const Yr=30;function Gr(t,e,n=function(t){const e=[];function n(t){return e.splice(e.indexOf(t),1)[0]}return{$:e,add:function(r){if(!(void 0===t||e.length<t))return lt(new Pn("Not adding Promise because buffer limit was reached."));const i=r();return-1===e.indexOf(i)&&e.push(i),i.then((()=>n(i))).then(null,(()=>n(i).then(null,(()=>{})))),i},drain:function(t){return new dt(((n,r)=>{let i=e.length;if(!i)return n(!0);const o=setTimeout((()=>{t&&t>0&&n(!1)}),t);e.forEach((t=>{ut(t).then((()=>{--i||(clearTimeout(o),n(!0))}),r)}))}))}}}(t.bufferSize||Yr)){let r={};function i(i){const o=[];if(vn(i,((e,n)=>{const i=kn(n);if(zr(r,i)){const r=Jr(e,n);t.recordDroppedEvent("ratelimit_backoff",i,r)}else o.push(e)})),0===o.length)return ut();const s=gn(i[0],o),a=e=>{vn(s,((n,r)=>{const i=Jr(n,r);t.recordDroppedEvent(e,kn(r),i)}))};return n.add((()=>e({body:bn(s,t.textEncoder)}).then((t=>(void 0!==t.statusCode&&(t.statusCode<200||t.statusCode>=300)&&V&&N.warn(`Sentry responded with status code ${t.statusCode} to sent event.`),r=Wr(r,t),t)),(t=>{throw a("network_error"),t})))).then((t=>t),(t=>{if(t instanceof Pn)return V&&N.error("Skipped sending event because buffer is full."),a("queue_overflow"),ut();throw t}))}return i.__sentry__baseTransport__=!0,{send:i,flush:t=>n.drain(t)}}function Jr(t,e){if("event"===e||"transaction"===e)return Array.isArray(t)?t[1]:void 0}function Vr(t,e){let n;return vn(t,((t,r)=>(e.includes(r)&&(n=Array.isArray(t)?t[1]:void 0),!!n))),n}function Kr(t,e){return n=>{const r=t(n),i=new Map;function o(e,r){const o=r?`${e}:${r}`:e;let s=i.get(o);if(!s){const a=hn(e);if(!a)return;const c=$n(a,n.tunnel);s=r?function(t,e){return n=>{const r=t(n);return{...r,send:async t=>{const n=Vr(t,["event","transaction","profile","replay_event"]);return n&&(n.release=e),r.send(t)}}}}(t,r)({...n,url:c}):t({...n,url:c}),i.set(o,s)}return[e,s]}return{send:async function(t){const n=e({envelope:t,getEvent:function(e){const n=e&&e.length?e:["event"];return Vr(t,n)}}).map((t=>"string"==typeof t?o(t,void 0):o(t.dsn,t.release))).filter((t=>!!t));return 0===n.length&&n.push(["",r]),(await Promise.all(n.map((([e,n])=>n.send(function(t,e){return gn(e?{...t[0],dsn:e}:t[0],t[1])}(t,e))))))[0]},flush:async function(t){const e=[await r.flush(t)];for(const[,n]of i)e.push(await n.flush(t));return e.every((t=>t))}}}}const Xr={c:class{constructor(t){this._value=t}get weight(){return 1}add(t){this._value+=t}toString(){return`${this._value}`}},g:class{constructor(t){this._last=t,this._min=t,this._max=t,this._sum=t,this._count=1}get weight(){return 5}add(t){this._last=t,t<this._min&&(this._min=t),t>this._max&&(this._max=t),this._sum+=t,this._count++}toString(){return`${this._last}:${this._min}:${this._max}:${this._sum}:${this._count}`}},d:class{constructor(t){this._value=[t]}get weight(){return this._value.length}add(t){this._value.push(t)}toString(){return this._value.join(":")}},s:class{constructor(t){this.first=t,this._value=new Set([t])}get weight(){return this._value.size}add(t){this._value.add(t)}toString(){return Array.from(this._value).map((t=>"string"==typeof t?function(t){let e=0;for(let n=0;n<t.length;n++)e=(e<<5)-e+t.charCodeAt(n),e&=e;return e>>>0}(t):t)).join(":")}}};class Qr{constructor(t){this._client=t,this._buckets=new Map,this._interval=setInterval((()=>this.flush()),5e3)}add(t,e,n,r="none",i={},o=ot()){const s=Math.floor(o),a=e.replace(/[^\w\-.]+/gi,"_"),c=function(t){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[Bn(n)]=qn(String(t[n])));return e}(i),u=function(t){return t.replace(/[^\w]+/gi,"_")}(r),l=function(t,e,n,r){return`${t}${e}${n}${Object.entries(Y(r)).sort(((t,e)=>t[0].localeCompare(e[0])))}`}(t,a,u,c);let d=this._buckets.get(l);const p=d&&"s"===t?d.metric.weight:0;d?(d.metric.add(n),d.timestamp<s&&(d.timestamp=s)):(d={metric:new Xr[t](n),timestamp:s,metricType:t,name:a,unit:u,tags:c},this._buckets.set(l,d)),function(t,e,n,r,i,o){const s=gr();if(s){const a=kr(s)||new Map,c=`${t}:${e}@${r}`,u=a.get(o);if(u){const[,t]=u;a.set(o,[c,{min:Math.min(t.min,n),max:Math.max(t.max,n),count:t.count+=1,sum:t.sum+=n,tags:t.tags}])}else a.set(o,[c,{min:n,max:n,count:1,sum:n,tags:i}]);wr||(wr=new WeakMap),wr.set(s,a)}}(t,a,"string"==typeof n?d.metric.weight-p:n,u,i,l)}flush(){if(0!==this._buckets.size){if(this._client.captureAggregateMetrics){const t=Array.from(this._buckets).map((([,t])=>t));this._client.captureAggregateMetrics(t)}this._buckets.clear()}}close(){clearInterval(this._interval),this.flush()}}const Zr="MetricsAggregator",ti=()=>({name:Zr,setupOnce(){},setup(t){t.metricsAggregator=new Qr(t)}});function ei(t,e,n,r={}){const i=Ae(),o=Ne();if(i){if(!i.metricsAggregator)return void(V&&N.warn("No metrics aggregator enabled. Please add the MetricsAggregator integration to use metrics APIs"));const{unit:s,tags:a,timestamp:c}=r,{release:u,environment:l}=i.getOptions(),d=o.getTransaction(),p={};u&&(p.release=u),l&&(p.environment=l),d&&(p.transaction=Lt(d).description||""),V&&N.log(`Adding value of ${n} to ${t} metric ${e}`),i.metricsAggregator.add(t,e,n,s,{...p,...a},c)}}const ni={increment:function(t,e=1,n){ei("c",t,e,n)},distribution:function(t,e,n){ei("d",t,e,n)},set:function(t,e,n){ei("s",t,e,n)},gauge:function(t,e,n){ei("g",t,e,n)},MetricsAggregator:ze(Zr,ti),metricsAggregatorIntegration:ti};function ri(t,...e){const n=new String(String.raw(t,...e));return n.__sentry_template_string__=t.join("\0").replace(/%/g,"%%").replace(/\0/g,"%s"),n.__sentry_template_values__=e,n}function ii(t){const e=Xt().getStackTop();e.client=t,e.scope.setClient(t)}function oi(t,e,n){const r=sr();r&&r.setMeasurement(t,e,n)}const si=b;let ai=0;function ci(){return ai>0}function ui(t,e={},n){if("function"!=typeof t)return t;try{const e=t.__sentry_wrapped__;if(e)return"function"==typeof e?e:t;if(U(t))return t}catch(e){return t}const r=function(){const r=Array.prototype.slice.call(arguments);try{n&&"function"==typeof n&&n.apply(this,arguments);const i=r.map((t=>ui(t,e)));return t.apply(this,i)}catch(t){throw ai++,setTimeout((()=>{ai--})),Ce((n=>{n.addEventProcessor((t=>(e.mechanism&&(Z(t,void 0,void 0),tt(t,e.mechanism)),t.extra={...t.extra,arguments:r},t))),fe(t)})),t}};try{for(const e in t)Object.prototype.hasOwnProperty.call(t,e)&&(r[e]=t[e])}catch(t){}B(r,t),H(t,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get:()=>t.name})}catch(t){}return r}var li=n(6958);const di="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function pi(t,e){const n=fi(t,e),r={type:e&&e.name,value:gi(e)};return n.length&&(r.stacktrace={frames:n}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function hi(t,e){return{exception:{values:[pi(t,e)]}}}function fi(t,e){const n=e.stacktrace||e.stack||"",r=function(t){if(t){if("number"==typeof t.framesToPop)return t.framesToPop;if(mi.test(t.message))return 1}return 0}(e);try{return t(n,r)}catch(t){}return[]}const mi=/Minified React error #\d+;/i;function gi(t){const e=t&&t.message;return e?e.error&&"string"==typeof e.error.message?e.error.message:e:"No error message"}function yi(t,e,n,r){const i=_i(t,e,n&&n.syntheticException||void 0,r);return tt(i),i.level="error",n&&n.event_id&&(i.event_id=n.event_id),ut(i)}function vi(t,e,n="info",r,i){const o=bi(t,e,r&&r.syntheticException||void 0,i);return o.level=n,r&&r.event_id&&(o.event_id=r.event_id),ut(o)}function _i(t,e,n,r,i){let o;if(c(e)&&e.error)return hi(t,e.error);if(u(e)||a(e,"DOMException")){const i=e;if("stack"in e)o=hi(t,e);else{const e=i.name||(u(i)?"DOMError":"DOMException"),s=i.message?`${e}: ${i.message}`:e;o=bi(t,s,n,r),Z(o,s)}return"code"in i&&(o.tags={...o.tags,"DOMException.code":`${i.code}`}),o}return s(e)?hi(t,e):h(e)||f(e)?(o=function(t,e,n,r){const i=Ae(),o=i&&i.getOptions().normalizeDepth,s={exception:{values:[{type:f(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:Si(e,{isUnhandledRejection:r})}]},extra:{__serialized__:ue(e,o)}};if(n){const e=fi(t,n);e.length&&(s.exception.values[0].stacktrace={frames:e})}return s}(t,e,n,i),tt(o,{synthetic:!0}),o):(o=bi(t,e,n,r),Z(o,`${e}`,void 0),tt(o,{synthetic:!0}),o)}function bi(t,e,n,r){const i={};if(r&&n){const r=fi(t,n);r.length&&(i.exception={values:[{value:e,stacktrace:{frames:r}}]})}if(d(e)){const{__sentry_template_string__:t,__sentry_template_values__:n}=e;return i.logentry={message:t,params:n},i}return i.message=e,i}function Si(t,{isUnhandledRejection:e}){const n=function(t,e=40){const n=Object.keys(q(t));if(n.sort(),!n.length)return"[object has no keys]";if(n[0].length>=e)return L(n[0],e);for(let t=n.length;t>0;t--){const r=n.slice(0,t).join(", ");if(!(r.length>e))return t===n.length?r:L(r,e)}return""}(t),r=e?"promise rejection":"exception";return c(t)?`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``:f(t)?`Event \`${function(t){try{const e=Object.getPrototypeOf(t);return e?e.constructor.name:void 0}catch(t){}}(t)}\` (type=${t.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}function wi(t,{metadata:e,tunnel:n,dsn:r}){const i={event_id:t.event_id,sent_at:(new Date).toISOString(),...e&&e.sdk&&{sdk:{name:e.sdk.name,version:e.sdk.version}},...!!n&&!!r&&{dsn:pn(r)}},o=function(t){return[{type:"user_report"},t]}(t);return gn(i,[o])}class ki extends Wn{constructor(t){!function(t,e,n=[e],r="npm"){const i=t._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${e}`,packages:n.map((t=>({name:`${r}:@sentry/${t}`,version:Wt}))),version:Wt}),t._metadata=i}(t,"browser",["browser"],si.SENTRY_SDK_SOURCE||(0,li.e)()),super(t),t.sendClientReports&&si.document&&si.document.addEventListener("visibilitychange",(()=>{"hidden"===si.document.visibilityState&&this._flushOutcomes()}))}eventFromException(t,e){return yi(this._options.stackParser,t,e,this._options.attachStacktrace)}eventFromMessage(t,e="info",n){return vi(this._options.stackParser,t,e,n,this._options.attachStacktrace)}captureUserFeedback(t){if(!this._isEnabled())return void(di&&N.warn("SDK not enabled, will not capture user feedback."));const e=wi(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this._sendEnvelope(e)}_prepareEvent(t,e,n){return t.platform=t.platform||"javascript",super._prepareEvent(t,e,n)}_flushOutcomes(){const t=this._clearOutcomes();if(0===t.length)return void(di&&N.log("No outcomes to send"));if(!this._dsn)return void(di&&N.log("No dsn provided, will not send outcomes"));di&&N.log("Sending outcomes:",t);const e=(n=t,gn((r=this._options.tunnel&&pn(this._dsn))?{dsn:r}:{},[[{type:"client_report"},{timestamp:it(),discarded_events:n}]]));var n,r;this._sendEnvelope(e)}}const xi=S();function Ci(){if(!("fetch"in xi))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(t){return!1}}function Ei(t){return t&&/^function fetch\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function Ti(){if("string"==typeof EdgeRuntime)return!0;if(!Ci())return!1;if(Ei(xi.fetch))return!0;let t=!1;const e=xi.document;if(e&&"function"==typeof e.createElement)try{const n=e.createElement("iframe");n.hidden=!0,e.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(t=Ei(n.contentWindow.fetch)),e.head.removeChild(n)}catch(t){O&&N.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",t)}return t}let Ii;function Ri(t,e=function(){if(Ii)return Ii;if(Ei(si.fetch))return Ii=si.fetch.bind(si);const t=si.document;let e=si.fetch;if(t&&"function"==typeof t.createElement)try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n);const r=n.contentWindow;r&&r.fetch&&(e=r.fetch),t.head.removeChild(n)}catch(t){di&&N.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",t)}return Ii=e.bind(si)}()){let n=0,r=0;return Gr(t,(function(i){const o=i.body.length;n+=o,r++;const s={body:i.body,method:"POST",referrerPolicy:"origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};try{return e(t.url,s).then((t=>(n-=o,r--,{statusCode:t.status,headers:{"x-sentry-rate-limits":t.headers.get("X-Sentry-Rate-Limits"),"retry-after":t.headers.get("Retry-After")}})))}catch(t){return Ii=void 0,n-=o,r--,lt(t)}}))}const Oi=4;function Mi(t){return Gr(t,(function(e){return new dt(((n,r)=>{const i=new XMLHttpRequest;i.onerror=r,i.onreadystatechange=()=>{i.readyState===Oi&&n({statusCode:i.status,headers:{"x-sentry-rate-limits":i.getResponseHeader("X-Sentry-Rate-Limits"),"retry-after":i.getResponseHeader("Retry-After")}})},i.open("POST",t.url);for(const e in t.headers)Object.prototype.hasOwnProperty.call(t.headers,e)&&i.setRequestHeader(e,t.headers[e]);i.send(e.body)}))}))}const Ai="?";function Di(t,e,n,r){const i={filename:t,function:e,in_app:!0};return void 0!==n&&(i.lineno=n),void 0!==r&&(i.colno=r),i}const Ni=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Li=/\((\S*)(?::(\d+))(?::(\d+))\)/,Pi=[30,t=>{const e=Ni.exec(t);if(e){if(e[2]&&0===e[2].indexOf("eval")){const t=Li.exec(e[2]);t&&(e[2]=t[1],e[3]=t[2],e[4]=t[3])}const[t,n]=Ji(e[1]||Ai,e[2]);return Di(n,t,e[3]?+e[3]:void 0,e[4]?+e[4]:void 0)}}],Fi=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,ji=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,$i=[50,t=>{const e=Fi.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const t=ji.exec(e[3]);t&&(e[1]=e[1]||"eval",e[3]=t[1],e[4]=t[2],e[5]="")}let t=e[3],n=e[1]||Ai;return[n,t]=Ji(n,t),Di(t,n,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}],Hi=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:[-a-z]+):.*?):(\d+)(?::(\d+))?\)?\s*$/i,Bi=[40,t=>{const e=Hi.exec(t);return e?Di(e[2],e[1]||Ai,+e[3],e[4]?+e[4]:void 0):void 0}],Ui=/ line (\d+).*script (?:in )?(\S+)(?:: in function (\S+))?$/i,qi=[10,t=>{const e=Ui.exec(t);return e?Di(e[2],e[3]||Ai,+e[1]):void 0}],zi=/ line (\d+), column (\d+)\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\(.*\))? in (.*):\s*$/i,Wi=[20,t=>{const e=zi.exec(t);return e?Di(e[5],e[3]||e[4]||Ai,+e[1],+e[2]):void 0}],Yi=[Pi,$i,Bi],Gi=oe(...Yi),Ji=(t,e)=>{const n=-1!==t.indexOf("safari-extension"),r=-1!==t.indexOf("safari-web-extension");return n||r?[-1!==t.indexOf("@")?t.split("@")[0]:Ai,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},Vi=S(),Ki=b;let Xi;function Qi(t){const e="history";Xn(e,t),Qn(e,Zi)}function Zi(){if(!function(){const t=Vi.chrome,e=t&&t.app&&t.app.runtime,n="history"in Vi&&!!Vi.history.pushState&&!!Vi.history.replaceState;return!e&&n}())return;const t=Ki.onpopstate;function e(t){return function(...e){const n=e.length>2?e[2]:void 0;if(n){const t=Xi,e=String(n);Xi=e,Zn("history",{from:t,to:e})}return t.apply(this,e)}}Ki.onpopstate=function(...e){const n=Ki.location.href,r=Xi;if(Xi=n,Zn("history",{from:r,to:n}),t)try{return t.apply(this,e)}catch(t){}},$(Ki.history,"pushState",e),$(Ki.history,"replaceState",e)}function to(t){const e="console";Xn(e,t),Qn(e,eo)}function eo(){"console"in b&&M.forEach((function(t){t in b.console&&$(b.console,t,(function(e){return A[t]=e,function(...e){Zn("console",{args:e,level:t});const n=A[t];n&&n.apply(b.console,e)}}))}))}const no=b,ro=1e3;let io,oo,so;function ao(t){Xn("dom",t),Qn("dom",co)}function co(){if(!no.document)return;const t=Zn.bind(null,"dom"),e=uo(t,!0);no.document.addEventListener("click",e,!1),no.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach((e=>{const n=no[e]&&no[e].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&($(n,"addEventListener",(function(e){return function(n,r,i){if("click"===n||"keypress"==n)try{const r=this,o=r.__sentry_instrumentation_handlers__=r.__sentry_instrumentation_handlers__||{},s=o[n]=o[n]||{refCount:0};if(!s.handler){const r=uo(t);s.handler=r,e.call(this,n,r,i)}s.refCount++}catch(t){}return e.call(this,n,r,i)}})),$(n,"removeEventListener",(function(t){return function(e,n,r){if("click"===e||"keypress"==e)try{const n=this,i=n.__sentry_instrumentation_handlers__||{},o=i[e];o&&(o.refCount--,o.refCount<=0&&(t.call(this,e,o.handler,r),o.handler=void 0,delete i[e]),0===Object.keys(i).length&&delete n.__sentry_instrumentation_handlers__)}catch(t){}return t.call(this,e,n,r)}})))}))}function uo(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(t){try{return t.target}catch(t){return null}}(n);if(function(t,e){return"keypress"===t&&(!e||!e.tagName||"INPUT"!==e.tagName&&"TEXTAREA"!==e.tagName&&!e.isContentEditable)}(n.type,r))return;H(n,"_sentryCaptured",!0),r&&!r._sentryId&&H(r,"_sentryId",K());const i="keypress"===n.type?"input":n.type;(function(t){if(t.type!==oo)return!1;try{if(!t.target||t.target._sentryId!==so)return!1}catch(t){}return!0})(n)||(t({event:n,name:i,global:e}),oo=n.type,so=r?r._sentryId:void 0),clearTimeout(io),io=no.setTimeout((()=>{so=void 0,oo=void 0}),ro)}}const lo=b,po="__sentry_xhr_v3__";function ho(t){Xn("xhr",t),Qn("xhr",fo)}function fo(){if(!lo.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;$(t,"open",(function(t){return function(...e){const n=Date.now(),r=l(e[0])?e[0].toUpperCase():void 0,i=function(t){if(l(t))return t;try{return t.toString()}catch(t){}}(e[1]);if(!r||!i)return t.apply(this,e);this[po]={method:r,url:i,request_headers:{}},"POST"===r&&i.match(/sentry_key/)&&(this.__sentry_own_request__=!0);const o=()=>{const t=this[po];if(t&&4===this.readyState){try{t.status_code=this.status}catch(t){}Zn("xhr",{args:[r,i],endTimestamp:Date.now(),startTimestamp:n,xhr:this})}};return"onreadystatechange"in this&&"function"==typeof this.onreadystatechange?$(this,"onreadystatechange",(function(t){return function(...e){return o(),t.apply(this,e)}})):this.addEventListener("readystatechange",o),$(this,"setRequestHeader",(function(t){return function(...e){const[n,r]=e,i=this[po];return i&&l(n)&&l(r)&&(i.request_headers[n.toLowerCase()]=r),t.apply(this,e)}})),t.apply(this,e)}})),$(t,"send",(function(t){return function(...e){const n=this[po];return n?(void 0!==e[0]&&(n.body=e[0]),Zn("xhr",{args:[n.method,n.url],startTimestamp:Date.now(),xhr:this}),t.apply(this,e)):t.apply(this,e)}}))}function mo(t){const e="fetch";Xn(e,t),Qn(e,go)}function go(){Ti()&&$(b,"fetch",(function(t){return function(...e){const{method:n,url:r}=function(t){if(0===t.length)return{method:"GET",url:""};if(2===t.length){const[e,n]=t;return{url:vo(e),method:yo(n,"method")?String(n.method).toUpperCase():"GET"}}const e=t[0];return{url:vo(e),method:yo(e,"method")?String(e.method).toUpperCase():"GET"}}(e),i={args:e,fetchData:{method:n,url:r},startTimestamp:Date.now()};return Zn("fetch",{...i}),t.apply(b,e).then((t=>(Zn("fetch",{...i,endTimestamp:Date.now(),response:t}),t)),(t=>{throw Zn("fetch",{...i,endTimestamp:Date.now(),error:t}),t}))}}))}function yo(t,e){return!!t&&"object"==typeof t&&!!t[e]}function vo(t){return"string"==typeof t?t:t?yo(t,"url")?t.url:t.toString?t.toString():"":""}const _o=["fatal","error","warning","log","info","debug"];function bo(t){return"warn"===t?"warning":_o.includes(t)?t:"log"}function So(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}const wo="Breadcrumbs",ko=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:wo,setupOnce(){},setup(t){e.console&&to(function(t){return function(e){if(Ae()!==t)return;const n={category:"console",data:{arguments:e.args,logger:"console"},level:bo(e.level),message:F(e.args," ")};if("assert"===e.level){if(!1!==e.args[0])return;n.message=`Assertion failed: ${F(e.args.slice(1)," ")||"console.assert"}`,n.data.arguments=e.args.slice(1)}ve(n,{input:e.args,level:e.level})}}(t)),e.dom&&ao(function(t,e){return function(n){if(Ae()!==t)return;let r,i,o="object"==typeof e?e.serializeAttribute:void 0,s="object"==typeof e&&"number"==typeof e.maxStringLength?e.maxStringLength:void 0;s&&s>1024&&(di&&N.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${s} was configured. Sentry will use 1024 instead.`),s=1024),"string"==typeof o&&(o=[o]);try{const t=n.event,e=function(t){return!!t&&!!t.target}(t)?t.target:t;r=C(e,{keyAttrs:o,maxStringLength:s}),i=R(e)}catch(t){r="<unknown>"}if(0===r.length)return;const a={category:`ui.${n.name}`,message:r};i&&(a.data={"ui.component_name":i}),ve(a,{event:n.event,name:n.name,global:n.global})}}(t,e.dom)),e.xhr&&ho(function(t){return function(e){if(Ae()!==t)return;const{startTimestamp:n,endTimestamp:r}=e,i=e.xhr[po];if(!n||!r||!i)return;const{method:o,url:s,status_code:a,body:c}=i;ve({category:"xhr",data:{method:o,url:s,status_code:a},type:"http"},{xhr:e.xhr,input:c,startTimestamp:n,endTimestamp:r})}}(t)),e.fetch&&mo(function(t){return function(e){if(Ae()!==t)return;const{startTimestamp:n,endTimestamp:r}=e;if(r&&(!e.fetchData.url.match(/sentry_key/)||"POST"!==e.fetchData.method))if(e.error)ve({category:"fetch",data:e.fetchData,level:"error",type:"http"},{data:e.error,input:e.args,startTimestamp:n,endTimestamp:r});else{const t=e.response;ve({category:"fetch",data:{...e.fetchData,status_code:t&&t.status},type:"http"},{input:e.args,response:t,startTimestamp:n,endTimestamp:r})}}}(t)),e.history&&Qi(function(t){return function(e){if(Ae()!==t)return;let n=e.from,r=e.to;const i=So(si.location.href);let o=n?So(n):void 0;const s=So(r);o&&o.path||(o=i),i.protocol===s.protocol&&i.host===s.host&&(r=s.relative),i.protocol===o.protocol&&i.host===o.host&&(n=o.relative),ve({category:"navigation",data:{from:n,to:r}})}}(t)),e.sentry&&t.on&&t.on("beforeSendEvent",function(t){return function(e){Ae()===t&&ve({category:"sentry."+("transaction"===e.type?"transaction":"event"),event_id:e.event_id,level:e.level,message:Q(e)},{event:e})}}(t))}}},xo=ze(wo,ko),Co="Dedupe",Eo=()=>{let t;return{name:Co,setupOnce(){},processEvent(e){if(e.type)return e;try{if(function(t,e){return!!e&&(!!function(t,e){const n=t.message,r=e.message;return!(!n&&!r)&&(!(n&&!r||!n&&r)&&(n===r&&(!!Ro(t,e)&&!!Io(t,e))))}(t,e)||!!function(t,e){const n=Oo(e),r=Oo(t);return!(!n||!r)&&(n.type===r.type&&n.value===r.value&&(!!Ro(t,e)&&!!Io(t,e)))}(t,e))}(e,t))return di&&N.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(t){}return t=e}}},To=ze(Co,Eo);function Io(t,e){let n=Mo(t),r=Mo(e);if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;if(r.length!==n.length)return!1;for(let t=0;t<r.length;t++){const e=r[t],i=n[t];if(e.filename!==i.filename||e.lineno!==i.lineno||e.colno!==i.colno||e.function!==i.function)return!1}return!0}function Ro(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return!(n.join("")!==r.join(""))}catch(t){return!1}}function Oo(t){return t.exception&&t.exception.values&&t.exception.values[0]}function Mo(t){const e=t.exception;if(e)try{return e.values[0].stacktrace.frames}catch(t){return}}const Ao="GlobalHandlers",Do=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:Ao,setupOnce(){Error.stackTraceLimit=50},setup(t){e.onerror&&(function(t){er((e=>{const{stackParser:n,attachStacktrace:r}=Fo();if(Ae()!==t||ci())return;const{msg:i,url:o,line:s,column:a,error:u}=e,d=void 0===u&&l(i)?function(t,e,n,r){let i=c(t)?t.message:t,o="Error";const s=i.match(/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i);s&&(o=s[1],i=s[2]);return Lo({exception:{values:[{type:o,value:i}]}},e,n,r)}(i,o,s,a):Lo(_i(n,u||i,void 0,r,!1),o,s,a);d.level="error",ge(d,{originalException:u,mechanism:{handled:!1,type:"onerror"}})}))}(t),Po("onerror")),e.onunhandledrejection&&(function(t){ir((e=>{const{stackParser:n,attachStacktrace:r}=Fo();if(Ae()!==t||ci())return;const i=function(t){if(p(t))return t;const e=t;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch(t){}return t}(e),o=p(i)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(i)}`}]}}:_i(n,i,void 0,r,!0);o.level="error",ge(o,{originalException:i,mechanism:{handled:!1,type:"onunhandledrejection"}})}))}(t),Po("onunhandledrejection"))}}},No=ze(Ao,Do);function Lo(t,e,n,r){const i=t.exception=t.exception||{},o=i.values=i.values||[],s=o[0]=o[0]||{},a=s.stacktrace=s.stacktrace||{},c=a.frames=a.frames||[],u=isNaN(parseInt(r,10))?void 0:r,d=isNaN(parseInt(n,10))?void 0:n,p=l(e)&&e.length>0?e:T();return 0===c.length&&c.push({colno:u,filename:p,function:"?",in_app:!0,lineno:d}),t}function Po(t){di&&N.log(`Global Handler attached: ${t}`)}function Fo(){const t=Ae();return t&&t.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const jo="HttpContext",$o=()=>({name:jo,setupOnce(){},preprocessEvent(t){if(!si.navigator&&!si.location&&!si.document)return;const e=t.request&&t.request.url||si.location&&si.location.href,{referrer:n}=si.document||{},{userAgent:r}=si.navigator||{},i={...t.request&&t.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},o={...t.request,...e&&{url:e},headers:i};t.request=o}}),Ho=ze(jo,$o),Bo="LinkedErrors",Uo=(t={})=>{const e=t.limit||5,n=t.key||"cause";return{name:Bo,setupOnce(){},preprocessEvent(t,r,i){const o=i.getOptions();nn(pi,o.stackParser,o.maxValueLength,n,e,t,r)}}},qo=ze(Bo,Uo),zo=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Wo="TryCatch",Yo=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:Wo,setupOnce(){e.setTimeout&&$(si,"setTimeout",Jo),e.setInterval&&$(si,"setInterval",Jo),e.requestAnimationFrame&&$(si,"requestAnimationFrame",Vo),e.XMLHttpRequest&&"XMLHttpRequest"in si&&$(XMLHttpRequest.prototype,"send",Ko);const t=e.eventTarget;t&&(Array.isArray(t)?t:zo).forEach(Xo)}}},Go=ze(Wo,Yo);function Jo(t){return function(...e){const n=e[0];return e[0]=ui(n,{mechanism:{data:{function:ae(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function Vo(t){return function(e){return t.apply(this,[ui(e,{mechanism:{data:{function:"requestAnimationFrame",handler:ae(t)},handled:!1,type:"instrument"}})])}}function Ko(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach((t=>{t in n&&"function"==typeof n[t]&&$(n,t,(function(e){const n={mechanism:{data:{function:t,handler:ae(e)},handled:!1,type:"instrument"}},r=U(e);return r&&(n.mechanism.data.handler=ae(r)),ui(e,n)}))})),t.apply(this,e)}}function Xo(t){const e=si,n=e[t]&&e[t].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&($(n,"addEventListener",(function(e){return function(n,r,i){try{"function"==typeof r.handleEvent&&(r.handleEvent=ui(r.handleEvent,{mechanism:{data:{function:"handleEvent",handler:ae(r),target:t},handled:!1,type:"instrument"}}))}catch(t){}return e.apply(this,[n,ui(r,{mechanism:{data:{function:"addEventListener",handler:ae(r),target:t},handled:!1,type:"instrument"}}),i])}})),$(n,"removeEventListener",(function(t){return function(e,n,r){const i=n;try{const n=i&&i.__sentry_wrapped__;n&&t.call(this,e,n,r)}catch(t){}return t.call(this,e,i,r)}})))}const Qo=[Ze(),Je(),Yo(),ko(),Do(),Uo(),Eo(),$o()];function Zo(t){return[...Qo]}function ts(t={}){void 0===t.defaultIntegrations&&(t.defaultIntegrations=Zo()),void 0===t.release&&("string"==typeof __SENTRY_RELEASE__&&(t.release=__SENTRY_RELEASE__),si.SENTRY_RELEASE&&si.SENTRY_RELEASE.id&&(t.release=si.SENTRY_RELEASE.id)),void 0===t.autoSessionTracking&&(t.autoSessionTracking=!0),void 0===t.sendClientReports&&(t.sendClientReports=!0);const e={...t,stackParser:(n=t.stackParser||Gi,Array.isArray(n)?oe(...n):n),integrations:He(t),transport:t.transport||(Ci()?Ri:Mi)};var n;!function(t,e){!0===e.debug&&(V?N.enable():D((()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")}))),Ne().update(e.initialScope);const n=new t(e);ii(n),function(t){t.init?t.init():t.setupIntegrations&&t.setupIntegrations()}(n)}(ki,e),t.autoSessionTracking&&(void 0!==si.document?(Le({ignoreDuration:!0}),je(),Qi((({from:t,to:e})=>{void 0!==t&&t!==e&&(Le({ignoreDuration:!0}),je())}))):di&&N.warn("Session tracking in non-browser environment with @sentry/browser is not supported."))}const es=(t={},e=Xt())=>{if(!si.document)return void(di&&N.error("Global document not defined in showReportDialog call"));const{client:n,scope:r}=e.getStackTop(),i=t.dsn||n&&n.getDsn();if(!i)return void(di&&N.error("DSN not configured for showReportDialog call"));r&&(t.user={...r.getUser(),...t.user}),t.eventId||(t.eventId=e.lastEventId());const o=si.document.createElement("script");o.async=!0,o.crossOrigin="anonymous",o.src=function(t,e){const n=mn(t);if(!n)return"";const r=`${jn(n)}embed/error-page/`;let i=`dsn=${pn(n)}`;for(const t in e)if("dsn"!==t&&"onClose"!==t)if("user"===t){const t=e.user;if(!t)continue;t.name&&(i+=`&name=${encodeURIComponent(t.name)}`),t.email&&(i+=`&email=${encodeURIComponent(t.email)}`)}else i+=`&${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`;return`${r}?${i}`}(i,t),t.onLoad&&(o.onload=t.onLoad);const{onClose:s}=t;if(s){const t=e=>{if("__sentry_reportdialog_closed__"===e.data)try{s()}finally{si.removeEventListener("message",t)}};si.addEventListener("message",t)}const a=si.document.head||si.document.body;a?a.appendChild(o):di&&N.error("Not injecting report dialog. No injection point found in HTML")};function ns(){}function rs(t){t()}function is(t){return ui(t)()}function os(t){const e=Ae();e&&e.captureUserFeedback(t)}function ss(t){let e,n=t[0],r=1;for(;r<t.length;){const i=t[r],o=t[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(e=n,n=o(n)):"call"!==i&&"optionalCall"!==i||(n=o(((...t)=>n.call(e,...t))),e=void 0)}return n}function as(t,e){const n=e&&function(t){return void 0!==t.getClient}(e)?e.getClient():e,r=n&&n.getDsn(),i=n&&n.getOptions().tunnel;return function(t,e){return!!e&&t.includes(e.host)}(t,r)||function(t,e){return!!e&&cs(t)===cs(e)}(t,i)}function cs(t){return"/"===t[t.length-1]?t.slice(0,-1):t}var us=n(8609);function ls(){return"undefined"!=typeof window&&(!(0,us.wD)()||void 0!==b.process&&"renderer"===b.process.type)}const ds="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,ps=(t,e,n)=>{let r,i;return o=>{e.value>=0&&(o||n)&&(i=e.value-(r||0),(i||void 0===r)&&(r=e.value,e.delta=i,t(e)))}},hs=b,fs=()=>hs.__WEB_VITALS_POLYFILL__?hs.performance&&(performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]||(()=>{const t=hs.performance.timing,e=hs.performance.navigation.type,n={entryType:"navigation",startTime:0,type:2==e?"back_forward":1===e?"reload":"navigate"};for(const e in t)"navigationStart"!==e&&"toJSON"!==e&&(n[e]=Math.max(t[e]-t.navigationStart,0));return n})()):hs.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0],ms=()=>{const t=fs();return t&&t.activationStart||0},gs=(t,e)=>{const n=fs();let r="navigate";return n&&(r=hs.document&&hs.document.prerendering||ms()>0?"prerender":n.type.replace(/_/g,"-")),{name:t,value:void 0===e?-1:e,rating:"good",delta:0,entries:[],id:`v3-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},ys=(t,e,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const r=new PerformanceObserver((t=>{e(t.getEntries())}));return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch(t){}},vs=(t,e)=>{const n=r=>{"pagehide"!==r.type&&"hidden"!==hs.document.visibilityState||(t(r),e&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};hs.document&&(addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0))},_s=(t,e={})=>{const n=gs("CLS",0);let r,i=0,o=[];const s=t=>{t.forEach((t=>{if(!t.hadRecentInput){const e=o[0],s=o[o.length-1];i&&0!==o.length&&t.startTime-s.startTime<1e3&&t.startTime-e.startTime<5e3?(i+=t.value,o.push(t)):(i=t.value,o=[t]),i>n.value&&(n.value=i,n.entries=o,r&&r())}}))},a=ys("layout-shift",s);if(a){r=ps(t,n,e.reportAllChanges);const i=()=>{s(a.takeRecords()),r(!0)};return vs(i),i}};let bs=-1;const Ss=()=>(bs<0&&(hs.document&&hs.document.visibilityState&&(bs="hidden"!==hs.document.visibilityState||hs.document.prerendering?1/0:0),vs((({timeStamp:t})=>{bs=t}),!0)),{get firstHiddenTime(){return bs}}),ws=t=>{const e=Ss(),n=gs("FID");let r;const i=t=>{t.startTime<e.firstHiddenTime&&(n.value=t.processingStart-t.startTime,n.entries.push(t),r(!0))},o=t=>{t.forEach(i)},s=ys("first-input",o);r=ps(t,n),s&&vs((()=>{o(s.takeRecords()),s.disconnect()}),!0)};let ks=0,xs=1/0,Cs=0;const Es=t=>{t.forEach((t=>{t.interactionId&&(xs=Math.min(xs,t.interactionId),Cs=Math.max(Cs,t.interactionId),ks=Cs?(Cs-xs)/7+1:0)}))};let Ts;const Is=()=>{"interactionCount"in performance||Ts||(Ts=ys("event",Es,{type:"event",buffered:!0,durationThreshold:0}))},Rs=()=>Ts?ks:performance.interactionCount||0,Os=[],Ms={},As=t=>{const e=Os[Os.length-1],n=Ms[t.interactionId];if(n||Os.length<10||t.duration>e.latency){if(n)n.entries.push(t),n.latency=Math.max(n.latency,t.duration);else{const e={id:t.interactionId,latency:t.duration,entries:[t]};Ms[e.id]=e,Os.push(e)}Os.sort(((t,e)=>e.latency-t.latency)),Os.splice(10).forEach((t=>{delete Ms[t.id]}))}},Ds=(t,e)=>{e=e||{},Is();const n=gs("INP");let r;const i=t=>{t.forEach((t=>{t.interactionId&&As(t),"first-input"===t.entryType&&!Os.some((e=>e.entries.some((e=>t.duration===e.duration&&t.startTime===e.startTime))))&&As(t)}));const e=(()=>{const t=Math.min(Os.length-1,Math.floor(Rs()/50));return Os[t]})();e&&e.latency!==n.value&&(n.value=e.latency,n.entries=e.entries,r())},o=ys("event",i,{durationThreshold:e.durationThreshold||40});r=ps(t,n,e.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),vs((()=>{i(o.takeRecords()),n.value<0&&Rs()>0&&(n.value=0,n.entries=[]),r(!0)})))},Ns={},Ls=t=>{const e=Ss(),n=gs("LCP");let r;const i=t=>{const i=t[t.length-1];if(i){const t=Math.max(i.startTime-ms(),0);t<e.firstHiddenTime&&(n.value=t,n.entries=[i],r())}},o=ys("largest-contentful-paint",i);if(o){r=ps(t,n);const e=()=>{Ns[n.id]||(i(o.takeRecords()),o.disconnect(),Ns[n.id]=!0,r(!0))};return["keydown","click"].forEach((t=>{hs.document&&addEventListener(t,e,{once:!0,capture:!0})})),vs(e,!0),e}},Ps=t=>{hs.document&&(hs.document.prerendering?addEventListener("prerenderingchange",(()=>Ps(t)),!0):"complete"!==hs.document.readyState?addEventListener("load",(()=>Ps(t)),!0):setTimeout(t,0))},Fs=(t,e)=>{e=e||{};const n=gs("TTFB"),r=ps(t,n,e.reportAllChanges);Ps((()=>{const t=fs();if(t){if(n.value=Math.max(t.responseStart-ms(),0),n.value<0||n.value>performance.now())return;n.entries=[t],r(!0)}}))},js={},$s={};let Hs,Bs,Us,qs,zs;function Ws(t,e=!1){return Zs("lcp",t,Ks,Us,e)}function Ys(t,e){return ta(t,e),$s[t]||(function(t){const e={};"event"===t&&(e.durationThreshold=0),ys(t,(e=>{Gs(t,{entries:e})}),e)}(t),$s[t]=!0),ea(t,e)}function Gs(t,e){const n=js[t];if(n&&n.length)for(const r of n)try{r(e)}catch(e){ds&&N.error(`Error while triggering instrumentation handler.\nType: ${t}\nName: ${ae(r)}\nError:`,e)}}function Js(){return _s((t=>{Gs("cls",{metric:t}),Hs=t}),{reportAllChanges:!0})}function Vs(){return ws((t=>{Gs("fid",{metric:t}),Bs=t}))}function Ks(){return Ls((t=>{Gs("lcp",{metric:t}),Us=t}))}function Xs(){return Fs((t=>{Gs("ttfb",{metric:t}),qs=t}))}function Qs(){return Ds((t=>{Gs("inp",{metric:t}),zs=t}))}function Zs(t,e,n,r,i=!1){let o;return ta(t,e),$s[t]||(o=n(),$s[t]=!0),r&&e({metric:r}),ea(t,e,i?o:void 0)}function ta(t,e){js[t]=js[t]||[],js[t].push(e)}function ea(t,e,n){return()=>{n&&n();const r=js[t];if(!r)return;const i=r.indexOf(e);-1!==i&&r.splice(i,1)}}const na=b,ra="sentryReplaySession",ia="replay_event",oa="Unable to send Replay",sa=15e4,aa=5e3,ca=2e7;function ua(t){let e,n=t[0],r=1;for(;r<t.length;){const i=t[r],o=t[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(e=n,n=o(n)):"call"!==i&&"optionalCall"!==i||(n=o(((...t)=>n.call(e,...t))),e=void 0)}return n}var la;function da(t){const e=ua([t,"optionalAccess",t=>t.host]);return Boolean(ua([e,"optionalAccess",t=>t.shadowRoot])===t)}function pa(t){return"[object ShadowRoot]"===Object.prototype.toString.call(t)}function ha(t){try{const n=t.rules||t.cssRules;return n?((e=Array.from(n,fa).join("")).includes(" background-clip: text;")&&!e.includes(" -webkit-background-clip: text;")&&(e=e.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),e):null}catch(t){return null}var e}function fa(t){let e;if(function(t){return"styleSheet"in t}(t))try{e=ha(t.styleSheet)||function(t){const{cssText:e}=t;if(e.split('"').length<3)return e;const n=["@import",`url(${JSON.stringify(t.href)})`];return""===t.layerName?n.push("layer"):t.layerName&&n.push(`layer(${t.layerName})`),t.supportsText&&n.push(`supports(${t.supportsText})`),t.media.length&&n.push(t.media.mediaText),n.join(" ")+";"}(t)}catch(t){}else if(function(t){return"selectorText"in t}(t)&&t.selectorText.includes(":"))return t.cssText.replace(/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,"$1\\$2");return e||t.cssText}!function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"}(la||(la={}));class ma{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(t){if(!t)return-1;return()=>-1,null!=(e=ua([this,"access",t=>t.getMeta,"call",e=>e(t),"optionalAccess",t=>t.id]))?e:-1;var e}getNode(t){return this.idNodeMap.get(t)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(t){return this.nodeMetaMap.get(t)||null}removeNodeFromMap(t){const e=this.getId(t);this.idNodeMap.delete(e),t.childNodes&&t.childNodes.forEach((t=>this.removeNodeFromMap(t)))}has(t){return this.idNodeMap.has(t)}hasNode(t){return this.nodeMetaMap.has(t)}add(t,e){const n=e.id;this.idNodeMap.set(n,t),this.nodeMetaMap.set(t,e)}replace(t,e){const n=this.getNode(t);if(n){const t=this.nodeMetaMap.get(n);t&&this.nodeMetaMap.set(e,t)}this.idNodeMap.set(t,e)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function ga({maskInputOptions:t,tagName:e,type:n}){return"OPTION"===e&&(e="SELECT"),Boolean(t[e.toLowerCase()]||n&&t[n]||"password"===n||"INPUT"===e&&!n&&t.text)}function ya({isMasked:t,element:e,value:n,maskInputFn:r}){let i=n||"";return t?(r&&(i=r(i,e)),"*".repeat(i.length)):i}function va(t){return t.toLowerCase()}function _a(t){return t.toUpperCase()}const ba="__rrweb_original__";function Sa(t){const e=t.type;return t.hasAttribute("data-rr-is-password")?"password":e?va(e):null}function wa(t,e,n){return"INPUT"!==e||"radio"!==n&&"checkbox"!==n?t.value:t.getAttribute("value")||""}let ka=1;const xa=new RegExp("[^a-z0-9-_:]"),Ca=-2;function Ea(){return ka++}let Ta,Ia;const Ra=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,Oa=/^(?:[a-z+]+:)?\/\//i,Ma=/^www\..*/i,Aa=/^(data:)([^,]*),(.*)/i;function Da(t,e){return(t||"").replace(Ra,((t,n,r,i,o,s)=>{const a=r||o||s,c=n||i||"";if(!a)return t;if(Oa.test(a)||Ma.test(a))return`url(${c}${a}${c})`;if(Aa.test(a))return`url(${c}${a}${c})`;if("/"===a[0])return`url(${c}${function(t){let e="";return e=t.indexOf("//")>-1?t.split("/").slice(0,3).join("/"):t.split("/")[0],e=e.split("?")[0],e}(e)+a}${c})`;const u=e.split("/"),l=a.split("/");u.pop();for(const t of l)"."!==t&&(".."===t?u.pop():u.push(t));return`url(${c}${u.join("/")}${c})`}))}const Na=/^[^ \t\n\r\u000c]+/,La=/^[, \t\n\r\u000c]+/;function Pa(t,e){if(!e||""===e.trim())return e;const n=t.createElement("a");return n.href=e,n.href}function Fa(){const t=document.createElement("a");return t.href="",t.href}function ja(t,e,n,r,i,o){return r?"src"===n||"href"===n&&("use"!==e||"#"!==r[0])||"xlink:href"===n&&"#"!==r[0]?Pa(t,r):"background"!==n||"table"!==e&&"td"!==e&&"th"!==e?"srcset"===n?function(t,e){if(""===e.trim())return e;let n=0;function r(t){let r;const i=t.exec(e.substring(n));return i?(r=i[0],n+=r.length,r):""}const i=[];for(;r(La),!(n>=e.length);){let o=r(Na);if(","===o.slice(-1))o=Pa(t,o.substring(0,o.length-1)),i.push(o);else{let r="";o=Pa(t,o);let s=!1;for(;;){const t=e.charAt(n);if(""===t){i.push((o+r).trim());break}if(s)")"===t&&(s=!1);else{if(","===t){n+=1,i.push((o+r).trim());break}"("===t&&(s=!0)}r+=t,n+=1}}}return i.join(", ")}(t,r):"style"===n?Da(r,Fa()):"object"===e&&"data"===n?Pa(t,r):"function"==typeof o?o(n,r,i):r:Pa(t,r):r}function $a(t,e,n){return("video"===t||"audio"===t)&&"autoplay"===e}function Ha(t,e,n=1/0,r=0){return t?t.nodeType!==t.ELEMENT_NODE||r>n?-1:e(t)?r:Ha(t.parentNode,e,n,r+1):-1}function Ba(t,e){return n=>{const r=n;if(null===r)return!1;try{if(t)if("string"==typeof t){if(r.matches(`.${t}`))return!0}else if(function(t,e){for(let n=t.classList.length;n--;){const r=t.classList[n];if(e.test(r))return!0}return!1}(r,t))return!0;return!(!e||!r.matches(e))}catch(t){return!1}}}function Ua(t,e,n,r,i,o){try{const s=t.nodeType===t.ELEMENT_NODE?t:t.parentElement;if(null===s)return!1;if("INPUT"===s.tagName){const t=s.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(t))return!0}let a=-1,c=-1;if(o){if(c=Ha(s,Ba(r,i)),c<0)return!0;a=Ha(s,Ba(e,n),c>=0?c:1/0)}else{if(a=Ha(s,Ba(e,n)),a<0)return!1;c=Ha(s,Ba(r,i),a>=0?a:1/0)}return a>=0?!(c>=0)||a<=c:!(c>=0||!o)}catch(t){}return!!o}function qa(t){return null==t?"":t.toLowerCase()}function za(t,e){const{doc:n,mirror:r,blockClass:i,blockSelector:o,unblockSelector:s,maskAllText:a,maskTextClass:c,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:d,skipChild:p=!1,inlineStylesheet:h=!0,maskInputOptions:f={},maskAttributeFn:m,maskTextFn:g,maskInputFn:y,slimDOMOptions:v,dataURLOptions:_={},inlineImages:b=!1,recordCanvas:S=!1,onSerialize:w,onIframeLoad:k,iframeLoadTimeout:x=5e3,onStylesheetLoad:C,stylesheetLoadTimeout:E=5e3,keepIframeSrcFn:T=(()=>!1),newlyAddedElement:I=!1}=e;let{preserveWhiteSpace:R=!0}=e;const O=function(t,e){const{doc:n,mirror:r,blockClass:i,blockSelector:o,unblockSelector:s,maskAllText:a,maskAttributeFn:c,maskTextClass:u,unmaskTextClass:l,maskTextSelector:d,unmaskTextSelector:p,inlineStylesheet:h,maskInputOptions:f={},maskTextFn:m,maskInputFn:g,dataURLOptions:y={},inlineImages:v,recordCanvas:_,keepIframeSrcFn:b,newlyAddedElement:S=!1}=e,w=function(t,e){if(!e.hasNode(t))return;const n=e.getId(t);return 1===n?void 0:n}(n,r);switch(t.nodeType){case t.DOCUMENT_NODE:return"CSS1Compat"!==t.compatMode?{type:la.Document,childNodes:[],compatMode:t.compatMode}:{type:la.Document,childNodes:[]};case t.DOCUMENT_TYPE_NODE:return{type:la.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId,rootId:w};case t.ELEMENT_NODE:return function(t,e){const{doc:n,blockClass:r,blockSelector:i,unblockSelector:o,inlineStylesheet:s,maskInputOptions:a={},maskAttributeFn:c,maskInputFn:u,dataURLOptions:l={},inlineImages:d,recordCanvas:p,keepIframeSrcFn:h,newlyAddedElement:f=!1,rootId:m,maskAllText:g,maskTextClass:y,unmaskTextClass:v,maskTextSelector:_,unmaskTextSelector:b}=e,S=function(t,e,n,r){try{if(r&&t.matches(r))return!1;if("string"==typeof e){if(t.classList.contains(e))return!0}else for(let n=t.classList.length;n--;){const r=t.classList[n];if(e.test(r))return!0}if(n)return t.matches(n)}catch(t){}return!1}(t,r,i,o),w=function(t){if(t instanceof HTMLFormElement)return"form";const e=va(t.tagName);return xa.test(e)?"div":e}(t);let k={};const x=t.attributes.length;for(let e=0;e<x;e++){const r=t.attributes[e];r.name&&!$a(w,r.name,r.value)&&(k[r.name]=ja(n,w,va(r.name),r.value,t,c))}if("link"===w&&s){const e=Array.from(n.styleSheets).find((e=>e.href===t.href));let r=null;e&&(r=ha(e)),r&&(delete k.rel,delete k.href,k._cssText=Da(r,e.href))}if("style"===w&&t.sheet&&!(t.innerText||t.textContent||"").trim().length){const e=ha(t.sheet);e&&(k._cssText=Da(e,Fa()))}if("input"===w||"textarea"===w||"select"===w||"option"===w){const e=t,n=Sa(e),r=wa(e,_a(w),n),i=e.checked;if("submit"!==n&&"button"!==n&&r){const t=Ua(e,y,_,v,b,ga({type:n,tagName:_a(w),maskInputOptions:a}));k.value=ya({isMasked:t,element:e,value:r,maskInputFn:u})}i&&(k.checked=i)}if("option"===w&&(t.selected&&!a.select?k.selected=!0:delete k.selected),"canvas"===w&&p)if("2d"===t.__context)(function(t){const e=t.getContext("2d");if(!e)return!0;for(let n=0;n<t.width;n+=50)for(let r=0;r<t.height;r+=50){const i=e.getImageData,o=ba in i?i[ba]:i;if(new Uint32Array(o.call(e,n,r,Math.min(50,t.width-n),Math.min(50,t.height-r)).data.buffer).some((t=>0!==t)))return!1}return!0})(t)||(k.rr_dataURL=t.toDataURL(l.type,l.quality));else if(!("__context"in t)){const e=t.toDataURL(l.type,l.quality),n=document.createElement("canvas");n.width=t.width,n.height=t.height,e!==n.toDataURL(l.type,l.quality)&&(k.rr_dataURL=e)}if("img"===w&&d){Ta||(Ta=n.createElement("canvas"),Ia=Ta.getContext("2d"));const e=t,r=e.crossOrigin;e.crossOrigin="anonymous";const i=()=>{e.removeEventListener("load",i);try{Ta.width=e.naturalWidth,Ta.height=e.naturalHeight,Ia.drawImage(e,0,0),k.rr_dataURL=Ta.toDataURL(l.type,l.quality)}catch(t){console.warn(`Cannot inline img src=${e.currentSrc}! Error: ${t}`)}r?k.crossOrigin=r:e.removeAttribute("crossorigin")};e.complete&&0!==e.naturalWidth?i():e.addEventListener("load",i)}if("audio"!==w&&"video"!==w||(k.rr_mediaState=t.paused?"paused":"played",k.rr_mediaCurrentTime=t.currentTime),f||(t.scrollLeft&&(k.rr_scrollLeft=t.scrollLeft),t.scrollTop&&(k.rr_scrollTop=t.scrollTop)),S){const{width:e,height:n}=t.getBoundingClientRect();k={class:k.class,rr_width:`${e}px`,rr_height:`${n}px`}}let C;"iframe"!==w||h(k.src)||(t.contentDocument||(k.rr_src=k.src),delete k.src);try{customElements.get(w)&&(C=!0)}catch(t){}return{type:la.Element,tagName:w,attributes:k,childNodes:[],isSVG:(E=t,Boolean("svg"===E.tagName||E.ownerSVGElement)||void 0),needBlock:S,rootId:m,isCustom:C};var E}(t,{doc:n,blockClass:i,blockSelector:o,unblockSelector:s,inlineStylesheet:h,maskAttributeFn:c,maskInputOptions:f,maskInputFn:g,dataURLOptions:y,inlineImages:v,recordCanvas:_,keepIframeSrcFn:b,newlyAddedElement:S,rootId:w,maskAllText:a,maskTextClass:u,unmaskTextClass:l,maskTextSelector:d,unmaskTextSelector:p});case t.TEXT_NODE:return function(t,e){const{maskAllText:n,maskTextClass:r,unmaskTextClass:i,maskTextSelector:o,unmaskTextSelector:s,maskTextFn:a,maskInputOptions:c,maskInputFn:u,rootId:l}=e,d=t.parentNode&&t.parentNode.tagName;let p=t.textContent;const h="STYLE"===d||void 0,f="SCRIPT"===d||void 0,m="TEXTAREA"===d||void 0;if(h&&p){try{t.nextSibling||t.previousSibling||ua([t,"access",t=>t.parentNode,"access",t=>t.sheet,"optionalAccess",t=>t.cssRules])&&(p=ha(t.parentNode.sheet))}catch(e){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${e}`,t)}p=Da(p,Fa())}f&&(p="SCRIPT_PLACEHOLDER");const g=Ua(t,r,o,i,s,n);return h||f||m||!p||!g||(p=a?a(p,t.parentElement):p.replace(/[\S]/g,"*")),m&&p&&(c.textarea||g)&&(p=u?u(p,t.parentNode):p.replace(/[\S]/g,"*")),"OPTION"===d&&p&&(p=ya({isMasked:Ua(t,r,o,i,s,ga({type:null,tagName:d,maskInputOptions:c})),element:t,value:p,maskInputFn:u})),{type:la.Text,textContent:p||"",isStyle:h,rootId:l}}(t,{maskAllText:a,maskTextClass:u,unmaskTextClass:l,maskTextSelector:d,unmaskTextSelector:p,maskTextFn:m,maskInputOptions:f,maskInputFn:g,rootId:w});case t.CDATA_SECTION_NODE:return{type:la.CDATA,textContent:"",rootId:w};case t.COMMENT_NODE:return{type:la.Comment,textContent:t.textContent||"",rootId:w};default:return!1}}(t,{doc:n,mirror:r,blockClass:i,blockSelector:o,maskAllText:a,unblockSelector:s,maskTextClass:c,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:d,inlineStylesheet:h,maskInputOptions:f,maskAttributeFn:m,maskTextFn:g,maskInputFn:y,dataURLOptions:_,inlineImages:b,recordCanvas:S,keepIframeSrcFn:T,newlyAddedElement:I});if(!O)return console.warn(t,"not serialized"),null;let M;M=r.hasNode(t)?r.getId(t):!function(t,e){if(e.comment&&t.type===la.Comment)return!0;if(t.type===la.Element){if(e.script&&("script"===t.tagName||"link"===t.tagName&&("preload"===t.attributes.rel||"modulepreload"===t.attributes.rel)&&"script"===t.attributes.as||"link"===t.tagName&&"prefetch"===t.attributes.rel&&"string"==typeof t.attributes.href&&t.attributes.href.endsWith(".js")))return!0;if(e.headFavicon&&("link"===t.tagName&&"shortcut icon"===t.attributes.rel||"meta"===t.tagName&&(qa(t.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===qa(t.attributes.name)||"icon"===qa(t.attributes.rel)||"apple-touch-icon"===qa(t.attributes.rel)||"shortcut icon"===qa(t.attributes.rel))))return!0;if("meta"===t.tagName){if(e.headMetaDescKeywords&&qa(t.attributes.name).match(/^description|keywords$/))return!0;if(e.headMetaSocial&&(qa(t.attributes.property).match(/^(og|twitter|fb):/)||qa(t.attributes.name).match(/^(og|twitter):/)||"pinterest"===qa(t.attributes.name)))return!0;if(e.headMetaRobots&&("robots"===qa(t.attributes.name)||"googlebot"===qa(t.attributes.name)||"bingbot"===qa(t.attributes.name)))return!0;if(e.headMetaHttpEquiv&&void 0!==t.attributes["http-equiv"])return!0;if(e.headMetaAuthorship&&("author"===qa(t.attributes.name)||"generator"===qa(t.attributes.name)||"framework"===qa(t.attributes.name)||"publisher"===qa(t.attributes.name)||"progid"===qa(t.attributes.name)||qa(t.attributes.property).match(/^article:/)||qa(t.attributes.property).match(/^product:/)))return!0;if(e.headMetaVerification&&("google-site-verification"===qa(t.attributes.name)||"yandex-verification"===qa(t.attributes.name)||"csrf-token"===qa(t.attributes.name)||"p:domain_verify"===qa(t.attributes.name)||"verify-v1"===qa(t.attributes.name)||"verification"===qa(t.attributes.name)||"shopify-checkout-api-token"===qa(t.attributes.name)))return!0}}return!1}(O,v)&&(R||O.type!==la.Text||O.isStyle||O.textContent.replace(/^\s+|\s+$/gm,"").length)?Ea():Ca;const A=Object.assign(O,{id:M});if(r.add(t,A),M===Ca)return null;w&&w(t);let D=!p;if(A.type===la.Element){D=D&&!A.needBlock,delete A.needBlock;const e=t.shadowRoot;e&&pa(e)&&(A.isShadowHost=!0)}if((A.type===la.Document||A.type===la.Element)&&D){v.headWhitespace&&A.type===la.Element&&"head"===A.tagName&&(R=!1);const e={doc:n,mirror:r,blockClass:i,blockSelector:o,maskAllText:a,unblockSelector:s,maskTextClass:c,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:d,skipChild:p,inlineStylesheet:h,maskInputOptions:f,maskAttributeFn:m,maskTextFn:g,maskInputFn:y,slimDOMOptions:v,dataURLOptions:_,inlineImages:b,recordCanvas:S,preserveWhiteSpace:R,onSerialize:w,onIframeLoad:k,iframeLoadTimeout:x,onStylesheetLoad:C,stylesheetLoadTimeout:E,keepIframeSrcFn:T};for(const n of Array.from(t.childNodes)){const t=za(n,e);t&&A.childNodes.push(t)}if(function(t){return t.nodeType===t.ELEMENT_NODE}(t)&&t.shadowRoot)for(const n of Array.from(t.shadowRoot.childNodes)){const r=za(n,e);r&&(pa(t.shadowRoot)&&(r.isShadow=!0),A.childNodes.push(r))}}return t.parentNode&&da(t.parentNode)&&pa(t.parentNode)&&(A.isShadow=!0),A.type===la.Element&&"iframe"===A.tagName&&function(t,e,n){const r=t.contentWindow;if(!r)return;let i,o=!1;try{i=r.document.readyState}catch(t){return}if("complete"!==i){const r=setTimeout((()=>{o||(e(),o=!0)}),n);return void t.addEventListener("load",(()=>{clearTimeout(r),o=!0,e()}))}const s="about:blank";if(r.location.href!==s||t.src===s||""===t.src)return setTimeout(e,0),t.addEventListener("load",e);t.addEventListener("load",e)}(t,(()=>{const e=t.contentDocument;if(e&&k){const n=za(e,{doc:e,mirror:r,blockClass:i,blockSelector:o,unblockSelector:s,maskAllText:a,maskTextClass:c,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:d,skipChild:!1,inlineStylesheet:h,maskInputOptions:f,maskAttributeFn:m,maskTextFn:g,maskInputFn:y,slimDOMOptions:v,dataURLOptions:_,inlineImages:b,recordCanvas:S,preserveWhiteSpace:R,onSerialize:w,onIframeLoad:k,iframeLoadTimeout:x,onStylesheetLoad:C,stylesheetLoadTimeout:E,keepIframeSrcFn:T});n&&k(t,n)}}),x),A.type===la.Element&&"link"===A.tagName&&"stylesheet"===A.attributes.rel&&function(t,e,n){let r,i=!1;try{r=t.sheet}catch(t){return}if(r)return;const o=setTimeout((()=>{i||(e(),i=!0)}),n);t.addEventListener("load",(()=>{clearTimeout(o),i=!0,e()}))}(t,(()=>{if(C){const e=za(t,{doc:n,mirror:r,blockClass:i,blockSelector:o,unblockSelector:s,maskAllText:a,maskTextClass:c,unmaskTextClass:u,maskTextSelector:l,unmaskTextSelector:d,skipChild:!1,inlineStylesheet:h,maskInputOptions:f,maskAttributeFn:m,maskTextFn:g,maskInputFn:y,slimDOMOptions:v,dataURLOptions:_,inlineImages:b,recordCanvas:S,preserveWhiteSpace:R,onSerialize:w,onIframeLoad:k,iframeLoadTimeout:x,onStylesheetLoad:C,stylesheetLoadTimeout:E,keepIframeSrcFn:T});e&&C(t,e)}}),E),A}function Wa(t){let e,n=t[0],r=1;for(;r<t.length;){const i=t[r],o=t[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(e=n,n=o(n)):"call"!==i&&"optionalCall"!==i||(n=o(((...t)=>n.call(e,...t))),e=void 0)}return n}function Ya(t,e,n=document){const r={capture:!0,passive:!0};return n.addEventListener(t,e,r),()=>n.removeEventListener(t,e,r)}const Ga="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.";let Ja={map:{},getId:()=>(console.error(Ga),-1),getNode:()=>(console.error(Ga),null),removeNodeFromMap(){console.error(Ga)},has:()=>(console.error(Ga),!1),reset(){console.error(Ga)}};function Va(t,e,n={}){let r=null,i=0;return function(...o){const s=Date.now();i||!1!==n.leading||(i=s);const a=e-(s-i),c=this;a<=0||a>e?(r&&(function(...t){fc("clearTimeout")(...t)}(r),r=null),i=s,t.apply(c,o)):r||!1===n.trailing||(r=mc((()=>{i=!1===n.leading?0:Date.now(),r=null,t.apply(c,o)}),a))}}function Ka(t,e,n,r,i=window){const o=i.Object.getOwnPropertyDescriptor(t,e);return i.Object.defineProperty(t,e,r?n:{set(t){mc((()=>{n.set.call(this,t)}),0),o&&o.set&&o.set.call(this,t)}}),()=>Ka(t,e,o||{},!0)}function Xa(t,e,n){try{if(!(e in t))return()=>{};const r=t[e],i=n(r);return"function"==typeof i&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:r}})),t[e]=i,()=>{t[e]=r}}catch(t){return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(Ja=new Proxy(Ja,{get:(t,e,n)=>("map"===e&&console.error(Ga),Reflect.get(t,e,n))}));let Qa=Date.now;function Za(t){const e=t.document;return{left:e.scrollingElement?e.scrollingElement.scrollLeft:void 0!==t.pageXOffset?t.pageXOffset:Wa([e,"optionalAccess",t=>t.documentElement,"access",t=>t.scrollLeft])||Wa([e,"optionalAccess",t=>t.body,"optionalAccess",t=>t.parentElement,"optionalAccess",t=>t.scrollLeft])||Wa([e,"optionalAccess",t=>t.body,"optionalAccess",t=>t.scrollLeft])||0,top:e.scrollingElement?e.scrollingElement.scrollTop:void 0!==t.pageYOffset?t.pageYOffset:Wa([e,"optionalAccess",t=>t.documentElement,"access",t=>t.scrollTop])||Wa([e,"optionalAccess",t=>t.body,"optionalAccess",t=>t.parentElement,"optionalAccess",t=>t.scrollTop])||Wa([e,"optionalAccess",t=>t.body,"optionalAccess",t=>t.scrollTop])||0}}function tc(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function ec(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function nc(t){return t?t.nodeType===t.ELEMENT_NODE?t:t.parentElement:null}function rc(t,e,n,r,i){if(!t)return!1;const o=nc(t);if(!o)return!1;const s=Ba(e,n);if(!i){const t=r&&o.matches(r);return s(o)&&!t}const a=Ha(o,s);let c=-1;return!(a<0)&&(r&&(c=Ha(o,Ba(null,r))),a>-1&&c<0||a<c)}function ic(t,e){return e.getId(t)===Ca}function oc(t,e){if(da(t))return!1;const n=e.getId(t);return!e.has(n)||(!t.parentNode||t.parentNode.nodeType!==t.DOCUMENT_NODE)&&(!t.parentNode||oc(t.parentNode,e))}function sc(t){return Boolean(t.changedTouches)}function ac(t,e){return Boolean("IFRAME"===t.nodeName&&e.getMeta(t))}function cc(t,e){return Boolean("LINK"===t.nodeName&&t.nodeType===t.ELEMENT_NODE&&t.getAttribute&&"stylesheet"===t.getAttribute("rel")&&e.getMeta(t))}function uc(t){return Boolean(Wa([t,"optionalAccess",t=>t.shadowRoot]))}/[1-9][0-9]{12}/.test(Date.now().toString())||(Qa=()=>(new Date).getTime());class lc{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(t){return()=>-1,null!=(e=this.styleIDMap.get(t))?e:-1;var e}has(t){return this.styleIDMap.has(t)}add(t,e){if(this.has(t))return this.getId(t);let n;return n=void 0===e?this.id++:e,this.styleIDMap.set(t,n),this.idStyleMap.set(n,t),n}getStyle(t){return this.idStyleMap.get(t)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function dc(t){let e=null;return Wa([t,"access",t=>t.getRootNode,"optionalCall",t=>t(),"optionalAccess",t=>t.nodeType])===Node.DOCUMENT_FRAGMENT_NODE&&t.getRootNode().host&&(e=t.getRootNode().host),e}function pc(t){const e=t.ownerDocument;return!!e&&(e.contains(t)||function(t){const e=t.ownerDocument;if(!e)return!1;const n=function(t){let e,n=t;for(;e=dc(n);)n=e;return n}(t);return e.contains(n)}(t))}const hc={};function fc(t){const e=hc[t];if(e)return e;const n=window.document;let r=window[t];if(n&&"function"==typeof n.createElement)try{const e=n.createElement("iframe");e.hidden=!0,n.head.appendChild(e);const i=e.contentWindow;i&&i[t]&&(r=i[t]),n.head.removeChild(e)}catch(t){}return hc[t]=r.bind(window)}function mc(...t){return fc("setTimeout")(...t)}var gc=(t=>(t[t.DomContentLoaded=0]="DomContentLoaded",t[t.Load=1]="Load",t[t.FullSnapshot=2]="FullSnapshot",t[t.IncrementalSnapshot=3]="IncrementalSnapshot",t[t.Meta=4]="Meta",t[t.Custom=5]="Custom",t[t.Plugin=6]="Plugin",t))(gc||{}),yc=(t=>(t[t.Mutation=0]="Mutation",t[t.MouseMove=1]="MouseMove",t[t.MouseInteraction=2]="MouseInteraction",t[t.Scroll=3]="Scroll",t[t.ViewportResize=4]="ViewportResize",t[t.Input=5]="Input",t[t.TouchMove=6]="TouchMove",t[t.MediaInteraction=7]="MediaInteraction",t[t.StyleSheetRule=8]="StyleSheetRule",t[t.CanvasMutation=9]="CanvasMutation",t[t.Font=10]="Font",t[t.Log=11]="Log",t[t.Drag=12]="Drag",t[t.StyleDeclaration=13]="StyleDeclaration",t[t.Selection=14]="Selection",t[t.AdoptedStyleSheet=15]="AdoptedStyleSheet",t[t.CustomElement=16]="CustomElement",t))(yc||{}),vc=(t=>(t[t.MouseUp=0]="MouseUp",t[t.MouseDown=1]="MouseDown",t[t.Click=2]="Click",t[t.ContextMenu=3]="ContextMenu",t[t.DblClick=4]="DblClick",t[t.Focus=5]="Focus",t[t.Blur=6]="Blur",t[t.TouchStart=7]="TouchStart",t[t.TouchMove_Departed=8]="TouchMove_Departed",t[t.TouchEnd=9]="TouchEnd",t[t.TouchCancel=10]="TouchCancel",t))(vc||{}),_c=(t=>(t[t.Mouse=0]="Mouse",t[t.Pen=1]="Pen",t[t.Touch=2]="Touch",t))(_c||{});function bc(t){let e,n=t[0],r=1;for(;r<t.length;){const i=t[r],o=t[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(e=n,n=o(n)):"call"!==i&&"optionalCall"!==i||(n=o(((...t)=>n.call(e,...t))),e=void 0)}return n}function Sc(t){return"__ln"in t}class wc{constructor(){this.length=0,this.head=null,this.tail=null}get(t){if(t>=this.length)throw new Error("Position outside of list range");let e=this.head;for(let n=0;n<t;n++)e=bc([e,"optionalAccess",t=>t.next])||null;return e}addNode(t){const e={value:t,previous:null,next:null};if(t.__ln=e,t.previousSibling&&Sc(t.previousSibling)){const n=t.previousSibling.__ln.next;e.next=n,e.previous=t.previousSibling.__ln,t.previousSibling.__ln.next=e,n&&(n.previous=e)}else if(t.nextSibling&&Sc(t.nextSibling)&&t.nextSibling.__ln.previous){const n=t.nextSibling.__ln.previous;e.previous=n,e.next=t.nextSibling.__ln,t.nextSibling.__ln.previous=e,n&&(n.next=e)}else this.head&&(this.head.previous=e),e.next=this.head,this.head=e;null===e.next&&(this.tail=e),this.length++}removeNode(t){const e=t.__ln;this.head&&(e.previous?(e.previous.next=e.next,e.next?e.next.previous=e.previous:this.tail=e.previous):(this.head=e.next,this.head?this.head.previous=null:this.tail=null),t.__ln&&delete t.__ln,this.length--)}}const kc=(t,e)=>`${t}@${e}`;class xc{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=t=>{t.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;const t=[],e=new Set,n=new wc,r=t=>{let e=t,n=Ca;for(;n===Ca;)e=e&&e.nextSibling,n=e&&this.mirror.getId(e);return n},i=i=>{if(!i.parentNode||!pc(i))return;const o=da(i.parentNode)?this.mirror.getId(dc(i)):this.mirror.getId(i.parentNode),s=r(i);if(-1===o||-1===s)return n.addNode(i);const a=za(i,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:t=>{ac(t,this.mirror)&&this.iframeManager.addIframe(t),cc(t,this.mirror)&&this.stylesheetManager.trackLinkElement(t),uc(i)&&this.shadowDomManager.addShadowRoot(i.shadowRoot,this.doc)},onIframeLoad:(t,e)=>{this.iframeManager.attachIframe(t,e),this.shadowDomManager.observeAttachShadow(t)},onStylesheetLoad:(t,e)=>{this.stylesheetManager.attachLinkElement(t,e)}});a&&(t.push({parentId:o,nextId:s,node:a}),e.add(a.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(const t of this.movedSet)Ec(this.removes,t,this.mirror)&&!this.movedSet.has(t.parentNode)||i(t);for(const t of this.addedSet)Ic(this.droppedSet,t)||Ec(this.removes,t,this.mirror)?Ic(this.movedSet,t)?i(t):this.droppedSet.add(t):i(t);let o=null;for(;n.length;){let t=null;if(o){const e=this.mirror.getId(o.value.parentNode),n=r(o.value);-1!==e&&-1!==n&&(t=o)}if(!t){let e=n.tail;for(;e;){const n=e;if(e=e.previous,n){const e=this.mirror.getId(n.value.parentNode);if(-1===r(n.value))continue;if(-1!==e){t=n;break}{const e=n.value;if(e.parentNode&&e.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const r=e.parentNode.host;if(-1!==this.mirror.getId(r)){t=n;break}}}}}}if(!t){for(;n.head;)n.removeNode(n.head.value);break}o=t.previous,n.removeNode(t.value),i(t.value)}const s={texts:this.texts.map((t=>({id:this.mirror.getId(t.node),value:t.value}))).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),attributes:this.attributes.map((t=>{const{attributes:e}=t;if("string"==typeof e.style){const n=JSON.stringify(t.styleDiff),r=JSON.stringify(t._unchangedStyles);n.length<e.style.length&&(n+r).split("var(").length===e.style.split("var(").length&&(e.style=t.styleDiff)}return{id:this.mirror.getId(t.node),attributes:e}})).filter((t=>!e.has(t.id))).filter((t=>this.mirror.has(t.id))),removes:this.removes,adds:t};(s.texts.length||s.attributes.length||s.removes.length||s.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(s))},this.processMutation=t=>{if(!ic(t.target,this.mirror))switch(t.type){case"characterData":{const e=t.target.textContent;rc(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||e===t.oldValue||this.texts.push({value:Ua(t.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&e?this.maskTextFn?this.maskTextFn(e,nc(t.target)):e.replace(/[\S]/g,"*"):e,node:t.target});break}case"attributes":{const e=t.target;let n=t.attributeName,r=t.target.getAttribute(n);if("value"===n){const n=Sa(e),i=e.tagName;r=wa(e,i,n);const o=ga({maskInputOptions:this.maskInputOptions,tagName:i,type:n});r=ya({isMasked:Ua(t.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,o),element:e,value:r,maskInputFn:this.maskInputFn})}if(rc(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||r===t.oldValue)return;let i=this.attributeMap.get(t.target);if("IFRAME"===e.tagName&&"src"===n&&!this.keepIframeSrcFn(r)){if(e.contentDocument)return;n="rr_src"}if(i||(i={node:t.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(i),this.attributeMap.set(t.target,i)),"type"===n&&"INPUT"===e.tagName&&"password"===(t.oldValue||"").toLowerCase()&&e.setAttribute("data-rr-is-password","true"),!$a(e.tagName,n)&&(i.attributes[n]=ja(this.doc,va(e.tagName),va(n),r,e,this.maskAttributeFn),"style"===n)){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(t){this.unattachedDoc=this.doc}const n=this.unattachedDoc.createElement("span");t.oldValue&&n.setAttribute("style",t.oldValue);for(const t of Array.from(e.style)){const r=e.style.getPropertyValue(t),o=e.style.getPropertyPriority(t);r!==n.style.getPropertyValue(t)||o!==n.style.getPropertyPriority(t)?i.styleDiff[t]=""===o?r:[r,o]:i._unchangedStyles[t]=[r,o]}for(const t of Array.from(n.style))""===e.style.getPropertyValue(t)&&(i.styleDiff[t]=!1)}break}case"childList":if(rc(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;t.addedNodes.forEach((e=>this.genAdds(e,t.target))),t.removedNodes.forEach((e=>{const n=this.mirror.getId(e),r=da(t.target)?this.mirror.getId(t.target.host):this.mirror.getId(t.target);rc(t.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||ic(e,this.mirror)||!function(t,e){return-1!==e.getId(t)}(e,this.mirror)||(this.addedSet.has(e)?(Cc(this.addedSet,e),this.droppedSet.add(e)):this.addedSet.has(t.target)&&-1===n||oc(t.target,this.mirror)||(this.movedSet.has(e)&&this.movedMap[kc(n,r)]?Cc(this.movedSet,e):this.removes.push({parentId:r,id:n,isShadow:!(!da(t.target)||!pa(t.target))||void 0})),this.mapRemoves.push(e))}))}},this.genAdds=(t,e)=>{if(!this.processedNodeManager.inOtherBuffer(t,this)&&!this.addedSet.has(t)&&!this.movedSet.has(t)){if(this.mirror.hasNode(t)){if(ic(t,this.mirror))return;this.movedSet.add(t);let n=null;e&&this.mirror.hasNode(e)&&(n=this.mirror.getId(e)),n&&-1!==n&&(this.movedMap[kc(this.mirror.getId(t),n)]=!0)}else this.addedSet.add(t),this.droppedSet.delete(t);rc(t,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(t.childNodes.forEach((t=>this.genAdds(t))),uc(t)&&t.shadowRoot.childNodes.forEach((e=>{this.processedNodeManager.add(e,this),this.genAdds(e,t)})))}}}init(t){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach((e=>{this[e]=t[e]}))}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function Cc(t,e){t.delete(e),e.childNodes.forEach((e=>Cc(t,e)))}function Ec(t,e,n){return 0!==t.length&&Tc(t,e,n)}function Tc(t,e,n){const{parentNode:r}=e;if(!r)return!1;const i=n.getId(r);return!!t.some((t=>t.id===i))||Tc(t,r,n)}function Ic(t,e){return 0!==t.size&&Rc(t,e)}function Rc(t,e){const{parentNode:n}=e;return!!n&&(!!t.has(n)||Rc(t,n))}let Oc;function Mc(t){Oc=t}function Ac(){Oc=void 0}const Dc=t=>Oc?(...e)=>{try{return t(...e)}catch(t){if(Oc&&!0===Oc(t))return()=>{};throw t}}:t;function Nc(t){let e,n=t[0],r=1;for(;r<t.length;){const i=t[r],o=t[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(e=n,n=o(n)):"call"!==i&&"optionalCall"!==i||(n=o(((...t)=>n.call(e,...t))),e=void 0)}return n}const Lc=[];function Pc(t){try{if("composedPath"in t){const e=t.composedPath();if(e.length)return e[0]}else if("path"in t&&t.path.length)return t.path[0]}catch(t){}return t&&t.target}function Fc(t,e){const n=new xc;Lc.push(n),n.init(t);let r=window.MutationObserver||window.__rrMutationObserver;const i=Nc([window,"optionalAccess",t=>t.Zone,"optionalAccess",t=>t.__symbol__,"optionalCall",t=>t("MutationObserver")]);i&&window[i]&&(r=window[i]);const o=new r(Dc((e=>{t.onMutation&&!1===t.onMutation(e)||n.processMutations.bind(n)(e)})));return o.observe(e,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),o}function jc({scrollCb:t,doc:e,mirror:n,blockClass:r,blockSelector:i,unblockSelector:o,sampling:s}){return Ya("scroll",Dc(Va(Dc((s=>{const a=Pc(s);if(!a||rc(a,r,i,o,!0))return;const c=n.getId(a);if(a===e&&e.defaultView){const n=Za(e.defaultView);t({id:c,x:n.left,y:n.top})}else t({id:c,x:a.scrollLeft,y:a.scrollTop})})),s.scroll||100)),e)}const $c=["INPUT","TEXTAREA","SELECT"],Hc=new WeakMap;function Bc({inputCb:t,doc:e,mirror:n,blockClass:r,blockSelector:i,unblockSelector:o,ignoreClass:s,ignoreSelector:a,maskInputOptions:c,maskInputFn:u,sampling:l,userTriggeredOnInput:d,maskTextClass:p,unmaskTextClass:h,maskTextSelector:f,unmaskTextSelector:m}){function g(t){let n=Pc(t);const l=t.isTrusted,g=n&&_a(n.tagName);if("OPTION"===g&&(n=n.parentElement),!n||!g||$c.indexOf(g)<0||rc(n,r,i,o,!0))return;const v=n;if(v.classList.contains(s)||a&&v.matches(a))return;const _=Sa(n);let b=wa(v,g,_),S=!1;const w=ga({maskInputOptions:c,tagName:g,type:_}),k=Ua(n,p,f,h,m,w);"radio"!==_&&"checkbox"!==_||(S=n.checked),b=ya({isMasked:k,element:n,value:b,maskInputFn:u}),y(n,d?{text:b,isChecked:S,userTriggered:l}:{text:b,isChecked:S});const x=n.name;"radio"===_&&x&&S&&e.querySelectorAll(`input[type="radio"][name="${x}"]`).forEach((t=>{if(t!==n){const e=ya({isMasked:k,element:t,value:wa(t,g,_),maskInputFn:u});y(t,d?{text:e,isChecked:!S,userTriggered:!1}:{text:e,isChecked:!S})}}))}function y(e,r){const i=Hc.get(e);if(!i||i.text!==r.text||i.isChecked!==r.isChecked){Hc.set(e,r);const i=n.getId(e);Dc(t)({...r,id:i})}}const v=("last"===l.input?["change"]:["input","change"]).map((t=>Ya(t,Dc(g),e))),_=e.defaultView;if(!_)return()=>{v.forEach((t=>t()))};const b=_.Object.getOwnPropertyDescriptor(_.HTMLInputElement.prototype,"value"),S=[[_.HTMLInputElement.prototype,"value"],[_.HTMLInputElement.prototype,"checked"],[_.HTMLSelectElement.prototype,"value"],[_.HTMLTextAreaElement.prototype,"value"],[_.HTMLSelectElement.prototype,"selectedIndex"],[_.HTMLOptionElement.prototype,"selected"]];return b&&b.set&&v.push(...S.map((t=>Ka(t[0],t[1],{set(){Dc(g)({target:this,isTrusted:!1})}},!1,_)))),Dc((()=>{v.forEach((t=>t()))}))}function Uc(t){return function(t,e){if(Yc("CSSGroupingRule")&&t.parentRule instanceof CSSGroupingRule||Yc("CSSMediaRule")&&t.parentRule instanceof CSSMediaRule||Yc("CSSSupportsRule")&&t.parentRule instanceof CSSSupportsRule||Yc("CSSConditionRule")&&t.parentRule instanceof CSSConditionRule){const n=Array.from(t.parentRule.cssRules).indexOf(t);e.unshift(n)}else if(t.parentStyleSheet){const n=Array.from(t.parentStyleSheet.cssRules).indexOf(t);e.unshift(n)}return e}(t,[])}function qc(t,e,n){let r,i;return t?(t.ownerNode?r=e.getId(t.ownerNode):i=n.getId(t),{styleId:i,id:r}):{}}function zc({mirror:t,stylesheetManager:e},n){let r=null;r="#document"===n.nodeName?t.getId(n):t.getId(n.host);const i="#document"===n.nodeName?Nc([n,"access",t=>t.defaultView,"optionalAccess",t=>t.Document]):Nc([n,"access",t=>t.ownerDocument,"optionalAccess",t=>t.defaultView,"optionalAccess",t=>t.ShadowRoot]),o=Nc([i,"optionalAccess",t=>t.prototype])?Object.getOwnPropertyDescriptor(Nc([i,"optionalAccess",t=>t.prototype]),"adoptedStyleSheets"):void 0;return null!==r&&-1!==r&&i&&o?(Object.defineProperty(n,"adoptedStyleSheets",{configurable:o.configurable,enumerable:o.enumerable,get(){return Nc([o,"access",t=>t.get,"optionalAccess",t=>t.call,"call",t=>t(this)])},set(t){const n=Nc([o,"access",t=>t.set,"optionalAccess",t=>t.call,"call",e=>e(this,t)]);if(null!==r&&-1!==r)try{e.adoptStyleSheets(t,r)}catch(t){}return n}}),Dc((()=>{Object.defineProperty(n,"adoptedStyleSheets",{configurable:o.configurable,enumerable:o.enumerable,get:o.get,set:o.set})}))):()=>{}}function Wc(t,e={}){const n=t.doc.defaultView;if(!n)return()=>{};const r=Fc(t,t.doc),i=function({mousemoveCb:t,sampling:e,doc:n,mirror:r}){if(!1===e.mousemove)return()=>{};const i="number"==typeof e.mousemove?e.mousemove:50,o="number"==typeof e.mousemoveCallback?e.mousemoveCallback:500;let s,a=[];const c=Va(Dc((e=>{const n=Date.now()-s;t(a.map((t=>(t.timeOffset-=n,t))),e),a=[],s=null})),o),u=Dc(Va(Dc((t=>{const e=Pc(t),{clientX:n,clientY:i}=sc(t)?t.changedTouches[0]:t;s||(s=Qa()),a.push({x:n,y:i,id:r.getId(e),timeOffset:Qa()-s}),c("undefined"!=typeof DragEvent&&t instanceof DragEvent?yc.Drag:t instanceof MouseEvent?yc.MouseMove:yc.TouchMove)})),i,{trailing:!1})),l=[Ya("mousemove",u,n),Ya("touchmove",u,n),Ya("drag",u,n)];return Dc((()=>{l.forEach((t=>t()))}))}(t),o=function({mouseInteractionCb:t,doc:e,mirror:n,blockClass:r,blockSelector:i,unblockSelector:o,sampling:s}){if(!1===s.mouseInteraction)return()=>{};const a=!0===s.mouseInteraction||void 0===s.mouseInteraction?{}:s.mouseInteraction,c=[];let u=null;return Object.keys(vc).filter((t=>Number.isNaN(Number(t))&&!t.endsWith("_Departed")&&!1!==a[t])).forEach((s=>{let a=va(s);const l=(e=>s=>{const a=Pc(s);if(rc(a,r,i,o,!0))return;let c=null,l=e;if("pointerType"in s){switch(s.pointerType){case"mouse":c=_c.Mouse;break;case"touch":c=_c.Touch;break;case"pen":c=_c.Pen}c===_c.Touch?vc[e]===vc.MouseDown?l="TouchStart":vc[e]===vc.MouseUp&&(l="TouchEnd"):_c.Pen}else sc(s)&&(c=_c.Touch);null!==c?(u=c,(l.startsWith("Touch")&&c===_c.Touch||l.startsWith("Mouse")&&c===_c.Mouse)&&(c=null)):vc[e]===vc.Click&&(c=u,u=null);const d=sc(s)?s.changedTouches[0]:s;if(!d)return;const p=n.getId(a),{clientX:h,clientY:f}=d;Dc(t)({type:vc[l],id:p,x:h,y:f,...null!==c&&{pointerType:c}})})(s);if(window.PointerEvent)switch(vc[s]){case vc.MouseDown:case vc.MouseUp:a=a.replace("mouse","pointer");break;case vc.TouchStart:case vc.TouchEnd:return}c.push(Ya(a,l,e))})),Dc((()=>{c.forEach((t=>t()))}))}(t),s=jc(t),a=function({viewportResizeCb:t},{win:e}){let n=-1,r=-1;return Ya("resize",Dc(Va(Dc((()=>{const e=tc(),i=ec();n===e&&r===i||(t({width:Number(i),height:Number(e)}),n=e,r=i)})),200)),e)}(t,{win:n}),c=Bc(t),u=function({mediaInteractionCb:t,blockClass:e,blockSelector:n,unblockSelector:r,mirror:i,sampling:o,doc:s}){const a=Dc((s=>Va(Dc((o=>{const a=Pc(o);if(!a||rc(a,e,n,r,!0))return;const{currentTime:c,volume:u,muted:l,playbackRate:d}=a;t({type:s,id:i.getId(a),currentTime:c,volume:u,muted:l,playbackRate:d})})),o.media||500))),c=[Ya("play",a(0),s),Ya("pause",a(1),s),Ya("seeked",a(2),s),Ya("volumechange",a(3),s),Ya("ratechange",a(4),s)];return Dc((()=>{c.forEach((t=>t()))}))}(t),l=function({styleSheetRuleCb:t,mirror:e,stylesheetManager:n},{win:r}){if(!r.CSSStyleSheet||!r.CSSStyleSheet.prototype)return()=>{};const i=r.CSSStyleSheet.prototype.insertRule;r.CSSStyleSheet.prototype.insertRule=new Proxy(i,{apply:Dc(((r,i,o)=>{const[s,a]=o,{id:c,styleId:u}=qc(i,e,n.styleMirror);return(c&&-1!==c||u&&-1!==u)&&t({id:c,styleId:u,adds:[{rule:s,index:a}]}),r.apply(i,o)}))});const o=r.CSSStyleSheet.prototype.deleteRule;let s,a;r.CSSStyleSheet.prototype.deleteRule=new Proxy(o,{apply:Dc(((r,i,o)=>{const[s]=o,{id:a,styleId:c}=qc(i,e,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&t({id:a,styleId:c,removes:[{index:s}]}),r.apply(i,o)}))}),r.CSSStyleSheet.prototype.replace&&(s=r.CSSStyleSheet.prototype.replace,r.CSSStyleSheet.prototype.replace=new Proxy(s,{apply:Dc(((r,i,o)=>{const[s]=o,{id:a,styleId:c}=qc(i,e,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&t({id:a,styleId:c,replace:s}),r.apply(i,o)}))})),r.CSSStyleSheet.prototype.replaceSync&&(a=r.CSSStyleSheet.prototype.replaceSync,r.CSSStyleSheet.prototype.replaceSync=new Proxy(a,{apply:Dc(((r,i,o)=>{const[s]=o,{id:a,styleId:c}=qc(i,e,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&t({id:a,styleId:c,replaceSync:s}),r.apply(i,o)}))}));const c={};Gc("CSSGroupingRule")?c.CSSGroupingRule=r.CSSGroupingRule:(Gc("CSSMediaRule")&&(c.CSSMediaRule=r.CSSMediaRule),Gc("CSSConditionRule")&&(c.CSSConditionRule=r.CSSConditionRule),Gc("CSSSupportsRule")&&(c.CSSSupportsRule=r.CSSSupportsRule));const u={};return Object.entries(c).forEach((([r,i])=>{u[r]={insertRule:i.prototype.insertRule,deleteRule:i.prototype.deleteRule},i.prototype.insertRule=new Proxy(u[r].insertRule,{apply:Dc(((r,i,o)=>{const[s,a]=o,{id:c,styleId:u}=qc(i.parentStyleSheet,e,n.styleMirror);return(c&&-1!==c||u&&-1!==u)&&t({id:c,styleId:u,adds:[{rule:s,index:[...Uc(i),a||0]}]}),r.apply(i,o)}))}),i.prototype.deleteRule=new Proxy(u[r].deleteRule,{apply:Dc(((r,i,o)=>{const[s]=o,{id:a,styleId:c}=qc(i.parentStyleSheet,e,n.styleMirror);return(a&&-1!==a||c&&-1!==c)&&t({id:a,styleId:c,removes:[{index:[...Uc(i),s]}]}),r.apply(i,o)}))})})),Dc((()=>{r.CSSStyleSheet.prototype.insertRule=i,r.CSSStyleSheet.prototype.deleteRule=o,s&&(r.CSSStyleSheet.prototype.replace=s),a&&(r.CSSStyleSheet.prototype.replaceSync=a),Object.entries(c).forEach((([t,e])=>{e.prototype.insertRule=u[t].insertRule,e.prototype.deleteRule=u[t].deleteRule}))}))}(t,{win:n}),d=zc(t,t.doc),p=function({styleDeclarationCb:t,mirror:e,ignoreCSSAttributes:n,stylesheetManager:r},{win:i}){const o=i.CSSStyleDeclaration.prototype.setProperty;i.CSSStyleDeclaration.prototype.setProperty=new Proxy(o,{apply:Dc(((i,s,a)=>{const[c,u,l]=a;if(n.has(c))return o.apply(s,[c,u,l]);const{id:d,styleId:p}=qc(Nc([s,"access",t=>t.parentRule,"optionalAccess",t=>t.parentStyleSheet]),e,r.styleMirror);return(d&&-1!==d||p&&-1!==p)&&t({id:d,styleId:p,set:{property:c,value:u,priority:l},index:Uc(s.parentRule)}),i.apply(s,a)}))});const s=i.CSSStyleDeclaration.prototype.removeProperty;return i.CSSStyleDeclaration.prototype.removeProperty=new Proxy(s,{apply:Dc(((i,o,a)=>{const[c]=a;if(n.has(c))return s.apply(o,[c]);const{id:u,styleId:l}=qc(Nc([o,"access",t=>t.parentRule,"optionalAccess",t=>t.parentStyleSheet]),e,r.styleMirror);return(u&&-1!==u||l&&-1!==l)&&t({id:u,styleId:l,remove:{property:c},index:Uc(o.parentRule)}),i.apply(o,a)}))}),Dc((()=>{i.CSSStyleDeclaration.prototype.setProperty=o,i.CSSStyleDeclaration.prototype.removeProperty=s}))}(t,{win:n}),h=t.collectFonts?function({fontCb:t,doc:e}){const n=e.defaultView;if(!n)return()=>{};const r=[],i=new WeakMap,o=n.FontFace;n.FontFace=function(t,e,n){const r=new o(t,e,n);return i.set(r,{family:t,buffer:"string"!=typeof e,descriptors:n,fontSource:"string"==typeof e?e:JSON.stringify(Array.from(new Uint8Array(e)))}),r};const s=Xa(e.fonts,"add",(function(e){return function(n){return mc(Dc((()=>{const e=i.get(n);e&&(t(e),i.delete(n))})),0),e.apply(this,[n])}}));return r.push((()=>{n.FontFace=o})),r.push(s),Dc((()=>{r.forEach((t=>t()))}))}(t):()=>{},f=function(t){const{doc:e,mirror:n,blockClass:r,blockSelector:i,unblockSelector:o,selectionCb:s}=t;let a=!0;const c=Dc((()=>{const t=e.getSelection();if(!t||a&&Nc([t,"optionalAccess",t=>t.isCollapsed]))return;a=t.isCollapsed||!1;const c=[],u=t.rangeCount||0;for(let e=0;e<u;e++){const s=t.getRangeAt(e),{startContainer:a,startOffset:u,endContainer:l,endOffset:d}=s;rc(a,r,i,o,!0)||rc(l,r,i,o,!0)||c.push({start:n.getId(a),startOffset:u,end:n.getId(l),endOffset:d})}s({ranges:c})}));return c(),Ya("selectionchange",c)}(t),m=function({doc:t,customElementCb:e}){const n=t.defaultView;return n&&n.customElements?Xa(n.customElements,"define",(function(t){return function(n,r,i){try{e({define:{name:n}})}catch(t){}return t.apply(this,[n,r,i])}})):()=>{}}(t),g=[];for(const e of t.plugins)g.push(e.observer(e.callback,n,e.options));return Dc((()=>{Lc.forEach((t=>t.reset())),r.disconnect(),i(),o(),s(),a(),c(),u(),l(),d(),p(),h(),f(),m(),g.forEach((t=>t()))}))}function Yc(t){return void 0!==window[t]}function Gc(t){return Boolean(void 0!==window[t]&&window[t].prototype&&"insertRule"in window[t].prototype&&"deleteRule"in window[t].prototype)}class Jc{constructor(t){this.generateIdFn=t,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(t,e,n,r){const i=n||this.getIdToRemoteIdMap(t),o=r||this.getRemoteIdToIdMap(t);let s=i.get(e);return s||(s=this.generateIdFn(),i.set(e,s),o.set(s,e)),s}getIds(t,e){const n=this.getIdToRemoteIdMap(t),r=this.getRemoteIdToIdMap(t);return e.map((e=>this.getId(t,e,n,r)))}getRemoteId(t,e,n){const r=n||this.getRemoteIdToIdMap(t);if("number"!=typeof e)return e;return r.get(e)||-1}getRemoteIds(t,e){const n=this.getRemoteIdToIdMap(t);return e.map((e=>this.getRemoteId(t,e,n)))}reset(t){if(!t)return this.iframeIdToRemoteIdMap=new WeakMap,void(this.iframeRemoteIdToIdMap=new WeakMap);this.iframeIdToRemoteIdMap.delete(t),this.iframeRemoteIdToIdMap.delete(t)}getIdToRemoteIdMap(t){let e=this.iframeIdToRemoteIdMap.get(t);return e||(e=new Map,this.iframeIdToRemoteIdMap.set(t,e)),e}getRemoteIdToIdMap(t){let e=this.iframeRemoteIdToIdMap.get(t);return e||(e=new Map,this.iframeRemoteIdToIdMap.set(t,e)),e}}function Vc(t){let e,n=t[0],r=1;for(;r<t.length;){const i=t[r],o=t[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(e=n,n=o(n)):"call"!==i&&"optionalCall"!==i||(n=o(((...t)=>n.call(e,...t))),e=void 0)}return n}class Kc{constructor(){this.crossOriginIframeMirror=new Jc(Ea),this.crossOriginIframeRootIdMap=new WeakMap}addIframe(){}addLoadListener(){}attachIframe(){}}class Xc{constructor(t){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new Jc(Ea),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=t.mutationCb,this.wrappedEmit=t.wrappedEmit,this.stylesheetManager=t.stylesheetManager,this.recordCrossOriginIframes=t.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new Jc(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=t.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(t){this.iframes.set(t,!0),t.contentWindow&&this.crossOriginIframeMap.set(t.contentWindow,t)}addLoadListener(t){this.loadListener=t}attachIframe(t,e){this.mutationCb({adds:[{parentId:this.mirror.getId(t),nextId:null,node:e}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),Vc([this,"access",t=>t.loadListener,"optionalCall",e=>e(t)]),t.contentDocument&&t.contentDocument.adoptedStyleSheets&&t.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(t.contentDocument.adoptedStyleSheets,this.mirror.getId(t.contentDocument))}handleMessage(t){const e=t;if("rrweb"!==e.data.type||e.origin!==e.data.origin)return;if(!t.source)return;const n=this.crossOriginIframeMap.get(t.source);if(!n)return;const r=this.transformCrossOriginEvent(n,e.data.event);r&&this.wrappedEmit(r,e.data.isCheckout)}transformCrossOriginEvent(t,e){switch(e.type){case gc.FullSnapshot:{this.crossOriginIframeMirror.reset(t),this.crossOriginIframeStyleMirror.reset(t),this.replaceIdOnNode(e.data.node,t);const n=e.data.node.id;return this.crossOriginIframeRootIdMap.set(t,n),this.patchRootIdOnNode(e.data.node,n),{timestamp:e.timestamp,type:gc.IncrementalSnapshot,data:{source:yc.Mutation,adds:[{parentId:this.mirror.getId(t),nextId:null,node:e.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case gc.Meta:case gc.Load:case gc.DomContentLoaded:return!1;case gc.Plugin:return e;case gc.Custom:return this.replaceIds(e.data.payload,t,["id","parentId","previousId","nextId"]),e;case gc.IncrementalSnapshot:switch(e.data.source){case yc.Mutation:return e.data.adds.forEach((e=>{this.replaceIds(e,t,["parentId","nextId","previousId"]),this.replaceIdOnNode(e.node,t);const n=this.crossOriginIframeRootIdMap.get(t);n&&this.patchRootIdOnNode(e.node,n)})),e.data.removes.forEach((e=>{this.replaceIds(e,t,["parentId","id"])})),e.data.attributes.forEach((e=>{this.replaceIds(e,t,["id"])})),e.data.texts.forEach((e=>{this.replaceIds(e,t,["id"])})),e;case yc.Drag:case yc.TouchMove:case yc.MouseMove:return e.data.positions.forEach((e=>{this.replaceIds(e,t,["id"])})),e;case yc.ViewportResize:return!1;case yc.MediaInteraction:case yc.MouseInteraction:case yc.Scroll:case yc.CanvasMutation:case yc.Input:return this.replaceIds(e.data,t,["id"]),e;case yc.StyleSheetRule:case yc.StyleDeclaration:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleId"]),e;case yc.Font:return e;case yc.Selection:return e.data.ranges.forEach((e=>{this.replaceIds(e,t,["start","end"])})),e;case yc.AdoptedStyleSheet:return this.replaceIds(e.data,t,["id"]),this.replaceStyleIds(e.data,t,["styleIds"]),Vc([e,"access",t=>t.data,"access",t=>t.styles,"optionalAccess",t=>t.forEach,"call",e=>e((e=>{this.replaceStyleIds(e,t,["styleId"])}))]),e}}return!1}replace(t,e,n,r){for(const i of r)(Array.isArray(e[i])||"number"==typeof e[i])&&(Array.isArray(e[i])?e[i]=t.getIds(n,e[i]):e[i]=t.getId(n,e[i]));return e}replaceIds(t,e,n){return this.replace(this.crossOriginIframeMirror,t,e,n)}replaceStyleIds(t,e,n){return this.replace(this.crossOriginIframeStyleMirror,t,e,n)}replaceIdOnNode(t,e){this.replaceIds(t,e,["id","rootId"]),"childNodes"in t&&t.childNodes.forEach((t=>{this.replaceIdOnNode(t,e)}))}patchRootIdOnNode(t,e){t.type===la.Document||t.rootId||(t.rootId=e),"childNodes"in t&&t.childNodes.forEach((t=>{this.patchRootIdOnNode(t,e)}))}}class Qc{init(){}addShadowRoot(){}observeAttachShadow(){}reset(){}}class Zc{constructor(t){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=t.mutationCb,this.scrollCb=t.scrollCb,this.bypassOptions=t.bypassOptions,this.mirror=t.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(t,e){if(!pa(t))return;if(this.shadowDoms.has(t))return;this.shadowDoms.add(t);const n=Fc({...this.bypassOptions,doc:e,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this},t);this.restoreHandlers.push((()=>n.disconnect())),this.restoreHandlers.push(jc({...this.bypassOptions,scrollCb:this.scrollCb,doc:t,mirror:this.mirror})),mc((()=>{t.adoptedStyleSheets&&t.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(t.adoptedStyleSheets,this.mirror.getId(t.host)),this.restoreHandlers.push(zc({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},t))}),0)}observeAttachShadow(t){t.contentWindow&&t.contentDocument&&this.patchAttachShadow(t.contentWindow.Element,t.contentDocument)}patchAttachShadow(t,e){const n=this;this.restoreHandlers.push(Xa(t.prototype,"attachShadow",(function(t){return function(r){const i=t.call(this,r);return this.shadowRoot&&pc(this)&&n.addShadowRoot(this.shadowRoot,e),i}})))}reset(){this.restoreHandlers.forEach((t=>{try{t()}catch(t){}})),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}class tu{reset(){}freeze(){}unfreeze(){}lock(){}unlock(){}snapshot(){}}class eu{constructor(t){this.trackedLinkElements=new WeakSet,this.styleMirror=new lc,this.mutationCb=t.mutationCb,this.adoptedStyleSheetCb=t.adoptedStyleSheetCb}attachLinkElement(t,e){"_cssText"in e.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:e.id,attributes:e.attributes}]}),this.trackLinkElement(t)}trackLinkElement(t){this.trackedLinkElements.has(t)||(this.trackedLinkElements.add(t),this.trackStylesheetInLinkElement(t))}adoptStyleSheets(t,e){if(0===t.length)return;const n={id:e,styleIds:[]},r=[];for(const e of t){let t;this.styleMirror.has(e)?t=this.styleMirror.getId(e):(t=this.styleMirror.add(e),r.push({styleId:t,rules:Array.from(e.rules||CSSRule,((t,e)=>({rule:fa(t),index:e})))})),n.styleIds.push(t)}r.length>0&&(n.styles=r),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(t){}}class nu{constructor(){this.nodeMap=new WeakMap,this.loop=!0,this.periodicallyClear()}periodicallyClear(){!function(...t){fc("requestAnimationFrame")(...t)}((()=>{this.clear(),this.loop&&this.periodicallyClear()}))}inOtherBuffer(t,e){const n=this.nodeMap.get(t);return n&&Array.from(n).some((t=>t!==e))}add(t,e){this.nodeMap.set(t,(this.nodeMap.get(t)||new Set).add(e))}clear(){this.nodeMap=new WeakMap}destroy(){this.loop=!1}}let ru,iu;const ou=new ma;function su(t={}){const{emit:e,checkoutEveryNms:n,checkoutEveryNth:r,blockClass:i="rr-block",blockSelector:o=null,unblockSelector:s=null,ignoreClass:a="rr-ignore",ignoreSelector:c=null,maskAllText:u=!1,maskTextClass:l="rr-mask",unmaskTextClass:d=null,maskTextSelector:p=null,unmaskTextSelector:h=null,inlineStylesheet:f=!0,maskAllInputs:m,maskInputOptions:g,slimDOMOptions:y,maskAttributeFn:v,maskInputFn:_,maskTextFn:b,maxCanvasSize:S=null,packFn:w,sampling:k={},dataURLOptions:x={},mousemoveWait:C,recordCanvas:E=!1,recordCrossOriginIframes:T=!1,recordAfter:I=("DOMContentLoaded"===t.recordAfter?t.recordAfter:"load"),userTriggeredOnInput:R=!1,collectFonts:O=!1,inlineImages:M=!1,plugins:A,keepIframeSrcFn:D=(()=>!1),ignoreCSSAttributes:N=new Set([]),errorHandler:L,onMutation:P,getCanvasManager:F}=t;Mc(L);const j=!T||window.parent===window;let $=!1;if(!j)try{window.parent.document&&($=!1)}catch(t){$=!0}if(j&&!e)throw new Error("emit function is required");void 0!==C&&void 0===k.mousemove&&(k.mousemove=C),ou.reset();const H=!0===m?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==g?g:{},B=!0===y||"all"===y?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===y,headMetaDescKeywords:"all"===y}:y||{};let U;!function(t=window){"NodeList"in t&&!t.NodeList.prototype.forEach&&(t.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in t&&!t.DOMTokenList.prototype.forEach&&(t.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...t)=>{let e=t[0];if(!(0 in t))throw new TypeError("1 argument is required");do{if(this===e)return!0}while(e=e&&e.parentNode);return!1})}();let q=0;const z=t=>{for(const e of A||[])e.eventProcessor&&(t=e.eventProcessor(t));return w&&!$&&(t=w(t)),t};ru=(t,i)=>{const o=t;if(o.timestamp=Qa(),!ss([Lc,"access",t=>t[0],"optionalAccess",t=>t.isFrozen,"call",t=>t()])||o.type===gc.FullSnapshot||o.type===gc.IncrementalSnapshot&&o.data.source===yc.Mutation||Lc.forEach((t=>t.unfreeze())),j)ss([e,"optionalCall",t=>t(z(o),i)]);else if($){const t={type:"rrweb",event:z(o),origin:window.location.origin,isCheckout:i};window.parent.postMessage(t,"*")}if(o.type===gc.FullSnapshot)U=o,q=0;else if(o.type===gc.IncrementalSnapshot){if(o.data.source===yc.Mutation&&o.data.isAttachIframe)return;q++;const t=r&&q>=r,e=n&&U&&o.timestamp-U.timestamp>n;(t||e)&&Z(!0)}};const W=t=>{ru({type:gc.IncrementalSnapshot,data:{source:yc.Mutation,...t}})},Y=t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.Scroll,...t}}),G=t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.CanvasMutation,...t}}),J=new eu({mutationCb:W,adoptedStyleSheetCb:t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.AdoptedStyleSheet,...t}})}),V="boolean"==typeof __RRWEB_EXCLUDE_IFRAME__&&__RRWEB_EXCLUDE_IFRAME__?new Kc:new Xc({mirror:ou,mutationCb:W,stylesheetManager:J,recordCrossOriginIframes:T,wrappedEmit:ru});for(const t of A||[])t.getMirror&&t.getMirror({nodeMirror:ou,crossOriginIframeMirror:V.crossOriginIframeMirror,crossOriginIframeStyleMirror:V.crossOriginIframeStyleMirror});const K=new nu,X=function(t,e){try{return t?t(e):new tu}catch(t){return console.warn("Unable to initialize CanvasManager"),new tu}}(F,{mirror:ou,win:window,mutationCb:t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.CanvasMutation,...t}}),recordCanvas:E,blockClass:i,blockSelector:o,unblockSelector:s,maxCanvasSize:S,sampling:k.canvas,dataURLOptions:x,errorHandler:L}),Q="boolean"==typeof __RRWEB_EXCLUDE_SHADOW_DOM__&&__RRWEB_EXCLUDE_SHADOW_DOM__?new Qc:new Zc({mutationCb:W,scrollCb:Y,bypassOptions:{onMutation:P,blockClass:i,blockSelector:o,unblockSelector:s,maskAllText:u,maskTextClass:l,unmaskTextClass:d,maskTextSelector:p,unmaskTextSelector:h,inlineStylesheet:f,maskInputOptions:H,dataURLOptions:x,maskAttributeFn:v,maskTextFn:b,maskInputFn:_,recordCanvas:E,inlineImages:M,sampling:k,slimDOMOptions:B,iframeManager:V,stylesheetManager:J,canvasManager:X,keepIframeSrcFn:D,processedNodeManager:K},mirror:ou}),Z=(t=!1)=>{ru({type:gc.Meta,data:{href:window.location.href,width:ec(),height:tc()}},t),J.reset(),Q.init(),Lc.forEach((t=>t.lock()));const e=function(t,e){const{mirror:n=new ma,blockClass:r="rr-block",blockSelector:i=null,unblockSelector:o=null,maskAllText:s=!1,maskTextClass:a="rr-mask",unmaskTextClass:c=null,maskTextSelector:u=null,unmaskTextSelector:l=null,inlineStylesheet:d=!0,inlineImages:p=!1,recordCanvas:h=!1,maskAllInputs:f=!1,maskAttributeFn:m,maskTextFn:g,maskInputFn:y,slimDOM:v=!1,dataURLOptions:_,preserveWhiteSpace:b,onSerialize:S,onIframeLoad:w,iframeLoadTimeout:k,onStylesheetLoad:x,stylesheetLoadTimeout:C,keepIframeSrcFn:E=(()=>!1)}=e||{};return za(t,{doc:t,mirror:n,blockClass:r,blockSelector:i,unblockSelector:o,maskAllText:s,maskTextClass:a,unmaskTextClass:c,maskTextSelector:u,unmaskTextSelector:l,skipChild:!1,inlineStylesheet:d,maskInputOptions:!0===f?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===f?{}:f,maskAttributeFn:m,maskTextFn:g,maskInputFn:y,slimDOMOptions:!0===v||"all"===v?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===v,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===v?{}:v,dataURLOptions:_,inlineImages:p,recordCanvas:h,preserveWhiteSpace:b,onSerialize:S,onIframeLoad:w,iframeLoadTimeout:k,onStylesheetLoad:x,stylesheetLoadTimeout:C,keepIframeSrcFn:E,newlyAddedElement:!1})}(document,{mirror:ou,blockClass:i,blockSelector:o,unblockSelector:s,maskAllText:u,maskTextClass:l,unmaskTextClass:d,maskTextSelector:p,unmaskTextSelector:h,inlineStylesheet:f,maskAllInputs:H,maskAttributeFn:v,maskInputFn:_,maskTextFn:b,slimDOM:B,dataURLOptions:x,recordCanvas:E,inlineImages:M,onSerialize:t=>{ac(t,ou)&&V.addIframe(t),cc(t,ou)&&J.trackLinkElement(t),uc(t)&&Q.addShadowRoot(t.shadowRoot,document)},onIframeLoad:(t,e)=>{V.attachIframe(t,e),Q.observeAttachShadow(t)},onStylesheetLoad:(t,e)=>{J.attachLinkElement(t,e)},keepIframeSrcFn:D});if(!e)return console.warn("Failed to snapshot the document");ru({type:gc.FullSnapshot,data:{node:e,initialOffset:Za(window)}}),Lc.forEach((t=>t.unlock())),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&J.adoptStyleSheets(document.adoptedStyleSheets,ou.getId(document))};iu=Z;try{const t=[],e=t=>Dc(Wc)({onMutation:P,mutationCb:W,mousemoveCb:(t,e)=>ru({type:gc.IncrementalSnapshot,data:{source:e,positions:t}}),mouseInteractionCb:t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.MouseInteraction,...t}}),scrollCb:Y,viewportResizeCb:t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.ViewportResize,...t}}),inputCb:t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.Input,...t}}),mediaInteractionCb:t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.MediaInteraction,...t}}),styleSheetRuleCb:t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.StyleSheetRule,...t}}),styleDeclarationCb:t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.StyleDeclaration,...t}}),canvasMutationCb:G,fontCb:t=>ru({type:gc.IncrementalSnapshot,data:{source:yc.Font,...t}}),selectionCb:t=>{ru({type:gc.IncrementalSnapshot,data:{source:yc.Selection,...t}})},customElementCb:t=>{ru({type:gc.IncrementalSnapshot,data:{source:yc.CustomElement,...t}})},blockClass:i,ignoreClass:a,ignoreSelector:c,maskAllText:u,maskTextClass:l,unmaskTextClass:d,maskTextSelector:p,unmaskTextSelector:h,maskInputOptions:H,inlineStylesheet:f,sampling:k,recordCanvas:E,inlineImages:M,userTriggeredOnInput:R,collectFonts:O,doc:t,maskAttributeFn:v,maskInputFn:_,maskTextFn:b,keepIframeSrcFn:D,blockSelector:o,unblockSelector:s,slimDOMOptions:B,dataURLOptions:x,mirror:ou,iframeManager:V,stylesheetManager:J,shadowDomManager:Q,processedNodeManager:K,canvasManager:X,ignoreCSSAttributes:N,plugins:ss([A,"optionalAccess",t=>t.filter,"call",t=>t((t=>t.observer)),"optionalAccess",t=>t.map,"call",t=>t((t=>({observer:t.observer,options:t.options,callback:e=>ru({type:gc.Plugin,data:{plugin:t.name,payload:e}})})))])||[]},{});V.addLoadListener((n=>{try{t.push(e(n.contentDocument))}catch(t){console.warn(t)}}));const n=()=>{Z(),t.push(e(document))};return"interactive"===document.readyState||"complete"===document.readyState?n():(t.push(Ya("DOMContentLoaded",(()=>{ru({type:gc.DomContentLoaded,data:{}}),"DOMContentLoaded"===I&&n()}))),t.push(Ya("load",(()=>{ru({type:gc.Load,data:{}}),"load"===I&&n()}),window))),()=>{t.forEach((t=>t())),K.destroy(),iu=void 0,Ac()}}catch(t){console.warn(t)}}su.mirror=ou,su.takeFullSnapshot=function(t){if(!iu)throw new Error("please take full snapshot after start recording");iu(t)};const au=3;function cu(t){return t>9999999999?t:1e3*t}function uu(t){return t>9999999999?t/1e3:t}function lu(t,e){"sentry.transaction"!==e.category&&(["ui.click","ui.input"].includes(e.category)?t.triggerUserActivity():t.checkAndHandleExpiredSession(),t.addUpdate((()=>(t.throttledAddEvent({type:gc.Custom,timestamp:1e3*(e.timestamp||0),data:{tag:"breadcrumb",payload:ce(e,10,1e3)}}),"console"===e.category))))}const du="button,a";function pu(t){return t.closest(du)||t}function hu(t){const e=fu(t);return e&&e instanceof Element?pu(e):e}function fu(t){return function(t){return"object"==typeof t&&!!t&&"target"in t}(t)?t.target:t}let mu;class gu{constructor(t,e,n=lu){this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=e.timeout/1e3,this._threshold=e.threshold/1e3,this._scollTimeout=e.scrollTimeout/1e3,this._replay=t,this._ignoreSelector=e.ignoreSelector,this._addBreadcrumbEvent=n}addListeners(){const t=(e=()=>{this._lastMutation=vu()},mu||(mu=[],$(na,"open",(function(t){return function(...e){if(mu)try{mu.forEach((t=>t()))}catch(t){}return t.apply(na,e)}}))),mu.push(e),()=>{const t=mu?mu.indexOf(e):-1;t>-1&&mu.splice(t,1)});var e;this._teardown=()=>{t(),this._clicks=[],this._lastMutation=0,this._lastScroll=0}}removeListeners(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}handleClick(t,e){if(function(t,e){return!yu.includes(t.tagName)||("INPUT"===t.tagName&&!["submit","button"].includes(t.getAttribute("type")||"")||(!("A"!==t.tagName||!(t.hasAttribute("download")||t.hasAttribute("target")&&"_self"!==t.getAttribute("target")))||!(!e||!t.matches(e))))}(e,this._ignoreSelector)||!function(t){return!(!t.data||"number"!=typeof t.data.nodeId||!t.timestamp)}(t))return;const n={timestamp:uu(t.timestamp),clickBreadcrumb:t,clickCount:0,node:e};this._clicks.some((t=>t.node===n.node&&Math.abs(t.timestamp-n.timestamp)<1))||(this._clicks.push(n),1===this._clicks.length&&this._scheduleCheckClicks())}registerMutation(t=Date.now()){this._lastMutation=uu(t)}registerScroll(t=Date.now()){this._lastScroll=uu(t)}registerClick(t){const e=pu(t);this._handleMultiClick(e)}_handleMultiClick(t){this._getClicks(t).forEach((t=>{t.clickCount++}))}_getClicks(t){return this._clicks.filter((e=>e.node===t))}_checkClicks(){const t=[],e=vu();this._clicks.forEach((n=>{!n.mutationAfter&&this._lastMutation&&(n.mutationAfter=n.timestamp<=this._lastMutation?this._lastMutation-n.timestamp:void 0),!n.scrollAfter&&this._lastScroll&&(n.scrollAfter=n.timestamp<=this._lastScroll?this._lastScroll-n.timestamp:void 0),n.timestamp+this._timeout<=e&&t.push(n)}));for(const e of t){const t=this._clicks.indexOf(e);t>-1&&(this._generateBreadcrumbs(e),this._clicks.splice(t,1))}this._clicks.length&&this._scheduleCheckClicks()}_generateBreadcrumbs(t){const e=this._replay,n=t.scrollAfter&&t.scrollAfter<=this._scollTimeout,r=t.mutationAfter&&t.mutationAfter<=this._threshold,i=!n&&!r,{clickCount:o,clickBreadcrumb:s}=t;if(i){const n=1e3*Math.min(t.mutationAfter||this._timeout,this._timeout),r=n<1e3*this._timeout?"mutation":"timeout",i={type:"default",message:s.message,timestamp:s.timestamp,category:"ui.slowClickDetected",data:{...s.data,url:na.location.href,route:e.getCurrentRoute(),timeAfterClickMs:n,endReason:r,clickCount:o||1}};this._addBreadcrumbEvent(e,i)}else if(o>1){const t={type:"default",message:s.message,timestamp:s.timestamp,category:"ui.multiClick",data:{...s.data,url:na.location.href,route:e.getCurrentRoute(),clickCount:o,metric:!0}};this._addBreadcrumbEvent(e,t)}}_scheduleCheckClicks(){this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=setTimeout((()=>this._checkClicks()),1e3)}}const yu=["A","BUTTON","INPUT"];function vu(){return Date.now()/1e3}function _u(t){return{timestamp:Date.now()/1e3,type:"default",...t}}var bu;!function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"}(bu||(bu={}));const Su=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]);function wu(t){const e={};for(const n in t)if(Su.has(n)){let r=n;"data-testid"!==n&&"data-test-id"!==n||(r="testId"),e[r]=t[n]}return e}function ku(t,e){const n=su.mirror.getId(t),r=n&&su.mirror.getNode(n),i=r&&su.mirror.getMeta(r),o=i&&function(t){return t.type===bu.Element}(i)?i:null;return{message:e,data:o?{nodeId:n,node:{id:n,tagName:o.tagName,textContent:Array.from(o.childNodes).map((t=>t.type===bu.Text&&t.textContent)).filter(Boolean).map((t=>t.trim())).join(""),attributes:wu(o.attributes)}}:{}}}const xu={resource:function(t){const{entryType:e,initiatorType:n,name:r,responseEnd:i,startTime:o,decodedBodySize:s,encodedBodySize:a,responseStatus:c,transferSize:u}=t;return["fetch","xmlhttprequest"].includes(n)?null:{type:`${e}.${n}`,start:Eu(o),end:Eu(i),name:r,data:{size:u,statusCode:c,decodedBodySize:s,encodedBodySize:a}}},paint:function(t){const{duration:e,entryType:n,name:r,startTime:i}=t,o=Eu(i);return{type:n,name:r,start:o,end:o+e,data:void 0}},navigation:function(t){const{entryType:e,name:n,decodedBodySize:r,duration:i,domComplete:o,encodedBodySize:s,domContentLoadedEventStart:a,domContentLoadedEventEnd:c,domInteractive:u,loadEventStart:l,loadEventEnd:d,redirectCount:p,startTime:h,transferSize:f,type:m}=t;return 0===i?null:{type:`${e}.${m}`,start:Eu(h),end:Eu(o),name:n,data:{size:f,decodedBodySize:r,encodedBodySize:s,duration:i,domInteractive:u,domContentLoadedEventStart:a,domContentLoadedEventEnd:c,loadEventStart:l,loadEventEnd:d,domComplete:o,redirectCount:p}}}};function Cu(t){return xu[t.entryType]?xu[t.entryType](t):null}function Eu(t){return((at||na.performance.timeOrigin)+t)/1e3}const Tu="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function Iu(t,e){Tu&&(N.info(t),e&&Ou(t))}function Ru(t,e){Tu&&(N.info(t),e&&setTimeout((()=>{Ou(t)}),0))}function Ou(t){ve({category:"console",data:{logger:"replay"},level:"info",message:t},{level:"info"})}class Mu extends Error{constructor(){super("Event buffer exceeded maximum size of 20000000.")}}class Au{constructor(){this.events=[],this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(t){const e=JSON.stringify(t).length;if(this._totalSize+=e,this._totalSize>ca)throw new Mu;this.events.push(t)}finish(){return new Promise((t=>{const e=this.events;this.clear(),t(JSON.stringify(e))}))}clear(){this.events=[],this._totalSize=0,this.hasCheckout=!1}getEarliestTimestamp(){const t=this.events.map((t=>t.timestamp)).sort()[0];return t?cu(t):null}}class Du{constructor(t){this._worker=t,this._id=0}ensureReady(){return this._ensureReadyPromise||(this._ensureReadyPromise=new Promise(((t,e)=>{this._worker.addEventListener("message",(({data:n})=>{n.success?t():e()}),{once:!0}),this._worker.addEventListener("error",(t=>{e(t)}),{once:!0})}))),this._ensureReadyPromise}destroy(){Iu("[Replay] Destroying compression worker"),this._worker.terminate()}postMessage(t,e){const n=this._getAndIncrementId();return new Promise(((r,i)=>{const o=({data:e})=>{const s=e;if(s.method===t&&s.id===n){if(this._worker.removeEventListener("message",o),!s.success)return Tu&&N.error("[Replay]",s.response),void i(new Error("Error in compression worker"));r(s.response)}};this._worker.addEventListener("message",o),this._worker.postMessage({id:n,method:t,arg:e})}))}_getAndIncrementId(){return this._id++}}class Nu{constructor(t){this._worker=new Du(t),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1}get hasEvents(){return!!this._earliestTimestamp}get type(){return"worker"}ensureReady(){return this._worker.ensureReady()}destroy(){this._worker.destroy()}addEvent(t){const e=cu(t.timestamp);(!this._earliestTimestamp||e<this._earliestTimestamp)&&(this._earliestTimestamp=e);const n=JSON.stringify(t);return this._totalSize+=n.length,this._totalSize>ca?Promise.reject(new Mu):this._sendEventToWorker(n)}finish(){return this._finishRequest()}clear(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear").then(null,(t=>{Tu&&N.warn('[Replay] Sending "clear" message to worker failed',t)}))}getEarliestTimestamp(){return this._earliestTimestamp}_sendEventToWorker(t){return this._worker.postMessage("addEvent",t)}async _finishRequest(){const t=await this._worker.postMessage("finish");return this._earliestTimestamp=null,this._totalSize=0,t}}class Lu{constructor(t){this._fallback=new Au,this._compression=new Nu(t),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}get type(){return this._used.type}get hasEvents(){return this._used.hasEvents}get hasCheckout(){return this._used.hasCheckout}set hasCheckout(t){this._used.hasCheckout=t}destroy(){this._fallback.destroy(),this._compression.destroy()}clear(){return this._used.clear()}getEarliestTimestamp(){return this._used.getEarliestTimestamp()}addEvent(t){return this._used.addEvent(t)}async finish(){return await this.ensureWorkerIsLoaded(),this._used.finish()}ensureWorkerIsLoaded(){return this._ensureWorkerIsLoadedPromise}async _ensureWorkerIsLoaded(){try{await this._compression.ensureReady()}catch(t){return void Iu("[Replay] Failed to load the compression worker, falling back to simple buffer")}await this._switchToCompressionWorker()}async _switchToCompressionWorker(){const{events:t,hasCheckout:e}=this._fallback,n=[];for(const e of t)n.push(this._compression.addEvent(e));this._compression.hasCheckout=e,this._used=this._compression;try{await Promise.all(n)}catch(t){Tu&&N.warn("[Replay] Failed to add events when switching buffers.",t)}}}function Pu(){try{return"sessionStorage"in na&&!!na.sessionStorage}catch(t){return!1}}function Fu(t){return void 0!==t&&Math.random()<t}function ju(t){const e=Date.now();return{id:t.id||K(),started:t.started||e,lastActivity:t.lastActivity||e,segmentId:t.segmentId||0,sampled:t.sampled,previousSessionId:t.previousSessionId}}function $u(t){if(Pu())try{na.sessionStorage.setItem(ra,JSON.stringify(t))}catch(t){}}function Hu({sessionSampleRate:t,allowBuffering:e,stickySession:n=!1},{previousSessionId:r}={}){const i=function(t,e){return Fu(t)?"session":!!e&&"buffer"}(t,e),o=ju({sampled:i,previousSessionId:r});return n&&$u(o),o}function Bu(t,e,n=+new Date){return null===t||void 0===e||e<0||0!==e&&t+e<=n}function Uu(t,{maxReplayDuration:e,sessionIdleExpire:n,targetTime:r=Date.now()}){return Bu(t.started,e,r)||Bu(t.lastActivity,n,r)}function qu(t,{sessionIdleExpire:e,maxReplayDuration:n}){return!!Uu(t,{sessionIdleExpire:e,maxReplayDuration:n})&&("buffer"!==t.sampled||0!==t.segmentId)}function zu({traceInternals:t,sessionIdleExpire:e,maxReplayDuration:n,previousSessionId:r},i){const o=i.stickySession&&function(t){if(!Pu())return null;try{const e=na.sessionStorage.getItem(ra);if(!e)return null;const n=JSON.parse(e);return Ru("[Replay] Loading existing session",t),ju(n)}catch(t){return null}}(t);return o?qu(o,{sessionIdleExpire:e,maxReplayDuration:n})?(Ru("[Replay] Session in sessionStorage is expired, creating new one..."),Hu(i,{previousSessionId:o.id})):o:(Ru("[Replay] Creating new session",t),Hu(i,{previousSessionId:r}))}function Wu(t,e,n){return!!Gu(t,e)&&(Yu(t,e,n),!0)}async function Yu(t,e,n){if(!t.eventBuffer)return null;try{n&&"buffer"===t.recordingMode&&t.eventBuffer.clear(),n&&(t.eventBuffer.hasCheckout=!0);const r=function(t,e){try{if("function"==typeof e&&function(t){return t.type===gc.Custom}(t))return e(t)}catch(t){return Tu&&N.error("[Replay] An error occured in the `beforeAddRecordingEvent` callback, skipping the event...",t),null}return t}(e,t.getOptions().beforeAddRecordingEvent);if(!r)return;return await t.eventBuffer.addEvent(r)}catch(e){const n=e&&e instanceof Mu?"addEventSizeExceeded":"addEvent";Tu&&N.error(e),await t.stop({reason:n});const r=Ae();r&&r.recordDroppedEvent("internal_sdk_error","replay")}}function Gu(t,e){if(!t.eventBuffer||t.isPaused()||!t.isEnabled())return!1;const n=cu(e.timestamp);return!(n+t.timeouts.sessionIdlePause<Date.now()||n>t.getContext().initialTimestamp+t.getOptions().maxReplayDuration&&(Iu(`[Replay] Skipping event with timestamp ${n} because it is after maxReplayDuration`,t.getOptions()._experiments.traceInternals),1))}function Ju(t){return!t.type}function Vu(t){return"transaction"===t.type}function Ku(t){return"feedback"===t.type}function Xu(t){const e=function(){const t=Ae();if(!t)return!1;const e=t.getTransport();return e&&e.send.__sentry__baseTransport__||!1}();return(n,r)=>{if(!t.isEnabled()||!Ju(n)&&!Vu(n))return;const i=r&&r.statusCode;e&&(!i||i<200||i>=300)||(Vu(n)?function(t,e){const n=t.getContext();e.contexts&&e.contexts.trace&&e.contexts.trace.trace_id&&n.traceIds.size<100&&n.traceIds.add(e.contexts.trace.trace_id)}(t,n):function(t,e){const n=t.getContext();if(e.event_id&&n.errorIds.size<100&&n.errorIds.add(e.event_id),"buffer"!==t.recordingMode||!e.tags||!e.tags.replayId)return;const{beforeErrorSampling:r}=t.getOptions();("function"!=typeof r||r(e))&&setTimeout((()=>{t.sendBufferedReplayOrFlush()}))}(t,n))}}function Qu(t,e){return e.map((({type:e,start:n,end:r,name:i,data:o})=>{const s=t.throttledAddEvent({type:gc.Custom,timestamp:n,data:{tag:"performanceSpan",payload:{op:e,description:i,startTimestamp:n,endTimestamp:r,data:o}}});return"string"==typeof s?Promise.resolve(null):s}))}function Zu(t,e){t.isEnabled()&&null!==e&&(function(t,e){return(!Tu||!t.getOptions()._experiments.traceInternals)&&as(e,Ae())}(t,e.name)||t.addUpdate((()=>(Qu(t,[e]),!0))))}function tl(t,e){if(t)try{if("string"==typeof t)return e.encode(t).length;if(t instanceof URLSearchParams)return e.encode(t.toString()).length;if(t instanceof FormData){const n=cl(t);return e.encode(n).length}if(t instanceof Blob)return t.size;if(t instanceof ArrayBuffer)return t.byteLength}catch(t){}}function el(t){if(!t)return;const e=parseInt(t,10);return isNaN(e)?void 0:e}function nl(t){try{if("string"==typeof t)return[t];if(t instanceof URLSearchParams)return[t.toString()];if(t instanceof FormData)return[cl(t)];if(!t)return[void 0]}catch(e){return Tu&&N.warn("[Replay] Failed to serialize body",t),[void 0,"BODY_PARSE_ERROR"]}return Tu&&N.info("[Replay] Skipping network body because of body type",t),[void 0,"UNPARSEABLE_BODY_TYPE"]}function rl(t,e){if(!t)return{headers:{},size:void 0,_meta:{warnings:[e]}};const n={...t._meta},r=n.warnings||[];return n.warnings=[...r,e],t._meta=n,t}function il(t,e){if(!e)return null;const{startTimestamp:n,endTimestamp:r,url:i,method:o,statusCode:s,request:a,response:c}=e;return{type:t,start:n/1e3,end:r/1e3,name:i,data:Y({method:o,statusCode:s,request:a,response:c})}}function ol(t){return{headers:{},size:t,_meta:{warnings:["URL_SKIPPED"]}}}function sl(t,e,n){if(!e&&0===Object.keys(t).length)return;if(!e)return{headers:t};if(!n)return{headers:t,size:e};const r={headers:t,size:e},{body:i,warnings:o}=function(t){if(!t||"string"!=typeof t)return{body:t};const e=t.length>sa,n=function(t){const e=t[0],n=t[t.length-1];return"["===e&&"]"===n||"{"===e&&"}"===n}(t);if(e){const e=t.slice(0,sa);return n?{body:e,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:`${e}…`,warnings:["TEXT_TRUNCATED"]}}if(n)try{return{body:JSON.parse(t)}}catch(t){}return{body:t}}(n);return r.body=i,o&&o.length>0&&(r._meta={warnings:o}),r}function al(t,e){return Object.keys(t).reduce(((n,r)=>{const i=r.toLowerCase();return e.includes(i)&&t[r]&&(n[i]=t[r]),n}),{})}function cl(t){return new URLSearchParams(t).toString()}function ul(t,e){const n=function(t,e=na.document.baseURI){if(t.startsWith("http://")||t.startsWith("https://")||t.startsWith(na.location.origin))return t;const n=new URL(t,e);if(n.origin!==new URL(e).origin)return t;const r=n.href;return!t.endsWith("/")&&r.endsWith("/")?r.slice(0,-1):r}(t);return j(n,e)}function ll(t=[]){if(2===t.length&&"object"==typeof t[1])return t[1].body}function dl(t,e){const n={};return e.forEach((e=>{t.get(e)&&(n[e]=t.get(e))})),n}function pl(t,e){if(!t)return{};const n=t.headers;return n?n instanceof Headers?dl(n,e):Array.isArray(n)?{}:al(n,e):{}}function hl(t){const e=Ae();try{const n=new TextEncoder,{networkDetailAllowUrls:r,networkDetailDenyUrls:i,networkCaptureBodies:o,networkRequestHeaders:s,networkResponseHeaders:a}=t.getOptions(),c={replay:t,textEncoder:n,networkDetailAllowUrls:r,networkDetailDenyUrls:i,networkCaptureBodies:o,networkRequestHeaders:s,networkResponseHeaders:a};e&&e.on?e.on("beforeAddBreadcrumb",((t,e)=>function(t,e,n){if(e.data)try{(function(t){return"xhr"===t.category})(e)&&function(t){return t&&t.xhr}(n)&&(function(t,e,n){const{xhr:r,input:i}=e;if(!r)return;const o=tl(i,n.textEncoder),s=r.getResponseHeader("content-length")?el(r.getResponseHeader("content-length")):function(t,e,n){try{return tl("json"===e&&t&&"object"==typeof t?JSON.stringify(t):t,n)}catch(t){return}}(r.response,r.responseType,n.textEncoder);void 0!==o&&(t.data.request_body_size=o),void 0!==s&&(t.data.response_body_size=s)}(e,n,t),async function(t,e,n){try{const r=function(t,e,n){const r=Date.now(),{startTimestamp:i=r,endTimestamp:o=r,input:s,xhr:a}=e,{url:c,method:u,status_code:l=0,request_body_size:d,response_body_size:p}=t.data;if(!c)return null;if(!a||!ul(c,n.networkDetailAllowUrls)||ul(c,n.networkDetailDenyUrls))return{startTimestamp:i,endTimestamp:o,url:c,method:u,statusCode:l,request:ol(d),response:ol(p)};const h=a[po],f=h?al(h.request_headers,n.networkRequestHeaders):{},m=al(function(t){const e=t.getAllResponseHeaders();return e?e.split("\r\n").reduce(((t,e)=>{const[n,r]=e.split(": ");return t[n.toLowerCase()]=r,t}),{}):{}}(a),n.networkResponseHeaders),[g,y]=n.networkCaptureBodies?nl(s):[void 0],[v,_]=n.networkCaptureBodies?function(t){const e=[];try{return[t.responseText]}catch(t){e.push(t)}try{return function(t,e){try{if("string"==typeof t)return[t];if(t instanceof Document)return[t.body.outerHTML];if("json"===e&&t&&"object"==typeof t)return[JSON.stringify(t)];if(!t)return[void 0]}catch(e){return Tu&&N.warn("[Replay] Failed to serialize body",t),[void 0,"BODY_PARSE_ERROR"]}return Tu&&N.info("[Replay] Skipping network body because of body type",t),[void 0,"UNPARSEABLE_BODY_TYPE"]}(t.response,t.responseType)}catch(t){e.push(t)}return Tu&&N.warn("[Replay] Failed to get xhr response body",...e),[void 0]}(a):[void 0],b=sl(f,d,g),S=sl(m,p,v);return{startTimestamp:i,endTimestamp:o,url:c,method:u,statusCode:l,request:y?rl(b,y):b,response:_?rl(S,_):S}}(t,e,n),i=il("resource.xhr",r);Zu(n.replay,i)}catch(t){Tu&&N.error("[Replay] Failed to capture xhr breadcrumb",t)}}(e,n,t)),function(t){return"fetch"===t.category}(e)&&function(t){return t&&t.response}(n)&&(function(t,e,n){const{input:r,response:i}=e,o=tl(r?ll(r):void 0,n.textEncoder),s=i?el(i.headers.get("content-length")):void 0;void 0!==o&&(t.data.request_body_size=o),void 0!==s&&(t.data.response_body_size=s)}(e,n,t),async function(t,e,n){try{const r=await async function(t,e,n){const r=Date.now(),{startTimestamp:i=r,endTimestamp:o=r}=e,{url:s,method:a,status_code:c=0,request_body_size:u,response_body_size:l}=t.data,d=ul(s,n.networkDetailAllowUrls)&&!ul(s,n.networkDetailDenyUrls),p=d?function({networkCaptureBodies:t,networkRequestHeaders:e},n,r){const i=n?(s=e,1===(o=n).length&&"string"!=typeof o[0]?pl(o[0],s):2===o.length?pl(o[1],s):{}):{};var o,s;if(!t)return sl(i,r,void 0);const a=ll(n),[c,u]=nl(a),l=sl(i,r,c);return u?rl(l,u):l}(n,e.input,u):ol(u),h=await async function(t,{networkCaptureBodies:e,textEncoder:n,networkResponseHeaders:r},i,o){if(!t&&void 0!==o)return ol(o);const s=i?dl(i.headers,r):{};if(!i||!e&&void 0!==o)return sl(s,o,void 0);const[a,c]=await async function(t){const e=function(t){try{return t.clone()}catch(t){Tu&&N.warn("[Replay] Failed to clone response body",t)}}(t);if(!e)return[void 0,"BODY_PARSE_ERROR"];try{const t=await function(t){return new Promise(((e,n)=>{const r=setTimeout((()=>n(new Error("Timeout while trying to read response body"))),500);(async function(t){return await t.text()})(t).then((t=>e(t)),(t=>n(t))).finally((()=>clearTimeout(r)))}))}(e);return[t]}catch(t){return Tu&&N.warn("[Replay] Failed to get text body from response",t),[void 0,"BODY_PARSE_ERROR"]}}(i),u=function(t,{networkCaptureBodies:e,textEncoder:n,responseBodySize:r,captureDetails:i,headers:o}){try{const s=t&&t.length&&void 0===r?tl(t,n):r;return i?sl(o,s,e?t:void 0):ol(s)}catch(t){return Tu&&N.warn("[Replay] Failed to serialize response body",t),sl(o,r,void 0)}}(a,{networkCaptureBodies:e,textEncoder:n,responseBodySize:o,captureDetails:t,headers:s});return c?rl(u,c):u}(d,n,e.response,l);return{startTimestamp:i,endTimestamp:o,url:s,method:a,statusCode:c,request:p,response:h}}(t,e,n),i=il("resource.fetch",r);Zu(n.replay,i)}catch(t){Tu&&N.error("[Replay] Failed to capture fetch breadcrumb",t)}}(e,n,t))}catch(t){Tu&&N.warn("Error when enriching network breadcrumb")}}(c,t,e))):(mo(function(t){return e=>{if(!t.isEnabled())return;const n=function(t){const{startTimestamp:e,endTimestamp:n,fetchData:r,response:i}=t;if(!n)return null;const{method:o,url:s}=r;return{type:"resource.fetch",start:e/1e3,end:n/1e3,name:s,data:{method:o,statusCode:i?i.status:void 0}}}(e);Zu(t,n)}}(t)),ho(function(t){return e=>{if(!t.isEnabled())return;const n=function(t){const{startTimestamp:e,endTimestamp:n,xhr:r}=t,i=r[po];if(!e||!n||!i)return null;const{method:o,url:s,status_code:a}=i;return void 0===s?null:{type:"resource.xhr",name:s,start:e/1e3,end:n/1e3,data:{method:o,statusCode:a}}}(e);Zu(t,n)}}(t)))}catch(t){}}let fl=null;function ml(t){return!(!t||!t.on)}function gl(t){const{jsHeapSizeLimit:e,totalJSHeapSize:n,usedJSHeapSize:r}=t,i=Date.now()/1e3;return{type:"memory",name:"memory",start:i,end:i,data:{memory:{jsHeapSizeLimit:e,totalJSHeapSize:n,usedJSHeapSize:r}}}}function yl(t){let e=!1;return(n,r)=>{if(!t.checkAndHandleExpiredSession())return void(Tu&&N.warn("[Replay] Received replay event after session expired."));const i=r||!e;e=!0,t.clickDetector&&function(t,e){try{if(!function(t){return t.type===au}(e))return;const{source:n}=e.data;if(n===yc.Mutation&&t.registerMutation(e.timestamp),n===yc.Scroll&&t.registerScroll(e.timestamp),function(t){return t.data.source===yc.MouseInteraction}(e)){const{type:n,id:r}=e.data,i=su.mirror.getNode(r);i instanceof HTMLElement&&n===vc.Click&&t.registerClick(i)}}catch(t){}}(t.clickDetector,n),t.addUpdate((()=>{if("buffer"===t.recordingMode&&i&&t.setInitialState(),!Wu(t,n,i))return!0;if(!i)return!1;if(function(t,e){e&&t.session&&0===t.session.segmentId&&Wu(t,function(t){const e=t.getOptions();return{type:gc.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:t.isRecordingCanvas(),sessionSampleRate:e.sessionSampleRate,errorSampleRate:e.errorSampleRate,useCompressionOption:e.useCompression,blockAllMedia:e.blockAllMedia,maskAllText:e.maskAllText,maskAllInputs:e.maskAllInputs,useCompression:!!t.eventBuffer&&"worker"===t.eventBuffer.type,networkDetailHasUrls:e.networkDetailAllowUrls.length>0,networkCaptureBodies:e.networkCaptureBodies,networkRequestHasHeaders:e.networkRequestHeaders.length>0,networkResponseHasHeaders:e.networkResponseHeaders.length>0}}}}(t),!1)}(t,i),t.session&&t.session.previousSessionId)return!0;if("buffer"===t.recordingMode&&t.session&&t.eventBuffer){const e=t.eventBuffer.getEarliestTimestamp();e&&(Iu(`[Replay] Updating session start time to earliest event in buffer to ${new Date(e)}`,t.getOptions()._experiments.traceInternals),t.session.started=e,t.getOptions().stickySession&&$u(t.session))}return"session"===t.recordingMode&&t.flush(),!0}))}}class vl extends Error{constructor(t){super(`Transport returned status code ${t}`)}}class _l extends Error{constructor(t){super("Rate limit hit"),this.rateLimits=t}}async function bl(t,e={count:0,interval:5e3}){const{recordingData:n,options:r}=t;if(n.length)try{return await async function({recordingData:t,replayId:e,segmentId:n,eventContext:r,timestamp:i,session:o}){const s=function({recordingData:t,headers:e}){let n;const r=`${JSON.stringify(e)}\n`;if("string"==typeof t)n=`${r}${t}`;else{const e=(new TextEncoder).encode(r);n=new Uint8Array(e.length+t.length),n.set(e),n.set(t,e.length)}return n}({recordingData:t,headers:{segment_id:n}}),{urls:a,errorIds:c,traceIds:u,initialTimestamp:l}=r,d=Ae(),p=Ne(),h=d&&d.getTransport(),f=d&&d.getDsn();if(!(d&&h&&f&&o.sampled))return;const m={type:ia,replay_start_timestamp:l/1e3,timestamp:i/1e3,error_ids:c,trace_ids:u,urls:a,replay_id:e,segment_id:n,replay_type:o.sampled},g=await async function({client:t,scope:e,replayId:n,event:r}){const i={event_id:n,integrations:"object"!=typeof t._integrations||null===t._integrations||Array.isArray(t._integrations)?void 0:Object.keys(t._integrations)};t.emit&&t.emit("preprocessEvent",r,i);const o=await de(t.getOptions(),r,i,e,t,Qt());if(!o)return null;o.platform=o.platform||"javascript";const s=t.getSdkMetadata&&t.getSdkMetadata(),{name:a,version:c}=s&&s.sdk||{};return o.sdk={...o.sdk,name:a||"sentry.javascript.unknown",version:c||"0.0.0"},o}({scope:p,client:d,replayId:e,event:m});if(!g)return d.recordDroppedEvent("event_processor","replay",m),void Iu("An event processor returned `null`, will not send event.");delete g.sdkProcessingMetadata;const y=function(t,e,n,r){return gn(Cn(t,xn(t),r,n),[[{type:"replay_event"},t],[{type:"replay_recording",length:"string"==typeof e?(new TextEncoder).encode(e).length:e.length},e]])}(g,s,f,d.getOptions().tunnel);let v;try{v=await h.send(y)}catch(t){const e=new Error(oa);try{e.cause=t}catch(t){}throw e}if(!v)return v;if("number"==typeof v.statusCode&&(v.statusCode<200||v.statusCode>=300))throw new vl(v.statusCode);const _=Wr({},v);if(zr(_,"replay"))throw new _l(_);return v}(t),!0}catch(n){if(n instanceof vl||n instanceof _l)throw n;if(_e("Replays",{_retryCount:e.count}),Tu&&r._experiments&&r._experiments.captureExceptions&&fe(n),e.count>=3){const t=new Error(`${oa} - max retries exceeded`);try{t.cause=n}catch(t){}throw t}return e.interval*=++e.count,new Promise(((n,r)=>{setTimeout((async()=>{try{await bl(t,e),n(!0)}catch(t){r(t)}}),e.interval)}))}}const Sl="__THROTTLED";class wl{constructor({options:t,recordingOptions:e}){wl.prototype.__init.call(this),wl.prototype.__init2.call(this),wl.prototype.__init3.call(this),wl.prototype.__init4.call(this),wl.prototype.__init5.call(this),wl.prototype.__init6.call(this),this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=e,this._options=t,this._debouncedFlush=function(t,e,n){let r,i,o;const s=n&&n.maxWait?Math.max(n.maxWait,e):0;function a(){return c(),r=t(),r}function c(){void 0!==i&&clearTimeout(i),void 0!==o&&clearTimeout(o),i=o=void 0}function u(){return i&&clearTimeout(i),i=setTimeout(a,e),s&&void 0===o&&(o=setTimeout(a,s)),r}return u.cancel=c,u.flush=function(){return void 0!==i||void 0!==o?a():r},u}((()=>this._flush()),this._options.flushMinDelay,{maxWait:this._options.flushMaxDelay}),this._throttledAddEvent=function(t,e,n){const r=new Map;let i=!1;return(...o)=>{const s=Math.floor(Date.now()/1e3);if((t=>{const e=t-n;r.forEach(((t,n)=>{n<e&&r.delete(n)}))})(s),[...r.values()].reduce(((t,e)=>t+e),0)>=e){const t=i;return i=!0,t?"__SKIPPED":Sl}i=!1;const a=r.get(s)||0;return r.set(s,a+1),t(...o)}}(((t,e)=>function(t,e,n){return Gu(t,e)?Yu(t,e,n):Promise.resolve(null)}(this,t,e)),300,5);const{slowClickTimeout:n,slowClickIgnoreSelectors:r}=this.getOptions(),i=n?{threshold:Math.min(3e3,n),timeout:n,scrollTimeout:300,ignoreSelector:r?r.join(","):""}:void 0;i&&(this.clickDetector=new gu(this,i))}getContext(){return this._context}isEnabled(){return this._isEnabled}isPaused(){return this._isPaused}isRecordingCanvas(){return Boolean(this._canvas)}getOptions(){return this._options}initializeSampling(t){const{errorSampleRate:e,sessionSampleRate:n}=this._options;e<=0&&n<=0||(this._initializeSessionForSampling(t),this.session?!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",Ru(`[Replay] Starting replay in ${this.recordingMode} mode`,this._options._experiments.traceInternals),this._initializeRecording()):this._handleException(new Error("Unable to initialize and create session")))}start(){if(this._isEnabled&&"session"===this.recordingMode)throw new Error("Replay recording is already in progress");if(this._isEnabled&&"buffer"===this.recordingMode)throw new Error("Replay buffering is in progress, call `flush()` to save the replay");Ru("[Replay] Starting replay in session mode",this._options._experiments.traceInternals),this._updateUserActivity();const t=zu({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=t,this._initializeRecording()}startBuffering(){if(this._isEnabled)throw new Error("Replay recording is already in progress");Ru("[Replay] Starting replay in buffer mode",this._options._experiments.traceInternals);const t=zu({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=t,this.recordingMode="buffer",this._initializeRecording()}startRecording(){try{const t=this._canvas;this._stopRecording=su({...this._recordingOptions,..."buffer"===this.recordingMode&&{checkoutEveryNms:6e4},emit:yl(this),onMutation:this._onMutationHandler,...t?{recordCanvas:t.recordCanvas,getCanvasManager:t.getCanvasManager,sampling:t.sampling,dataURLOptions:t.dataURLOptions}:{}})}catch(t){this._handleException(t)}}stopRecording(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(t){return this._handleException(t),!1}}async stop({forceFlush:t=!1,reason:e}={}){if(this._isEnabled){this._isEnabled=!1;try{Iu("[Replay] Stopping Replay"+(e?` triggered by ${e}`:""),this._options._experiments.traceInternals),this._removeListeners(),this.stopRecording(),this._debouncedFlush.cancel(),t&&await this._flush({force:!0}),this.eventBuffer&&this.eventBuffer.destroy(),this.eventBuffer=null,n=this,function(){if(Pu())try{na.sessionStorage.removeItem(ra)}catch(t){}}(),n.session=void 0}catch(t){this._handleException(t)}}var n}pause(){this._isPaused||(this._isPaused=!0,this.stopRecording(),Iu("[Replay] Pausing replay",this._options._experiments.traceInternals))}resume(){this._isPaused&&this._checkSession()&&(this._isPaused=!1,this.startRecording(),Iu("[Replay] Resuming replay",this._options._experiments.traceInternals))}async sendBufferedReplayOrFlush({continueRecording:t=!0}={}){if("session"===this.recordingMode)return this.flushImmediate();const e=Date.now();Iu("[Replay] Converting buffer to session",this._options._experiments.traceInternals),await this.flushImmediate();const n=this.stopRecording();t&&n&&"session"!==this.recordingMode&&(this.recordingMode="session",this.session&&(this._updateUserActivity(e),this._updateSessionActivity(e),this._maybeSaveSession()),this.startRecording())}addUpdate(t){const e=t();"buffer"!==this.recordingMode&&!0!==e&&this._debouncedFlush()}triggerUserActivity(){if(this._updateUserActivity(),this._stopRecording)this.checkAndHandleExpiredSession(),this._updateSessionActivity();else{if(!this._checkSession())return;this.resume()}}updateUserActivity(){this._updateUserActivity(),this._updateSessionActivity()}conditionalFlush(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}flush(){return this._debouncedFlush()}flushImmediate(){return this._debouncedFlush(),this._debouncedFlush.flush()}cancelFlush(){this._debouncedFlush.cancel()}getSessionId(){return this.session&&this.session.id}checkAndHandleExpiredSession(){if(!(this._lastActivity&&Bu(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled))return!!this._checkSession();this.pause()}setInitialState(){const t=`${na.location.pathname}${na.location.hash}${na.location.search}`,e=`${na.location.origin}${t}`;this.performanceEntries=[],this.replayPerformanceEntries=[],this._clearContext(),this._context.initialUrl=e,this._context.initialTimestamp=Date.now(),this._context.urls.push(e)}throttledAddEvent(t,e){const n=this._throttledAddEvent(t,e);if(n===Sl){const t=_u({category:"replay.throttled"});this.addUpdate((()=>!Wu(this,{type:5,timestamp:t.timestamp||0,data:{tag:"breadcrumb",payload:t,metric:!0}})))}return n}getCurrentRoute(){const t=this.lastTransaction||Ne().getTransaction(),e=(t&&Lt(t).data||{})[An];if(t&&e&&["route","custom"].includes(e))return Lt(t).description}_initializeRecording(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=function({useCompression:t,workerUrl:e}){if(t&&window.Worker){const t=function(t){try{const e=t||("undefined"!=typeof __SENTRY_EXCLUDE_REPLAY_WORKER__&&__SENTRY_EXCLUDE_REPLAY_WORKER__?"":function(){const t=new Blob(['var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),a=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(t,e){for(var i=new n(31),a=0;a<31;++a)i[a]=e+=1<<t[a-1];var s=new r(i[30]);for(a=1;a<30;++a)for(var o=i[a];o<i[a+1];++o)s[o]=o-i[a]<<5|a;return{b:i,r:s}},o=s(e,2),f=o.b,h=o.r;f[28]=258,h[258]=28;for(var l=s(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,a=0,s=new n(r);a<i;++a)t[a]&&++s[t[a]-1];var o,f=new n(r);for(a=1;a<r;++a)f[a]=f[a-1]+s[a-1]<<1;if(e){o=new n(1<<r);var h=15-r;for(a=0;a<i;++a)if(t[a])for(var l=a<<4|t[a],c=r-t[a],v=f[t[a]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>h]=l}else for(o=new n(i),a=0;a<i;++a)t[a]&&(o[a]=u[f[t[a]-1]++]>>15-t[a]);return o},g=new t(288);for(c=0;c<144;++c)g[c]=8;for(c=144;c<256;++c)g[c]=9;for(c=256;c<280;++c)g[c]=7;for(c=280;c<288;++c)g[c]=8;var w=new t(32);for(c=0;c<32;++c)w[c]=5;var p=d(g,9,0),y=d(w,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==r||r<0)&&(r=0),(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},A=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},_=function(r,e){for(var i=[],a=0;a<r.length;++a)r[a]&&i.push({s:a,f:r[a]});var s=i.length,o=i.slice();if(!s)return{t:F,l:0};if(1==s){var f=new t(i[0].s+1);return f[i[0].s]=1,{t:f,l:1}}i.sort((function(t,n){return t.f-n.f})),i.push({s:-1,f:25001});var h=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:h.f+l.f,l:h,r:l};c!=s-1;)h=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:h.f+l.f,l:h,r:l};var d=o[0].s;for(a=1;a<s;++a)o[a].s>d&&(d=o[a].s);var g=new n(d+1),w=x(i[c-1],g,0);if(w>e){a=0;var p=0,y=w-e,m=1<<y;for(o.sort((function(t,n){return g[n.s]-g[t.s]||t.f-n.f}));a<s;++a){var b=o[a].s;if(!(g[b]>e))break;p+=m-(1<<w-g[b]),g[b]=e}for(p>>=y;p>0;){var M=o[a].s;g[M]<e?p-=1<<e-g[M]++-1:++a}for(;a>=0&&p;--a){var E=o[a].s;g[E]==e&&(--g[E],++p)}w=e}return{t:new t(g),l:w}},x=function(t,n,r){return-1==t.s?Math.max(x(t.l,n,r+1),x(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,a=t[0],s=1,o=function(t){e[i++]=t},f=1;f<=r;++f)if(t[f]==a&&f!=r)++s;else{if(!a&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(a),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(a);s=1,a=t[f]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var a=0;a<e;++a)t[i+a+4]=r[a];return 8*(i+4+e)},C=function(t,r,s,o,f,h,l,u,c,v,m){z(r,m++,s),++f[256];for(var b=_(f,15),M=b.t,E=b.l,x=_(h,15),C=x.t,U=x.l,F=D(M),I=F.c,S=F.n,L=D(C),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=_(q,7),H=G.t,J=G.l,K=19;K>4&&!H[a[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(f,g)+T(h,w)+l,X=T(f,M)+T(h,C)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(C,U,0),R=C;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[a[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=p,P=g,Q=y,R=w;for(B=0;B<u;++B){var rt=o[B];if(rt>255){A(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;A(r,m,Q[et]),m+=R[et],et>3&&(A(r,m,rt>>5&8191),m+=i[et])}else A(r,m,N[rt]),m+=P[rt]}return A(r,m,N[256]),m+P[256]},U=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,a=0|r.length,s=0;s!=a;){for(var o=Math.min(s+2655,a);s<o;++s)i+=e+=r[s];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},L=function(a,s,o,f,u){if(!u&&(u={l:1},s.dictionary)){var c=s.dictionary.subarray(-32768),v=new t(c.length+a.length);v.set(c),v.set(a,c.length),a=v,u.w=c.length}return function(a,s,o,f,u,c){var v=c.z||a.length,d=new t(f+v+5*(1+Math.ceil(v/7e3))+u),g=d.subarray(f,d.length-u),w=c.l,p=7&(c.r||0);if(s){p&&(g[0]=c.r>>3);for(var y=U[s-1],M=y>>13,E=8191&y,z=(1<<o)-1,A=c.p||new n(32768),_=c.h||new n(z+1),x=Math.ceil(o/3),D=2*x,T=function(t){return(a[t]^a[t+1]<<x^a[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=_[H];if(A[J]=K,_[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!w)){p=C(a,g,0,F,I,S,O,q,G,j-G,p),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(a[j+Q]==a[j+Q-W]){for(var $=0;$<Z&&a[j+$]==a[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-A[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=A[J])&32767}if(R){F[q++]=268435456|h[Q]<<18|l[R];var it=31&h[Q],at=31&l[R];O+=e[it]+i[at],++I[257+it],++S[at],B=j+Q,++L}else F[q++]=a[j],++I[a[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=a[j],++I[a[j]];p=C(a,g,w,F,I,S,O,q,G,j-G,p),w||(c.r=7&p|g[p/8|0]<<3,p-=7,c.h=_,c.p=A,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+w;j+=65535){var st=j+65535;st>=v&&(g[p/8|0]=w,st=v),p=k(g,p+1,a.subarray(j,st))}c.i=v}return b(d,0,f+m(p)+u)}(a,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(a.length)))):12+s.mem,o,f,u)},O=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},j=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(L(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var a=this.b.length-this.s.z;a&&(this.b.set(n.subarray(0,a),this.s.z),this.s.z=this.b.length,this.p(this.b,!1)),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(a),32768),this.s.z=n.length-a+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n}();function q(t,n){n||(n={});var r=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}}(),e=t.length;r.p(t);var i,a=L(t,n,10+((i=n).filename?i.filename.length+1:0),8),s=a.length;return function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&O(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}}(a,n),O(a,s-8,r.d()),O(a,s-4,e),a}var B=function(){function t(t,n){this.c=S(),this.v=1,j.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),j.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=L(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=S();i.p(n.dictionary),O(t,2,i.d())}}(r,this.o),this.v=0),n&&O(r,r.length-4,this.c.d()),this.ondata(r,n)},t}(),G="undefined"!=typeof TextEncoder&&new TextEncoder,H="undefined"!=typeof TextDecoder&&new TextDecoder;try{H.decode(F,{stream:!0})}catch(t){}var J=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(K(t),this.d=n||!1)},t}();function K(n,r){if(r){for(var e=new t(n.length),i=0;i<n.length;++i)e[i]=n.charCodeAt(i);return e}if(G)return G.encode(n);var a=n.length,s=new t(n.length+(n.length>>1)),o=0,f=function(t){s[o++]=t};for(i=0;i<a;++i){if(o+5>s.length){var h=new t(o+8+(a-i<<1));h.set(s),s=h}var l=n.charCodeAt(i);l<128||r?f(l):l<2048?(f(192|l>>6),f(128|63&l)):l>55295&&l<57344?(f(240|(l=65536+(1047552&l)|1023&n.charCodeAt(++i))>>18),f(128|l>>12&63),f(128|l>>6&63),f(128|63&l)):(f(224|l>>12),f(128|l>>6&63),f(128|63&l))}return b(s,0,o)}const N=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(let r=0,e=t.length;r<e;r++)n+=t[r].length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new B,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new J(((t,n)=>{this.deflate.push(t,n)})),this.stream.push("[")}},P={clear:()=>{N.clear()},addEvent:t=>N.addEvent(t),finish:()=>N.finish(),compress:t=>function(t){return q(K(t))}(t)};addEventListener("message",(function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in P&&"function"==typeof P[n])try{const t=P[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}})),postMessage({id:void 0,method:"init",success:!0,response:void 0});']);return URL.createObjectURL(t)}());if(!e)return;Iu("[Replay] Using compression worker"+(t?` from ${t}`:""));const n=new Worker(e);return new Lu(n)}catch(t){Iu("[Replay] Failed to create compression worker")}}(e);if(t)return t}return Iu("[Replay] Using simple buffer"),new Au}({useCompression:this._options.useCompression,workerUrl:this._options.workerUrl}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}_handleException(t){Tu&&N.error("[Replay]",t),Tu&&this._options._experiments&&this._options._experiments.captureExceptions&&fe(t)}_initializeSessionForSampling(t){const e=this._options.errorSampleRate>0,n=zu({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,traceInternals:this._options._experiments.traceInternals,previousSessionId:t},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:e});this.session=n}_checkSession(){if(!this.session)return!1;const t=this.session;return!qu(t,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})||(this._refreshSession(t),!1)}async _refreshSession(t){this._isEnabled&&(await this.stop({reason:"refresh session"}),this.initializeSampling(t.id))}_addListeners(){try{na.document.addEventListener("visibilitychange",this._handleVisibilityChange),na.addEventListener("blur",this._handleWindowBlur),na.addEventListener("focus",this._handleWindowFocus),na.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(function(t){const e=Ne(),n=Ae();e.addScopeListener((t=>e=>{if(!t.isEnabled())return;const n=function(t){const e=t.getLastBreadcrumb&&t.getLastBreadcrumb();return fl!==e&&e?(fl=e,!e.category||["fetch","xhr","sentry.event","sentry.transaction"].includes(e.category)||e.category.startsWith("ui.")?null:"console"===e.category?function(t){const e=t.data&&t.data.arguments;if(!Array.isArray(e)||0===e.length)return _u(t);let n=!1;const r=e.map((t=>{if(!t)return t;if("string"==typeof t)return t.length>aa?(n=!0,`${t.slice(0,aa)}…`):t;if("object"==typeof t)try{const e=ce(t,7);return JSON.stringify(e).length>aa?(n=!0,`${JSON.stringify(e,null,2).slice(0,aa)}…`):e}catch(t){}return t}));return _u({...t,data:{...t.data,arguments:r,...n?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}(e):_u(e)):null}(e);n&&lu(t,n)})(t)),ao((t=>e=>{if(!t.isEnabled())return;const n=function(t){const{target:e,message:n}=function(t){const e="click"===t.name;let n,r=null;try{r=e?hu(t.event):fu(t.event),n=C(r,{maxStringLength:200})||"<unknown>"}catch(t){n="<unknown>"}return{target:r,message:n}}(t);return _u({category:`ui.${t.name}`,...ku(e,n)})}(e);if(!n)return;const r="click"===e.name,i=r?e.event:void 0;var o,s,a;!(r&&t.clickDetector&&i&&i.target)||i.altKey||i.metaKey||i.ctrlKey||i.shiftKey||(o=t.clickDetector,s=n,a=hu(e.event),o.handleClick(s,a)),lu(t,n)})(t)),Qi(function(t){return e=>{if(!t.isEnabled())return;const n=function(t){const{from:e,to:n}=t,r=Date.now()/1e3;return{type:"navigation.push",start:r,end:r,name:n,data:{previous:e}}}(e);null!==n&&(t.getContext().urls.push(n.name),t.triggerUserActivity(),t.addUpdate((()=>(Qu(t,[n]),!1))))}}(t)),hl(t);const r=function(t,e=!1){const n=e?Xu(t):void 0;return Object.assign(((e,r)=>{if(!t.isEnabled())return e;if(function(t){return"replay_event"===t.type}(e))return delete e.breadcrumbs,e;if(!Ju(e)&&!Vu(e)&&!Ku(e))return e;if(!t.checkAndHandleExpiredSession())return e;if(Ku(e))return t.flush(),e.contexts.feedback.replay_id=t.getSessionId(),function(t,e){t.triggerUserActivity(),t.addUpdate((()=>!e.timestamp||(t.throttledAddEvent({type:gc.Custom,timestamp:1e3*e.timestamp,data:{tag:"breadcrumb",payload:{timestamp:e.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:e.event_id}}}}),!1)))}(t,e),e;if(function(t,e){return!(t.type||!t.exception||!t.exception.values||!t.exception.values.length||!e.originalException||!e.originalException.__rrweb__)}(e,r)&&!t.getOptions()._experiments.captureExceptions)return Tu&&N.log("[Replay] Ignoring error from rrweb internals",e),null;const i=function(t,e){return"buffer"===t.recordingMode&&e.message!==oa&&!(!e.exception||e.type)&&Fu(t.getOptions().errorSampleRate)}(t,e);return(i||"session"===t.recordingMode)&&(e.tags={...e.tags,replayId:t.getSessionId()}),n&&n(e,{statusCode:200}),e}),{id:"Replay"})}(t,!ml(n));n&&n.addEventProcessor?n.addEventProcessor(r):Jn(r),ml(n)&&(n.on("beforeSendEvent",function(t){return e=>{t.isEnabled()&&Ju(e)&&function(t,e){const n=e.exception&&e.exception.values&&e.exception.values[0].value;"string"==typeof n&&(n.match(/reactjs\.org\/docs\/error-decoder\.html\?invariant=(418|419|422|423|425)/)||n.match(/(does not match server-rendered HTML|Hydration failed because)/i))&&lu(t,_u({category:"replay.hydrate-error"}))}(t,e)}}(t)),n.on("afterSendEvent",Xu(t)),n.on("createDsc",(e=>{const n=t.getSessionId();n&&t.isEnabled()&&"session"===t.recordingMode&&t.checkAndHandleExpiredSession()&&(e.replay_id=n)})),n.on("startTransaction",(e=>{t.lastTransaction=e})),n.on("finishTransaction",(e=>{t.lastTransaction=e})),n.on("beforeSendFeedback",((e,n)=>{const r=t.getSessionId();n&&n.includeReplay&&t.isEnabled()&&r&&e.contexts&&e.contexts.feedback&&(e.contexts.feedback.replay_id=r)})))}(this),this._hasInitializedCoreListeners=!0)}catch(t){this._handleException(t)}this._performanceCleanupCallback=function(t){function e(e){t.performanceEntries.includes(e)||t.performanceEntries.push(e)}function n({entries:t}){t.forEach(e)}const r=[];return["navigation","paint","resource"].forEach((t=>{r.push(Ys(t,n))})),r.push(Ws((({metric:e})=>{t.replayPerformanceEntries.push(function(t){const e=t.entries,n=e[e.length-1],r=n?n.element:void 0,i=t.value,o=Eu(i);return{type:"largest-contentful-paint",name:"largest-contentful-paint",start:o,end:o,data:{value:i,size:i,nodeId:r?su.mirror.getId(r):void 0}}}(e))}))),()=>{r.forEach((t=>t()))}}(this)}_removeListeners(){try{na.document.removeEventListener("visibilitychange",this._handleVisibilityChange),na.removeEventListener("blur",this._handleWindowBlur),na.removeEventListener("focus",this._handleWindowFocus),na.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceCleanupCallback&&this._performanceCleanupCallback()}catch(t){this._handleException(t)}}__init(){this._handleVisibilityChange=()=>{"visible"===na.document.visibilityState?this._doChangeToForegroundTasks():this._doChangeToBackgroundTasks()}}__init2(){this._handleWindowBlur=()=>{const t=_u({category:"ui.blur"});this._doChangeToBackgroundTasks(t)}}__init3(){this._handleWindowFocus=()=>{const t=_u({category:"ui.focus"});this._doChangeToForegroundTasks(t)}}__init4(){this._handleKeyboardEvent=t=>{!function(t,e){if(!t.isEnabled())return;t.updateUserActivity();const n=function(t){const{metaKey:e,shiftKey:n,ctrlKey:r,altKey:i,key:o,target:s}=t;if(!s||function(t){return"INPUT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable}(s)||!o)return null;const a=e||r||i,c=1===o.length;if(!a&&c)return null;const u=C(s,{maxStringLength:200})||"<unknown>";return _u({category:"ui.keyDown",message:u,data:{...ku(s,u).data,metaKey:e,shiftKey:n,ctrlKey:r,altKey:i,key:o}})}(e);n&&lu(t,n)}(this,t)}}_doChangeToBackgroundTasks(t){this.session&&(Uu(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(t&&this._createCustomBreadcrumb(t),this.conditionalFlush()))}_doChangeToForegroundTasks(t){this.session&&(this.checkAndHandleExpiredSession()?t&&this._createCustomBreadcrumb(t):Iu("[Replay] Document has become active, but session has expired"))}_updateUserActivity(t=Date.now()){this._lastActivity=t}_updateSessionActivity(t=Date.now()){this.session&&(this.session.lastActivity=t,this._maybeSaveSession())}_createCustomBreadcrumb(t){this.addUpdate((()=>{this.throttledAddEvent({type:gc.Custom,timestamp:t.timestamp||0,data:{tag:"breadcrumb",payload:t}})}))}_addPerformanceEntries(){const t=(e=this.performanceEntries,e.map(Cu).filter(Boolean)).concat(this.replayPerformanceEntries);var e;return this.performanceEntries=[],this.replayPerformanceEntries=[],Promise.all(Qu(this,t))}_clearContext(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}_updateInitialTimestampFromEventBuffer(){const{session:t,eventBuffer:e}=this;if(!t||!e)return;if(t.segmentId)return;const n=e.getEarliestTimestamp();n&&n<this._context.initialTimestamp&&(this._context.initialTimestamp=n)}_popEventContext(){const t={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),t}async _runFlush(){const t=this.getSessionId();if(this.session&&this.eventBuffer&&t){if(await this._addPerformanceEntries(),this.eventBuffer&&this.eventBuffer.hasEvents&&(await async function(t){try{return Promise.all(Qu(t,[gl(na.performance.memory)]))}catch(t){return[]}}(this),this.eventBuffer&&t===this.getSessionId()))try{this._updateInitialTimestampFromEventBuffer();const e=Date.now();if(e-this._context.initialTimestamp>this._options.maxReplayDuration+3e4)throw new Error("Session is too long, not sending replay");const n=this._popEventContext(),r=this.session.segmentId++;this._maybeSaveSession();const i=await this.eventBuffer.finish();await bl({replayId:t,recordingData:i,segmentId:r,eventContext:n,session:this.session,options:this.getOptions(),timestamp:e})}catch(t){this._handleException(t),this.stop({reason:"sendReplay"});const e=Ae();e&&e.recordDroppedEvent("send_error","replay")}}else Tu&&N.error("[Replay] No session or eventBuffer found to flush.")}__init5(){this._flush=async({force:t=!1}={})=>{if(!this._isEnabled&&!t)return;if(!this.checkAndHandleExpiredSession())return void(Tu&&N.error("[Replay] Attempting to finish replay event after session expired."));if(!this.session)return;const e=this.session.started,n=Date.now()-e;this._debouncedFlush.cancel();const r=n<this._options.minReplayDuration,i=n>this._options.maxReplayDuration+5e3;if(r||i)return Iu(`[Replay] Session duration (${Math.floor(n/1e3)}s) is too ${r?"short":"long"}, not sending replay.`,this._options._experiments.traceInternals),void(r&&this._debouncedFlush());const o=this.eventBuffer;if(o&&0===this.session.segmentId&&!o.hasCheckout&&Iu("[Replay] Flushing initial segment without checkout.",this._options._experiments.traceInternals),!this._flushLock)return this._flushLock=this._runFlush(),await this._flushLock,void(this._flushLock=void 0);try{await this._flushLock}catch(t){Tu&&N.error(t)}finally{this._debouncedFlush()}}}_maybeSaveSession(){this.session&&this._options.stickySession&&$u(this.session)}__init6(){this._onMutationHandler=t=>{const e=t.length,n=this._options.mutationLimit,r=n&&e>n;if(e>this._options.mutationBreadcrumbLimit||r){const t=_u({category:"replay.mutations",data:{count:e,limit:r}});this._createCustomBreadcrumb(t)}return!r||(this.stop({reason:"mutationLimit",forceFlush:"session"===this.recordingMode}),!1)}}}function kl(t,e,n,r){const i=[...t,..."string"==typeof r?r.split(","):[],...e];return void 0!==n&&("string"==typeof n&&i.push(`.${n}`),D((()=>{console.warn("[Replay] You are using a deprecated configuration item for privacy. Read the documentation on how to use the new privacy configuration.")}))),i.join(",")}const xl='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',Cl=["content-length","content-type","accept"];let El=!1;const Tl=t=>new Il(t);class Il{static __initStatic(){this.id="Replay"}constructor({flushMinDelay:t=5e3,flushMaxDelay:e=5500,minReplayDuration:n=4999,maxReplayDuration:r=36e5,stickySession:i=!0,useCompression:o=!0,workerUrl:s,_experiments:a={},sessionSampleRate:c,errorSampleRate:u,maskAllText:l=!0,maskAllInputs:d=!0,blockAllMedia:p=!0,mutationBreadcrumbLimit:h=750,mutationLimit:f=1e4,slowClickTimeout:m=7e3,slowClickIgnoreSelectors:g=[],networkDetailAllowUrls:y=[],networkDetailDenyUrls:v=[],networkCaptureBodies:_=!0,networkRequestHeaders:b=[],networkResponseHeaders:S=[],mask:w=[],maskAttributes:k=["title","placeholder"],unmask:x=[],block:C=[],unblock:E=[],ignore:T=[],maskFn:I,beforeAddRecordingEvent:R,beforeErrorSampling:O,blockClass:M,blockSelector:A,maskInputOptions:D,maskTextClass:N,maskTextSelector:L,ignoreClass:P}={}){this.name=Il.id;const F=function({mask:t,unmask:e,block:n,unblock:r,ignore:i,blockClass:o,blockSelector:s,maskTextClass:a,maskTextSelector:c,ignoreClass:u}){const l={maskTextSelector:kl(t,[".sentry-mask","[data-sentry-mask]"],a,c),unmaskTextSelector:kl(e,[".sentry-unmask","[data-sentry-unmask]"]),blockSelector:kl(n,[".sentry-block","[data-sentry-block]",'base[href="/"]'],o,s),unblockSelector:kl(r,[".sentry-unblock","[data-sentry-unblock]"]),ignoreSelector:kl(i,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'],u)};return o instanceof RegExp&&(l.blockClass=o),a instanceof RegExp&&(l.maskTextClass=a),l}({mask:w,unmask:x,block:C,unblock:E,ignore:T,blockClass:M,blockSelector:A,maskTextClass:N,maskTextSelector:L,ignoreClass:P});if(this._recordingOptions={maskAllInputs:d,maskAllText:l,maskInputOptions:{...D||{},password:!0},maskTextFn:I,maskInputFn:I,maskAttributeFn:(t,e,n)=>function({el:t,key:e,maskAttributes:n,maskAllText:r,privacyOptions:i,value:o}){return r?i.unmaskTextSelector&&t.matches(i.unmaskTextSelector)?o:n.includes(e)||"value"===e&&"INPUT"===t.tagName&&["submit","button"].includes(t.getAttribute("type")||"")?o.replace(/[\S]/g,"*"):o:o}({maskAttributes:k,maskAllText:l,privacyOptions:F,key:t,value:e,el:n}),...F,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:t=>{try{t.__rrweb__=!0}catch(t){}}},this._initialOptions={flushMinDelay:t,flushMaxDelay:e,minReplayDuration:Math.min(n,15e3),maxReplayDuration:Math.min(r,36e5),stickySession:i,sessionSampleRate:c,errorSampleRate:u,useCompression:o,workerUrl:s,blockAllMedia:p,maskAllInputs:d,maskAllText:l,mutationBreadcrumbLimit:h,mutationLimit:f,slowClickTimeout:m,slowClickIgnoreSelectors:g,networkDetailAllowUrls:y,networkDetailDenyUrls:v,networkCaptureBodies:_,networkRequestHeaders:Rl(b),networkResponseHeaders:Rl(S),beforeAddRecordingEvent:R,beforeErrorSampling:O,_experiments:a},"number"==typeof c&&(console.warn(`[Replay] You are passing \`sessionSampleRate\` to the Replay integration.\nThis option is deprecated and will be removed soon.\nInstead, configure \`replaysSessionSampleRate\` directly in the SDK init options, e.g.:\nSentry.init({ replaysSessionSampleRate: ${c} })`),this._initialOptions.sessionSampleRate=c),"number"==typeof u&&(console.warn(`[Replay] You are passing \`errorSampleRate\` to the Replay integration.\nThis option is deprecated and will be removed soon.\nInstead, configure \`replaysOnErrorSampleRate\` directly in the SDK init options, e.g.:\nSentry.init({ replaysOnErrorSampleRate: ${u} })`),this._initialOptions.errorSampleRate=u),this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?`${this._recordingOptions.blockSelector},${xl}`:xl),this._isInitialized&&ls())throw new Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}get _isInitialized(){return El}set _isInitialized(t){El=t}setupOnce(){ls()&&(this._setup(),setTimeout((()=>this._initialize())))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}flush(t){return this._replay&&this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(t):Promise.resolve()}getReplayId(){if(this._replay&&this._replay.isEnabled())return this._replay.getSessionId()}_initialize(){this._replay&&(this._maybeLoadFromReplayCanvasIntegration(),this._replay.initializeSampling())}_setup(){const t=function(t){const e=Ae(),n=e&&e.getOptions(),r={sessionSampleRate:0,errorSampleRate:0,...Y(t)};return n?(null==t.sessionSampleRate&&null==t.errorSampleRate&&null==n.replaysSessionSampleRate&&null==n.replaysOnErrorSampleRate&&D((()=>{console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")})),"number"==typeof n.replaysSessionSampleRate&&(r.sessionSampleRate=n.replaysSessionSampleRate),"number"==typeof n.replaysOnErrorSampleRate&&(r.errorSampleRate=n.replaysOnErrorSampleRate),r):(D((()=>{console.warn("SDK client is not available.")})),r)}(this._initialOptions);this._replay=new wl({options:t,recordingOptions:this._recordingOptions})}_maybeLoadFromReplayCanvasIntegration(){try{const t=Ae().getIntegrationByName("ReplayCanvas");if(!t)return;this._replay._canvas=t.getOptions()}catch(t){}}}function Rl(t){return[...Cl,...t.map((t=>t.toLowerCase()))]}function Ol(){const t=Ae();return t&&t.getIntegrationByName&&t.getIntegrationByName("Replay")}var Ml;function Al(t,e,n=1/0,r=0){return t?t.nodeType!==t.ELEMENT_NODE||r>n?-1:e(t)?r:Al(t.parentNode,e,n,r+1):-1}function Dl(t,e){return n=>{const r=n;if(null===r)return!1;try{if(t)if("string"==typeof t){if(r.matches(`.${t}`))return!0}else if(function(t,e){for(let n=t.classList.length;n--;){const r=t.classList[n];if(e.test(r))return!0}return!1}(r,t))return!0;return!(!e||!r.matches(e))}catch(t){return!1}}}Il.__initStatic(),function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"}(Ml||(Ml={}));const Nl="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.";let Ll={map:{},getId:()=>(console.error(Nl),-1),getNode:()=>(console.error(Nl),null),removeNodeFromMap(){console.error(Nl)},has:()=>(console.error(Nl),!1),reset(){console.error(Nl)}};function Pl(t,e,n,r,i=window){const o=i.Object.getOwnPropertyDescriptor(t,e);return i.Object.defineProperty(t,e,r?n:{set(t){Ul((()=>{n.set.call(this,t)}),0),o&&o.set&&o.set.call(this,t)}}),()=>Pl(t,e,o||{},!0)}function Fl(t,e,n){try{if(!(e in t))return()=>{};const r=t[e],i=n(r);return"function"==typeof i&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:r}})),t[e]=i,()=>{t[e]=r}}catch(t){return()=>{}}}function jl(t,e,n,r,i){if(!t)return!1;const o=function(t){return t?t.nodeType===t.ELEMENT_NODE?t:t.parentElement:null}(t);if(!o)return!1;const s=Dl(e,n);if(!i){const t=r&&o.matches(r);return s(o)&&!t}const a=Al(o,s);let c=-1;return!(a<0)&&(r&&(c=Al(o,Dl(null,r))),a>-1&&c<0||a<c)}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(Ll=new Proxy(Ll,{get:(t,e,n)=>("map"===e&&console.error(Nl),Reflect.get(t,e,n))})),/[1-9][0-9]{12}/.test(Date.now().toString());const $l={};function Hl(t){const e=$l[t];if(e)return e;const n=window.document;let r=window[t];if(n&&"function"==typeof n.createElement)try{const e=n.createElement("iframe");e.hidden=!0,n.head.appendChild(e);const i=e.contentWindow;i&&i[t]&&(r=i[t]),n.head.removeChild(e)}catch(t){}return $l[t]=r.bind(window)}function Bl(...t){return Hl("requestAnimationFrame")(...t)}function Ul(...t){return Hl("setTimeout")(...t)}var ql=(t=>(t[t["2D"]=0]="2D",t[t.WebGL=1]="WebGL",t[t.WebGL2=2]="WebGL2",t))(ql||{});let zl;const Wl=t=>zl?(...e)=>{try{return t(...e)}catch(t){if(zl&&!0===zl(t))return()=>{};throw t}}:t;for(var Yl="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Gl="undefined"==typeof Uint8Array?[]:new Uint8Array(256),Jl=0;Jl<64;Jl++)Gl[Yl.charCodeAt(Jl)]=Jl;const Vl=new Map,Kl=(t,e,n)=>{if(!t||!Zl(t,e)&&"object"!=typeof t)return;const r=function(t,e){let n=Vl.get(t);return n||(n=new Map,Vl.set(t,n)),n.has(e)||n.set(e,[]),n.get(e)}(n,t.constructor.name);let i=r.indexOf(t);return-1===i&&(i=r.length,r.push(t)),i};function Xl(t,e,n){if(t instanceof Array)return t.map((t=>Xl(t,e,n)));if(null===t)return t;if(t instanceof Float32Array||t instanceof Float64Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Uint8Array||t instanceof Uint16Array||t instanceof Int16Array||t instanceof Int8Array||t instanceof Uint8ClampedArray)return{rr_type:t.constructor.name,args:[Object.values(t)]};if(t instanceof ArrayBuffer){const e=t.constructor.name,n=function(t){var e,n=new Uint8Array(t),r=n.length,i="";for(e=0;e<r;e+=3)i+=Yl[n[e]>>2],i+=Yl[(3&n[e])<<4|n[e+1]>>4],i+=Yl[(15&n[e+1])<<2|n[e+2]>>6],i+=Yl[63&n[e+2]];return r%3==2?i=i.substring(0,i.length-1)+"=":r%3==1&&(i=i.substring(0,i.length-2)+"=="),i}(t);return{rr_type:e,base64:n}}if(t instanceof DataView)return{rr_type:t.constructor.name,args:[Xl(t.buffer,e,n),t.byteOffset,t.byteLength]};if(t instanceof HTMLImageElement){const e=t.constructor.name,{src:n}=t;return{rr_type:e,src:n}}return t instanceof HTMLCanvasElement?{rr_type:"HTMLImageElement",src:t.toDataURL()}:t instanceof ImageData?{rr_type:t.constructor.name,args:[Xl(t.data,e,n),t.width,t.height]}:Zl(t,e)||"object"==typeof t?{rr_type:t.constructor.name,index:Kl(t,e,n)}:t}const Ql=(t,e,n)=>t.map((t=>Xl(t,e,n))),Zl=(t,e)=>{const n=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter((t=>"function"==typeof e[t]));return Boolean(n.find((n=>t instanceof e[n])))};function td(t,e,n,r,i){const o=[];try{const s=Fl(t.HTMLCanvasElement.prototype,"getContext",(function(t){return function(o,...s){if(!jl(this,e,n,r,!0)){const t=function(t){return"experimental-webgl"===t?"webgl":t}(o);if("__context"in this||(this.__context=t),i&&["webgl","webgl2"].includes(t))if(s[0]&&"object"==typeof s[0]){const t=s[0];t.preserveDrawingBuffer||(t.preserveDrawingBuffer=!0)}else s.splice(0,1,{preserveDrawingBuffer:!0})}return t.apply(this,[o,...s])}}));o.push(s)}catch(t){console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{o.forEach((t=>t()))}}function ed(t,e,n,r,i,o,s,a){const c=[],u=Object.getOwnPropertyNames(t);for(const s of u)if(!["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(s))try{if("function"!=typeof t[s])continue;const u=Fl(t,s,(function(t){return function(...c){const u=t.apply(this,c);if(Kl(u,a,this),"tagName"in this.canvas&&!jl(this.canvas,r,i,o,!0)){const t=Ql(c,a,this),r={type:e,property:s,args:t};n(this.canvas,r)}return u}}));c.push(u)}catch(r){const i=Pl(t,s,{set(t){n(this.canvas,{type:e,property:s,args:[t],setter:!0})}});c.push(i)}return c}class nd{reset(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}freeze(){this.frozen=!0}unfreeze(){this.frozen=!1}lock(){this.locked=!0}unlock(){this.locked=!1}constructor(t){this.pendingCanvasMutations=new Map,this.rafStamps={latestId:0,invokeId:null},this.frozen=!1,this.locked=!1,this.processMutation=(t,e)=>{!(this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId)&&this.rafStamps.invokeId||(this.rafStamps.invokeId=this.rafStamps.latestId),this.pendingCanvasMutations.has(t)||this.pendingCanvasMutations.set(t,[]),this.pendingCanvasMutations.get(t).push(e)};const{sampling:e="all",win:n,blockClass:r,blockSelector:i,unblockSelector:o,maxCanvasSize:s,recordCanvas:a,dataURLOptions:c,errorHandler:u}=t;this.mutationCb=t.mutationCb,this.mirror=t.mirror,this.options=t,u&&(zl=u),t.enableManualSnapshot||Wl((()=>{a&&"all"===e&&this.initCanvasMutationObserver(n,r,i,o),a&&"number"==typeof e&&this.initCanvasFPSObserver(e,n,r,i,o,s,{dataURLOptions:c})}))()}initCanvasFPSObserver(t,e,n,r,i,o,s){const a=td(e,n,r,i,!0),c=this.takeSnapshot(!1,t,e,n,r,i,o,s.dataURLOptions);this.resetObservers=()=>{a(),cancelAnimationFrame(c)}}initCanvasMutationObserver(t,e,n,r){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();const i=td(t,e,n,r,!1),o=function(t,e,n,r,i){const o=[],s=Object.getOwnPropertyNames(e.CanvasRenderingContext2D.prototype);for(const a of s)try{if("function"!=typeof e.CanvasRenderingContext2D.prototype[a])continue;const s=Fl(e.CanvasRenderingContext2D.prototype,a,(function(o){return function(...s){return jl(this.canvas,n,r,i,!0)||Ul((()=>{const n=Ql(s,e,this);t(this.canvas,{type:ql["2D"],property:a,args:n})}),0),o.apply(this,s)}}));o.push(s)}catch(n){const r=Pl(e.CanvasRenderingContext2D.prototype,a,{set(e){t(this.canvas,{type:ql["2D"],property:a,args:[e],setter:!0})}});o.push(r)}return()=>{o.forEach((t=>t()))}}(this.processMutation.bind(this),t,e,n,r),s=function(t,e,n,r,i,o){const s=[];return s.push(...ed(e.WebGLRenderingContext.prototype,ql.WebGL,t,n,r,i,0,e)),void 0!==e.WebGL2RenderingContext&&s.push(...ed(e.WebGL2RenderingContext.prototype,ql.WebGL2,t,n,r,i,0,e)),()=>{s.forEach((t=>t()))}}(this.processMutation.bind(this),t,e,n,r,this.mirror);this.resetObservers=()=>{i(),o(),s()}}snapshot(t){const{options:e}=this,n=this.takeSnapshot(!0,"all"===e.sampling?2:e.sampling||2,e.win,e.blockClass,e.blockSelector,e.unblockSelector,e.maxCanvasSize,e.dataURLOptions,t);this.resetObservers=()=>{cancelAnimationFrame(n)}}takeSnapshot(t,e,n,r,i,o,s,a,c){const u=new Map,l=new Worker(function(){const t=new Blob(['for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t="undefined"==typeof Uint8Array?[]:new Uint8Array(256),a=0;a<64;a++)t[e.charCodeAt(a)]=a;var n=function(t){var a,n=new Uint8Array(t),r=n.length,s="";for(a=0;a<r;a+=3)s+=e[n[a]>>2],s+=e[(3&n[a])<<4|n[a+1]>>4],s+=e[(15&n[a+1])<<2|n[a+2]>>6],s+=e[63&n[a+2]];return r%3==2?s=s.substring(0,s.length-1)+"=":r%3==1&&(s=s.substring(0,s.length-2)+"=="),s};const r=new Map,s=new Map;const i=self;i.onmessage=async function(e){if(!("OffscreenCanvas"in globalThis))return i.postMessage({id:e.data.id});{const{id:t,bitmap:a,width:o,height:f,maxCanvasSize:c,dataURLOptions:g}=e.data,u=async function(e,t,a){const r=e+"-"+t;if("OffscreenCanvas"in globalThis){if(s.has(r))return s.get(r);const i=new OffscreenCanvas(e,t);i.getContext("2d");const o=await i.convertToBlob(a),f=await o.arrayBuffer(),c=n(f);return s.set(r,c),c}return""}(o,f,g),[h,d]=function(e,t,a){if(!a)return[e,t];const[n,r]=a;if(e<=n&&t<=r)return[e,t];let s=e,i=t;return s>n&&(i=Math.floor(n*t/e),s=n),i>r&&(s=Math.floor(r*e/t),i=r),[s,i]}(o,f,c),l=new OffscreenCanvas(h,d),w=l.getContext("bitmaprenderer"),p=h===o&&d===f?a:await createImageBitmap(a,{resizeWidth:h,resizeHeight:d,resizeQuality:"low"});w.transferFromImageBitmap(p),a.close();const y=await l.convertToBlob(g),v=y.type,b=await y.arrayBuffer(),m=n(b);if(p.close(),!r.has(t)&&await u===m)return r.set(t,m),i.postMessage({id:t});if(r.get(t)===m)return i.postMessage({id:t});i.postMessage({id:t,type:v,base64:m,width:o,height:f}),r.set(t,m)}};']);return URL.createObjectURL(t)}());l.onmessage=t=>{const e=t.data,{id:n}=e;if(u.set(n,!1),!("base64"in e))return;const{base64:r,type:i,width:o,height:s}=e;this.mutationCb({id:n,type:ql["2D"],commands:[{property:"clearRect",args:[0,0,o,s]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:r}],type:i}]},0,0,o,s]}]})};const d=1e3/e;let p,h=0;const f=e=>{h&&e-h<d||(h=e,(t=>{if(t)return[t];const e=[];return n.document.querySelectorAll("canvas").forEach((t=>{jl(t,r,i,o,!0)||e.push(t)})),e})(c).forEach((e=>{const n=this.mirror.getId(e);if(!u.get(n)&&e.width&&e.height){if(u.set(n,!0),!t&&["webgl","webgl2"].includes(e.__context)){const t=e.getContext(e.__context);!1===ss([t,"optionalAccess",t=>t.getContextAttributes,"call",t=>t(),"optionalAccess",t=>t.preserveDrawingBuffer])&&t.clear(t.COLOR_BUFFER_BIT)}createImageBitmap(e).then((t=>{l.postMessage({id:n,bitmap:t,width:e.width,height:e.height,dataURLOptions:a,maxCanvasSize:s},[t])})).catch((t=>{Wl((()=>{throw t}))()}))}}))),p=Bl(f)};return p=Bl(f),p}startPendingCanvasMutationFlusher(){Bl((()=>this.flushPendingCanvasMutations()))}startRAFTimestamping(){const t=e=>{this.rafStamps.latestId=e,Bl(t)};Bl(t)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach(((t,e)=>{const n=this.mirror.getId(e);this.flushPendingCanvasMutationFor(e,n)})),Bl((()=>this.flushPendingCanvasMutations()))}flushPendingCanvasMutationFor(t,e){if(this.frozen||this.locked)return;const n=this.pendingCanvasMutations.get(t);if(!n||-1===e)return;const r=n.map((t=>{const{type:e,...n}=t;return n})),{type:i}=n[0];this.mutationCb({id:e,type:i,commands:r}),this.pendingCanvasMutations.delete(t)}}const rd={low:{sampling:{canvas:1},dataURLOptions:{type:"image/webp",quality:.25}},medium:{sampling:{canvas:2},dataURLOptions:{type:"image/webp",quality:.4}},high:{sampling:{canvas:4},dataURLOptions:{type:"image/webp",quality:.5}}},id="ReplayCanvas",od=1280,sd=(t={})=>{const[e,n]=t.maxCanvasSize||[],r={quality:t.quality||"medium",enableManualSnapshot:t.enableManualSnapshot,maxCanvasSize:[e?Math.min(e,od):od,n?Math.min(n,od):od]};let i;const o=new Promise((t=>i=t));return{name:id,setupOnce(){},getOptions(){const{quality:t,enableManualSnapshot:e,maxCanvasSize:n}=r;return{enableManualSnapshot:e,recordCanvas:!0,getCanvasManager:t=>{const r=new nd({...t,enableManualSnapshot:e,maxCanvasSize:n,errorHandler:t=>{try{"object"==typeof t&&(t.__rrweb__=!0)}catch(t){}}});return i(r),r},...rd[t||"medium"]||rd.medium}},async snapshot(t){(await o).snapshot(t)}}},ad=ze(id,sd),cd=b,ud="#ffffff",ld="inherit",dd="rgba(108, 95, 199, 1)",pd={fontFamily:"system-ui, 'Helvetica Neue', Arial, sans-serif",fontSize:"14px",background:ud,backgroundHover:"#f6f6f7",foreground:"#2b2233",border:"1.5px solid rgba(41, 35, 47, 0.13)",borderRadius:"25px",boxShadow:"0px 4px 24px 0px rgba(43, 34, 51, 0.12)",success:"#268d75",error:"#df3338",submitBackground:"rgba(88, 74, 192, 1)",submitBackgroundHover:dd,submitBorder:dd,submitOutlineFocus:"#29232f",submitForeground:ud,submitForegroundHover:ud,cancelBackground:"transparent",cancelBackgroundHover:"var(--background-hover)",cancelBorder:"var(--border)",cancelOutlineFocus:"var(--input-outline-focus)",cancelForeground:"var(--foreground)",cancelForegroundHover:"var(--foreground)",inputBackground:ld,inputForeground:ld,inputBorder:"var(--border)",inputOutlineFocus:dd,formBorderRadius:"20px",formContentBorderRadius:"6px"},hd=pd,fd={...pd,background:"#29232f",backgroundHover:"#352f3b",foreground:"#ebe6ef",border:"1.5px solid rgba(235, 230, 239, 0.15)",success:"#2da98c",error:"#f55459"},md="widget",gd="api";function yd({name:t,email:e,message:n,source:r=gd,url:i=T()},o={}){if(!n)throw new Error("Unable to submit feedback with empty message");return async function({feedback:{message:t,email:e,name:n,source:r,url:i}},{includeReplay:o=!0}={}){const s=Ae(),a=s&&s.getTransport(),c=s&&s.getDsn();if(!s||!a||!c)return;const u={contexts:{feedback:{contact_email:e,name:n,message:t,url:i,source:r}},type:"feedback"};return Ce((async t=>{t.clearBreadcrumbs(),[gd,md].includes(String(r))&&t.setLevel("info");const e=await async function({client:t,scope:e,event:n}){const r={};t.emit&&t.emit("preprocessEvent",n,r);const i=await de(t.getOptions(),n,r,e,t,Qt());return null===i?(t.recordDroppedEvent("event_processor","feedback",n),null):(i.platform=i.platform||"javascript",i)}({scope:t,client:s,event:u});if(!e)return;s.emit&&s.emit("beforeSendFeedback",e,{includeReplay:Boolean(o)});const n=Hn(e,c,s.getOptions()._metadata,s.getOptions().tunnel);let i;try{i=await a.send(n)}catch(t){const e=new Error("Unable to send Feedback");try{e.cause=t}catch(t){}throw e}if(i){if("number"==typeof i.statusCode&&(i.statusCode<200||i.statusCode>=300))throw new Error("Unable to send Feedback");return i}}))}({feedback:{name:t,email:e,message:n,url:i,source:r}},o)}const vd="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function _d(t,e){return{...t,...e,themeDark:{...t.themeDark,...e.themeDark},themeLight:{...t.themeLight,...e.themeLight}}}function bd(t){return`\n  --background: ${t.background};\n  --background-hover: ${t.backgroundHover};\n  --foreground: ${t.foreground};\n  --error: ${t.error};\n  --success: ${t.success};\n  --border: ${t.border};\n  --border-radius: ${t.borderRadius};\n  --box-shadow: ${t.boxShadow};\n\n  --submit-background: ${t.submitBackground};\n  --submit-background-hover: ${t.submitBackgroundHover};\n  --submit-border: ${t.submitBorder};\n  --submit-outline-focus: ${t.submitOutlineFocus};\n  --submit-foreground: ${t.submitForeground};\n  --submit-foreground-hover: ${t.submitForegroundHover};\n\n  --cancel-background: ${t.cancelBackground};\n  --cancel-background-hover: ${t.cancelBackgroundHover};\n  --cancel-border: ${t.cancelBorder};\n  --cancel-outline-focus: ${t.cancelOutlineFocus};\n  --cancel-foreground: ${t.cancelForeground};\n  --cancel-foreground-hover: ${t.cancelForegroundHover};\n\n  --input-background: ${t.inputBackground};\n  --input-foreground: ${t.inputForeground};\n  --input-border: ${t.inputBorder};\n  --input-outline-focus: ${t.inputOutlineFocus};\n\n  --form-border-radius: ${t.formBorderRadius};\n  --form-content-border-radius: ${t.formContentBorderRadius};\n  `}function Sd(t,e){return Object.entries(e).forEach((([e,n])=>{t.setAttributeNS(null,e,n)})),t}const wd=20,kd="http://www.w3.org/2000/svg";function xd(t,e,...n){const r=cd.document.createElement(t);e&&Object.entries(e).forEach((([t,e])=>{"className"===t&&"string"==typeof e?r.setAttribute("class",e):"boolean"==typeof e&&e?r.setAttribute(t,""):"string"==typeof e?r.setAttribute(t,e):t.startsWith("on")&&"function"==typeof e&&r.addEventListener(t.substring(2).toLowerCase(),e)}));for(const t of n)Cd(r,t);return r}function Cd(t,e){const n=cd.document;if(null!=e)if(Array.isArray(e))for(const n of e)Cd(t,n);else!1===e||("string"==typeof e?t.appendChild(n.createTextNode(e)):e instanceof Node?t.appendChild(e):t.appendChild(n.createTextNode(String(e))))}function Ed(t,e){const n=t.get(e);return"string"==typeof n?n.trim():""}const Td="http://www.w3.org/2000/svg";function Id({formTitle:t,showBranding:e,showName:n,showEmail:r,isNameRequired:i,isEmailRequired:o,colorScheme:s,defaultName:a,defaultEmail:c,onClosed:u,onCancel:l,onSubmit:d,...p}){let h=null;function f(){h&&(h.open=!1)}const{el:m,showError:g,hideError:y}=function({nameLabel:t,namePlaceholder:e,emailLabel:n,emailPlaceholder:r,messageLabel:i,messagePlaceholder:o,isRequiredLabel:s,cancelButtonLabel:a,submitButtonLabel:c,showName:u,showEmail:l,isNameRequired:d,isEmailRequired:p,defaultName:h,defaultEmail:f,onCancel:m,onSubmit:g}){const{el:y}=function({label:t}){return{el:xd("button",{type:"submit",className:"btn btn--primary","aria-label":t},t)}}({label:c}),v=xd("div",{className:"form__error-container form__error-container--hidden","aria-hidden":"true"}),_=xd("input",{id:"name",type:u?"text":"hidden","aria-hidden":u?"false":"true",name:"name",required:d,className:"form__input",placeholder:e,value:h}),b=xd("input",{id:"email",type:l?"text":"hidden","aria-hidden":l?"false":"true",name:"email",required:p,className:"form__input",placeholder:r,value:f}),S=xd("textarea",{id:"message",autoFocus:"true",rows:"5",name:"message",required:!0,className:"form__input form__input--textarea",placeholder:o}),w=xd("button",{type:"button",className:"btn btn--default","aria-label":a,onClick:t=>{m&&m(t)}},a),k=xd("form",{className:"form",onSubmit:function(t){if(t.preventDefault(),t.target instanceof HTMLFormElement)try{if(g){const e=new FormData(t.target),n={name:Ed(e,"name"),email:Ed(e,"email"),message:Ed(e,"message")};g(n)}}catch(t){}}},[v,u&&xd("label",{htmlFor:"name",className:"form__label"},[xd("span",{className:"form__label__text"},t,d&&xd("span",{className:"form__label__text--required"},` ${s}`)),_]),!u&&_,l&&xd("label",{htmlFor:"email",className:"form__label"},[xd("span",{className:"form__label__text"},n,p&&xd("span",{className:"form__label__text--required"},` ${s}`)),b]),!l&&b,xd("label",{htmlFor:"message",className:"form__label"},[xd("span",{className:"form__label__text"},i,xd("span",{className:"form__label__text--required"},` ${s}`)),S]),xd("div",{className:"btn-group"},[y,w])]);return{get el(){return k},showError:function(t){v.textContent=t,v.classList.remove("form__error-container--hidden"),v.setAttribute("aria-hidden","false")},hideError:function(){v.textContent="",v.classList.add("form__error-container--hidden"),v.setAttribute("aria-hidden","true")}}}({showEmail:r,showName:n,isEmailRequired:o,isNameRequired:i,defaultName:a,defaultEmail:c,onSubmit:d,onCancel:l,...p});return h=xd("dialog",{className:"dialog",open:!0,onClick:function(){f(),u&&u()}},xd("div",{className:"dialog__content",onClick:t=>{t.stopPropagation()}},xd("h2",{className:"dialog__header"},t,e&&xd("a",{className:"brand-link",target:"_blank",href:"https://sentry.io/welcome/",title:"Powered by Sentry",rel:"noopener noreferrer"},function({colorScheme:t}){const e=t=>cd.document.createElementNS(Td,t),n=Sd(e("svg"),{class:"sentry-logo",width:"32",height:"30",viewBox:"0 0 72 66",fill:"none"}),r=Sd(e("path"),{transform:"translate(11, 11)",d:"M29,2.26a4.67,4.67,0,0,0-8,0L14.42,13.53A32.21,32.21,0,0,1,32.17,40.19H27.55A27.68,27.68,0,0,0,12.09,17.47L6,28a15.92,15.92,0,0,1,9.23,12.17H4.62A.76.76,0,0,1,4,39.06l2.94-5a10.74,10.74,0,0,0-3.36-1.9l-2.91,5a4.54,4.54,0,0,0,1.69,6.24A4.66,4.66,0,0,0,4.62,44H19.15a19.4,19.4,0,0,0-8-17.31l2.31-4A23.87,23.87,0,0,1,23.76,44H36.07a35.88,35.88,0,0,0-16.41-31.8l4.67-8a.77.77,0,0,1,1.05-.27c.53.29,20.29,34.77,20.66,35.17a.76.76,0,0,1-.68,1.13H40.6q.09,1.91,0,3.81h4.78A4.59,4.59,0,0,0,50,39.43a4.49,4.49,0,0,0-.62-2.28Z"});n.append(r);const i=e("defs"),o=e("style");return o.textContent=`\n    path {\n      fill: ${"dark"===t?"#fff":"#362d59"};\n    }`,"system"===t&&(o.textContent+="\n    @media (prefers-color-scheme: dark) {\n      path: {\n        fill: '#fff';\n      }\n    }\n    "),i.append(o),n.append(i),{get el(){return n}}}({colorScheme:s}).el)),m)),{get el(){return h},showError:g,hideError:y,open:function(){h&&(h.open=!0)},close:f,checkIsOpen:function(){return h&&!0===h.open||!1}}}const Rd=16,Od=17,Md="http://www.w3.org/2000/svg";function Ad({shadow:t,options:{shouldCreateActor:e=!0,...n},attachTo:r}){let i,o,s=!1;async function a(e){if(!o)return;const r=[];n.isNameRequired&&!e.name&&r.push(n.nameLabel),n.isEmailRequired&&!e.email&&r.push(n.emailLabel),e.message||r.push(n.messageLabel),r.length>0?o.showError(`Please enter in the following required fields: ${r.join(", ")}`):await async function(t,e,n){if(t){t.hideError();try{return await yd({...e,source:md},n)}catch(e){vd&&N.error(e),t&&t.showError("There was a problem submitting feedback, please wait and try again.")}}}(o,e)?(h(),function(){if(t)try{const e=function({message:t,onRemove:e}){function n(){r&&(r.remove(),e&&e())}const r=xd("div",{className:"success-message",onClick:n},function(){const t=t=>cd.document.createElementNS(Md,t),e=Sd(t("svg"),{class:"success-icon",width:`${Rd}`,height:`${Od}`,viewBox:`0 0 ${Rd} ${Od}`,fill:"none"}),n=Sd(t("g"),{clipPath:"url(#clip0_57_156)"}),r=Sd(t("path"),{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M3.55544 15.1518C4.87103 16.0308 6.41775 16.5 8 16.5C10.1217 16.5 12.1566 15.6571 13.6569 14.1569C15.1571 12.6566 16 10.6217 16 8.5C16 6.91775 15.5308 5.37103 14.6518 4.05544C13.7727 2.73985 12.5233 1.71447 11.0615 1.10897C9.59966 0.503466 7.99113 0.34504 6.43928 0.653721C4.88743 0.962403 3.46197 1.72433 2.34315 2.84315C1.22433 3.96197 0.462403 5.38743 0.153721 6.93928C-0.15496 8.49113 0.00346625 10.0997 0.608967 11.5615C1.21447 13.0233 2.23985 14.2727 3.55544 15.1518ZM4.40546 3.1204C5.46945 2.40946 6.72036 2.03 8 2.03C9.71595 2.03 11.3616 2.71166 12.575 3.92502C13.7883 5.13838 14.47 6.78405 14.47 8.5C14.47 9.77965 14.0905 11.0306 13.3796 12.0945C12.6687 13.1585 11.6582 13.9878 10.476 14.4775C9.29373 14.9672 7.99283 15.0953 6.73777 14.8457C5.48271 14.596 4.32987 13.9798 3.42502 13.075C2.52018 12.1701 1.90397 11.0173 1.65432 9.76224C1.40468 8.50718 1.5328 7.20628 2.0225 6.02404C2.5122 4.8418 3.34148 3.83133 4.40546 3.1204Z"}),i=Sd(t("path"),{d:"M6.68775 12.4297C6.78586 12.4745 6.89218 12.4984 7 12.5C7.11275 12.4955 7.22315 12.4664 7.32337 12.4145C7.4236 12.3627 7.51121 12.2894 7.58 12.2L12 5.63999C12.0848 5.47724 12.1071 5.28902 12.0625 5.11098C12.0178 4.93294 11.9095 4.77744 11.7579 4.67392C11.6064 4.57041 11.4221 4.52608 11.24 4.54931C11.0579 4.57254 10.8907 4.66173 10.77 4.79999L6.88 10.57L5.13 8.56999C5.06508 8.49566 4.98613 8.43488 4.89768 8.39111C4.80922 8.34735 4.713 8.32148 4.61453 8.31498C4.51605 8.30847 4.41727 8.32147 4.32382 8.35322C4.23038 8.38497 4.14413 8.43484 4.07 8.49999C3.92511 8.63217 3.83692 8.81523 3.82387 9.01092C3.81083 9.2066 3.87393 9.39976 4 9.54999L6.43 12.24C6.50187 12.3204 6.58964 12.385 6.68775 12.4297Z"});e.appendChild(n).append(i,r);const o=t("defs"),s=Sd(t("clipPath"),{id:"clip0_57_156"}),a=Sd(t("rect"),{width:`${Rd}`,height:`${Rd}`,fill:"white",transform:"translate(0 0.5)"});return s.appendChild(a),o.appendChild(s),e.appendChild(o).appendChild(s).appendChild(a),{get el(){return e}}}().el,t);return{el:r,remove:n}}({message:n.successMessageText,onRemove:()=>{r&&clearTimeout(r),u()}});if(!e.el)throw new Error("Unable to show success message");t.appendChild(e.el);const r=setTimeout((()=>{e&&e.remove()}),5e3)}catch(t){N.error(t)}}(),n.onSubmitSuccess&&n.onSubmitSuccess()):n.onSubmitError&&n.onSubmitError()}function c(){const t=Ae(),e=t&&t.getIntegrationByName&&t.getIntegrationByName("Replay");e&&e.flush().catch((t=>{vd&&N.error(t)}))}function u(){i&&i.show()}function l(){i&&i.hide()}function d(){try{if(o)return o.open(),s=!0,n.onFormOpen&&n.onFormOpen(),void c();const e=n.useSentryUser,r=Ne(),i=r&&r.getUser();if(o=Id({colorScheme:n.colorScheme,showBranding:n.showBranding,showName:n.showName||n.isNameRequired,showEmail:n.showEmail||n.isEmailRequired,isNameRequired:n.isNameRequired,isEmailRequired:n.isEmailRequired,formTitle:n.formTitle,cancelButtonLabel:n.cancelButtonLabel,submitButtonLabel:n.submitButtonLabel,emailLabel:n.emailLabel,emailPlaceholder:n.emailPlaceholder,messageLabel:n.messageLabel,messagePlaceholder:n.messagePlaceholder,nameLabel:n.nameLabel,namePlaceholder:n.namePlaceholder,isRequiredLabel:n.isRequiredLabel,defaultName:e&&i&&i[e.name]||"",defaultEmail:e&&i&&i[e.email]||"",onClosed:()=>{u(),s=!1,n.onFormClose&&n.onFormClose()},onCancel:()=>{p(),u()},onSubmit:a}),!o.el)throw new Error("Unable to open Feedback dialog");t.appendChild(o.el),l(),n.onFormOpen&&n.onFormOpen(),c()}catch(t){N.error(t)}}function p(){o&&(o.close(),s=!1,n.onFormClose&&n.onFormClose())}function h(){if(o){p();const t=o.el;t&&t.remove(),o=void 0}}function f(){s||d(),l()}return r?r.addEventListener("click",f):e&&(i=function({buttonLabel:t,onClick:e}){const n=xd("button",{type:"button",className:"widget__actor","aria-label":t,"aria-hidden":"false"},function(){const t=t=>cd.document.createElementNS(kd,t),e=Sd(t("svg"),{class:"feedback-icon",width:`${wd}`,height:`${wd}`,viewBox:`0 0 ${wd} ${wd}`,fill:"none"}),n=Sd(t("g"),{clipPath:"url(#clip0_57_80)"}),r=Sd(t("path"),{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M15.6622 15H12.3997C12.2129 14.9959 12.031 14.9396 11.8747 14.8375L8.04965 12.2H7.49956V19.1C7.4875 19.3348 7.3888 19.5568 7.22256 19.723C7.05632 19.8892 6.83435 19.9879 6.59956 20H2.04956C1.80193 19.9968 1.56535 19.8969 1.39023 19.7218C1.21511 19.5467 1.1153 19.3101 1.11206 19.0625V12.2H0.949652C0.824431 12.2017 0.700142 12.1783 0.584123 12.1311C0.468104 12.084 0.362708 12.014 0.274155 11.9255C0.185602 11.8369 0.115689 11.7315 0.0685419 11.6155C0.0213952 11.4995 -0.00202913 11.3752 -0.00034808 11.25V3.75C-0.00900498 3.62067 0.0092504 3.49095 0.0532651 3.36904C0.0972798 3.24712 0.166097 3.13566 0.255372 3.04168C0.344646 2.94771 0.452437 2.87327 0.571937 2.82307C0.691437 2.77286 0.82005 2.74798 0.949652 2.75H8.04965L11.8747 0.1625C12.031 0.0603649 12.2129 0.00407221 12.3997 0H15.6622C15.9098 0.00323746 16.1464 0.103049 16.3215 0.278167C16.4966 0.453286 16.5964 0.689866 16.5997 0.9375V3.25269C17.3969 3.42959 18.1345 3.83026 18.7211 4.41679C19.5322 5.22788 19.9878 6.32796 19.9878 7.47502C19.9878 8.62209 19.5322 9.72217 18.7211 10.5333C18.1345 11.1198 17.3969 11.5205 16.5997 11.6974V14.0125C16.6047 14.1393 16.5842 14.2659 16.5395 14.3847C16.4948 14.5035 16.4268 14.6121 16.3394 14.7042C16.252 14.7962 16.147 14.8698 16.0307 14.9206C15.9144 14.9714 15.7891 14.9984 15.6622 15ZM1.89695 10.325H1.88715V4.625H8.33715C8.52423 4.62301 8.70666 4.56654 8.86215 4.4625L12.6872 1.875H14.7247V13.125H12.6872L8.86215 10.4875C8.70666 10.3835 8.52423 10.327 8.33715 10.325H2.20217C2.15205 10.3167 2.10102 10.3125 2.04956 10.3125C1.9981 10.3125 1.94708 10.3167 1.89695 10.325ZM2.98706 12.2V18.1625H5.66206V12.2H2.98706ZM16.5997 9.93612V5.01393C16.6536 5.02355 16.7072 5.03495 16.7605 5.04814C17.1202 5.13709 17.4556 5.30487 17.7425 5.53934C18.0293 5.77381 18.2605 6.06912 18.4192 6.40389C18.578 6.73866 18.6603 7.10452 18.6603 7.47502C18.6603 7.84552 18.578 8.21139 18.4192 8.54616C18.2605 8.88093 18.0293 9.17624 17.7425 9.41071C17.4556 9.64518 17.1202 9.81296 16.7605 9.90191C16.7072 9.91509 16.6536 9.9265 16.5997 9.93612Z"});e.appendChild(n).appendChild(r);const i=t("defs"),o=Sd(t("clipPath"),{id:"clip0_57_80"}),s=Sd(t("rect"),{width:`${wd}`,height:`${wd}`,fill:"white"});return o.appendChild(s),i.appendChild(o),e.appendChild(i).appendChild(o).appendChild(s),{get el(){return e}}}().el,t?xd("span",{className:"widget__actor__text"},t):null);return n.addEventListener("click",(function(t){e&&e(t)})),{get el(){return n},show:()=>{n.classList.remove("widget__actor--hidden"),n.setAttribute("aria-hidden","false")},hide:()=>{n.classList.add("widget__actor--hidden"),n.setAttribute("aria-hidden","true")}}}({buttonLabel:n.buttonLabel,onClick:f}),i.el&&t.appendChild(i.el)),{get actor(){return i},get dialog(){return o},showActor:u,hideActor:l,removeActor:function(){i&&i.el&&i.el.remove()},openDialog:d,closeDialog:p,removeDialog:h}}const Dd=cd.document,Nd=t=>new Ld(t);class Ld{static __initStatic(){this.id="Feedback"}constructor({autoInject:t=!0,id:e="sentry-feedback",isEmailRequired:n=!1,isNameRequired:r=!1,showBranding:i=!0,showEmail:o=!0,showName:s=!0,useSentryUser:a={email:"email",name:"username"},themeDark:c,themeLight:u,colorScheme:l="system",buttonLabel:d="Report a Bug",cancelButtonLabel:p="Cancel",submitButtonLabel:h="Send Bug Report",formTitle:f="Report a Bug",emailPlaceholder:m="<EMAIL>",emailLabel:g="Email",messagePlaceholder:y="What's the bug? What did you expect?",messageLabel:v="Description",namePlaceholder:_="Your Name",nameLabel:b="Name",isRequiredLabel:S="(required)",successMessageText:w="Thank you for your report!",onFormClose:k,onFormOpen:x,onSubmitError:C,onSubmitSuccess:E}={}){this.name=Ld.id,this._host=null,this._shadow=null,this._widget=null,this._widgets=new Set,this._hasInsertedActorStyles=!1,this.options={autoInject:t,showBranding:i,id:e,isEmailRequired:n,isNameRequired:r,showEmail:o,showName:s,useSentryUser:a,colorScheme:l,themeDark:{...fd,...c},themeLight:{...hd,...u},buttonLabel:d,cancelButtonLabel:p,submitButtonLabel:h,formTitle:f,emailLabel:g,emailPlaceholder:m,messageLabel:v,messagePlaceholder:y,nameLabel:b,namePlaceholder:_,isRequiredLabel:S,successMessageText:w,onFormClose:k,onFormOpen:x,onSubmitError:C,onSubmitSuccess:E}}setupOnce(){if(ls())try{this._cleanupWidgetIfExists();const{autoInject:t}=this.options;if(!t)return;this._createWidget(this.options)}catch(t){vd&&N.error(t)}}openDialog(){this._widget||this._createWidget({...this.options,shouldCreateActor:!1}),this._widget&&this._widget.openDialog()}closeDialog(){this._widget&&this._widget.closeDialog()}attachTo(t,e){try{const n=_d(this.options,e||{});return this._ensureShadowHost(n,(({shadow:e})=>{const r="string"==typeof t?Dd.querySelector(t):"function"==typeof t.addEventListener?t:null;if(!r)return vd&&N.error("[Feedback] Unable to attach to target element"),null;const i=Ad({shadow:e,options:n,attachTo:r});return this._widgets.add(i),this._widget||(this._widget=i),i}))}catch(t){return vd&&N.error(t),null}}createWidget(t){try{return this._createWidget(_d(this.options,t||{}))}catch(t){return vd&&N.error(t),null}}removeWidget(t){if(!t)return!1;try{if(this._widgets.has(t))return t.removeActor(),t.removeDialog(),this._widgets.delete(t),this._widget===t&&(this._widget=null),!0}catch(t){vd&&N.error(t)}return!1}getWidget(){return this._widget}remove(){this._host&&this._host.remove(),this._initialize()}_initialize(){this._host=null,this._shadow=null,this._widget=null,this._widgets=new Set,this._hasInsertedActorStyles=!1}_cleanupWidgetIfExists(){this._host&&this.remove();const t=Dd.querySelector(`#${this.options.id}`);t&&t.remove()}_createWidget(t){return this._ensureShadowHost(t,(({shadow:e})=>{const n=Ad({shadow:e,options:t});return!this._hasInsertedActorStyles&&n.actor&&(e.appendChild(function(t){const e=t.createElement("style");return e.textContent="\n.widget__actor {\n  position: fixed;\n  left: var(--left);\n  right: var(--right);\n  bottom: var(--bottom);\n  top: var(--top);\n  z-index: var(--z-index);\n\n  line-height: 16px;\n\n  display: flex;\n  align-items: center;\n  gap: 8px;\n\n  border-radius: var(--border-radius);\n  cursor: pointer;\n  font-family: inherit;\n  font-size: var(--font-size);\n  font-weight: 600;\n  padding: 16px;\n  text-decoration: none;\n  z-index: 9000;\n\n  color: var(--foreground);\n  background-color: var(--background);\n  border: var(--border);\n  box-shadow: var(--box-shadow);\n  opacity: 1;\n  transition: opacity 0.1s ease-in-out;\n}\n\n.widget__actor:hover {\n  background-color: var(--background-hover);\n}\n\n.widget__actor svg {\n  width: 16px;\n  height: 16px;\n}\n\n.widget__actor--hidden {\n  opacity: 0;\n  pointer-events: none;\n  visibility: hidden;\n}\n\n.widget__actor__text {\n}\n\n@media (max-width: 600px) {\n  .widget__actor__text {\n    display: none;\n  }\n}\n\n.feedback-icon path {\n  fill: var(--foreground);\n}\n",e}(Dd)),this._hasInsertedActorStyles=!0),this._widgets.add(n),this._widget||(this._widget=n),n}))}_ensureShadowHost(t,e){let n=!1;if(!this._shadow||!this._host){const{id:e,colorScheme:r,themeLight:i,themeDark:o}=t,{shadow:s,host:a}=function({id:t,colorScheme:e,themeDark:n,themeLight:r}){try{const i=cd.document,o=i.createElement("div");o.id=t;const s=o.attachShadow({mode:"open"});return s.appendChild(function(t,e,n){const r=t.createElement("style");return r.textContent=`\n:host {\n  --bottom: 1rem;\n  --right: 1rem;\n  --top: auto;\n  --left: auto;\n  --z-index: 100000;\n  --font-family: ${n.light.fontFamily};\n  --font-size: ${n.light.fontSize};\n\n  position: fixed;\n  left: var(--left);\n  right: var(--right);\n  bottom: var(--bottom);\n  top: var(--top);\n  z-index: var(--z-index);\n\n  font-family: var(--font-family);\n  font-size: var(--font-size);\n\n  ${bd("dark"===e?n.dark:n.light)}\n}\n\n${"system"===e?`\n@media (prefers-color-scheme: dark) {\n  :host {\n    ${bd(n.dark)}\n  }\n}`:""}\n}`,r}(i,e,{dark:n,light:r})),s.appendChild(function(t){const e=t.createElement("style");return e.textContent="\n.dialog {\n  line-height: 25px;\n  background-color: rgba(0, 0, 0, 0.05);\n  border: none;\n  position: fixed;\n  inset: 0;\n  z-index: 10000;\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 1;\n  transition: opacity 0.2s ease-in-out;\n}\n\n.dialog:not([open]) {\n  opacity: 0;\n  pointer-events: none;\n  visibility: hidden;\n}\n.dialog:not([open]) .dialog__content {\n  transform: translate(0, -16px) scale(0.98);\n}\n\n.dialog__content {\n  position: fixed;\n  left: var(--left);\n  right: var(--right);\n  bottom: var(--bottom);\n  top: var(--top);\n\n  border: var(--border);\n  border-radius: var(--form-border-radius);\n  background-color: var(--background);\n  color: var(--foreground);\n\n  width: 320px;\n  max-width: 100%;\n  max-height: calc(100% - 2rem);\n  display: flex;\n  flex-direction: column;\n  box-shadow: var(--box-shadow);\n  transition: transform 0.2s ease-in-out;\n  transform: translate(0, 0) scale(1);\n}\n\n.dialog__header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 20px;\n  font-weight: 600;\n  padding: 24px 24px 0 24px;\n  margin: 0;\n  margin-bottom: 16px;\n}\n\n.brand-link {\n  display: inline-flex;\n}\n\n.error {\n  color: var(--error);\n  margin-bottom: 16px;\n}\n\n.form {\n  display: grid;\n  overflow: auto;\n  flex-direction: column;\n  gap: 16px;\n  padding: 0 24px 24px;\n}\n\n.form__error-container {\n  color: var(--error);\n}\n\n.form__error-container--hidden {\n  display: none;\n}\n\n.form__label {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  margin: 0px;\n}\n\n.form__label__text {\n  display: grid;\n  gap: 4px;\n  align-items: center;\n  grid-auto-flow: column;\n  grid-auto-columns: max-content;\n}\n\n.form__label__text--required {\n  font-size: 0.85em;\n}\n\n.form__input {\n  line-height: inherit;\n  background-color: var(--input-background);\n  box-sizing: border-box;\n  border: var(--input-border);\n  border-radius: var(--form-content-border-radius);\n  color: var(--input-foreground);\n  font-family: inherit;\n  font-size: var(--font-size);\n  font-weight: 500;\n  padding: 6px 12px;\n}\n\n.form__input::placeholder {\n  color: var(--input-foreground);\n  opacity: 0.65;\n}\n\n.form__input:focus-visible {\n  outline: 1px auto var(--input-outline-focus);\n}\n\n.form__input--textarea {\n  font-family: inherit;\n  resize: vertical;\n}\n\n.btn-group {\n  display: grid;\n  gap: 8px;\n  margin-top: 8px;\n}\n\n.btn {\n  line-height: inherit;\n  border: var(--cancel-border);\n  border-radius: var(--form-content-border-radius);\n  cursor: pointer;\n  font-family: inherit;\n  font-size: var(--font-size);\n  font-weight: 600;\n  padding: 6px 16px;\n}\n.btn[disabled] {\n  opacity: 0.6;\n  pointer-events: none;\n}\n\n.btn--primary {\n  background-color: var(--submit-background);\n  border-color: var(--submit-border);\n  color: var(--submit-foreground);\n}\n.btn--primary:hover {\n  background-color: var(--submit-background-hover);\n  color: var(--submit-foreground-hover);\n}\n.btn--primary:focus-visible {\n  outline: 1px auto var(--submit-outline-focus);\n}\n\n.btn--default {\n  background-color: var(--cancel-background);\n  color: var(--cancel-foreground);\n  font-weight: 500;\n}\n.btn--default:hover {\n  background-color: var(--cancel-background-hover);\n  color: var(--cancel-foreground-hover);\n}\n.btn--default:focus-visible {\n  outline: 1px auto var(--cancel-outline-focus);\n}\n\n.success-message {\n  background-color: var(--background);\n  border: var(--border);\n  border-radius: var(--border-radius);\n  box-shadow: var(--box-shadow);\n  font-weight: 600;\n  color: var(--success);\n  padding: 12px 24px;\n  line-height: 25px;\n  display: grid;\n  align-items: center;\n  grid-auto-flow: column;\n  gap: 6px;\n  cursor: default;\n}\n\n.success-icon path {\n  fill: var(--success);\n}\n",e}(i)),{shadow:s,host:o}}catch(t){throw N.warn("[Feedback] Browser does not support shadow DOM API"),new Error("Browser does not support shadow DOM API.")}}({id:e,colorScheme:r,themeLight:i,themeDark:o});this._shadow=s,this._host=a,n=!0}this._host.dataset.sentryFeedbackColorscheme=t.colorScheme;const r=e({shadow:this._shadow,host:this._host});return n&&Dd.body.appendChild(this._host),r}}Ld.__initStatic();const Pd="CaptureConsole",Fd=(t={})=>{const e=t.levels||M;return{name:Pd,setupOnce(){},setup(t){"console"in b&&to((({args:n,level:r})=>{Ae()===t&&e.includes(r)&&function(t,e){const n={level:bo(e),extra:{arguments:t}};Ce((r=>{if(r.addEventProcessor((t=>(t.logger="console",tt(t,{handled:!1,type:"console"}),t))),"assert"===e&&!1===t[0]){const e=`Assertion failed: ${F(t.slice(1)," ")||"console.assert"}`;return r.setExtra("arguments",t.slice(1)),void me(e,n)}const i=t.find((t=>t instanceof Error));"error"===e&&i?fe(i,n):me(F(t," "),n)}))}(n,r)}))}}};ze(Pd,Fd);const jd=b,$d="ContextLines",Hd=(t={})=>{const e=null!=t.frameContextLines?t.frameContextLines:7;return{name:$d,setupOnce(){},processEvent:t=>function(t,e){const n=jd.document,r=jd.location&&jd.location.href.split(/[\?#]/,1)[0];if(!n||!r)return t;const i=t.exception&&t.exception.values;if(!i||!i.length)return t;const o=n.documentElement.innerHTML;if(!o)return t;const s=["<!DOCTYPE html>","<html>",...o.split("\n"),"</html>"];return i.forEach((t=>{const n=t.stacktrace;n&&n.frames&&(n.frames=n.frames.map((t=>function(t,e,n,r){return t.filename===n&&t.lineno&&e.length?(function(t,e,n=5){if(void 0===e.lineno)return;const r=t.length,i=Math.max(Math.min(r-1,e.lineno-1),0);e.pre_context=t.slice(Math.max(0,i-n),i).map((t=>P(t,0))),e.context_line=P(t[Math.min(r-1,i)],e.colno||0),e.post_context=t.slice(Math.min(i+1,r),i+1+n).map((t=>P(t,0)))}(e,t,r),t):t}(t,s,r,e))))})),t}(t,e)}};ze($d,Hd);const Bd="Debug",Ud=(t={})=>{const e={debugger:!1,stringify:!1,...t};return{name:Bd,setupOnce(){},setup(t){t.on&&t.on("beforeSendEvent",((t,n)=>{e.debugger,D((()=>{e.stringify?(console.log(JSON.stringify(t,null,2)),n&&Object.keys(n).length&&console.log(JSON.stringify(n,null,2))):(console.log(t),n&&Object.keys(n).length&&console.log(n))}))}))}}},qd=(ze(Bd,Ud),"undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__),zd="Dedupe",Wd=()=>{let t;return{name:zd,setupOnce(){},processEvent(e){if(e.type)return e;try{if(function(t,e){return!!e&&(!!function(t,e){const n=t.message,r=e.message;return!(!n&&!r)&&(!(n&&!r||!n&&r)&&(n===r&&(!!Gd(t,e)&&!!Yd(t,e))))}(t,e)||!!function(t,e){const n=Jd(e),r=Jd(t);return!(!n||!r)&&(n.type===r.type&&n.value===r.value&&(!!Gd(t,e)&&!!Yd(t,e)))}(t,e))}(e,t))return qd&&N.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(t){}return t=e}}};function Yd(t,e){let n=Vd(t),r=Vd(e);if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;if(r.length!==n.length)return!1;for(let t=0;t<r.length;t++){const e=r[t],i=n[t];if(e.filename!==i.filename||e.lineno!==i.lineno||e.colno!==i.colno||e.function!==i.function)return!1}return!0}function Gd(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return!(n.join("")!==r.join(""))}catch(t){return!1}}function Jd(t){return t.exception&&t.exception.values&&t.exception.values[0]}function Vd(t){const e=t.exception;if(e)try{return e.values[0].stacktrace.frames}catch(t){return}}ze(zd,Wd);const Kd="ExtraErrorData",Xd=(t={})=>{const e=t.depth||3,n=t.captureErrorCause||!1;return{name:Kd,setupOnce(){},processEvent:(t,r)=>function(t,e={},n,r){if(!e.originalException||!s(e.originalException))return t;const i=e.originalException.name||e.originalException.constructor.name,o=function(t,e){try{const n=["name","message","stack","line","column","fileName","lineNumber","columnNumber","toJSON"],r={};for(const e of Object.keys(t)){if(-1!==n.indexOf(e))continue;const i=t[e];r[e]=s(i)?i.toString():i}if(e&&void 0!==t.cause&&(r.cause=s(t.cause)?t.cause.toString():t.cause),"function"==typeof t.toJSON){const e=t.toJSON();for(const t of Object.keys(e)){const n=e[t];r[t]=s(n)?n.toString():n}}return r}catch(t){qd&&N.error("Unable to extract extra data from the Error object:",t)}return null}(e.originalException,r);if(o){const e={...t.contexts},r=ce(o,n);return h(r)&&(H(r,"__sentry_skip_normalization__",!0),e[i]=r),{...t,contexts:e}}return t}(t,r,e,n)}};ze(Kd,Xd);const Qd="HttpClient",Zd=(t={})=>{const e={failedRequestStatusCodes:[[500,599]],failedRequestTargets:[/.*/],...t};return{name:Qd,setupOnce(){},setup(t){!function(t,e){Ti()&&mo((n=>{if(Ae()!==t)return;const{response:r,args:i}=n,[o,s]=i;r&&function(t,e,n,r){if(np(t,n.status,n.url)){const t=function(t,e){return!e&&t instanceof Request||t instanceof Request&&t.bodyUsed?t:new Request(t,e)}(e,r);let i,o,s,a;ip()&&([{headers:i,cookies:s},{headers:o,cookies:a}]=[{cookieHeader:"Cookie",obj:t},{cookieHeader:"Set-Cookie",obj:n}].map((({cookieHeader:t,obj:e})=>{const n=function(t){const e={};return t.forEach(((t,n)=>{e[n]=t})),e}(e.headers);let r;try{const e=n[t]||n[t.toLowerCase()]||void 0;e&&(r=ep(e))}catch(e){qd&&N.log(`Could not extract cookies from header ${t}`)}return{headers:n,cookies:r}}))),ge(rp({url:t.url,method:t.method,status:n.status,requestHeaders:i,responseHeaders:o,requestCookies:s,responseCookies:a}))}}(e,o,r,s)}))}(t,e),function(t,e){"XMLHttpRequest"in b&&ho((n=>{if(Ae()!==t)return;const r=n.xhr,i=r[po];if(!i)return;const{method:o,request_headers:s}=i;try{!function(t,e,n,r){if(np(t,e.status,e.responseURL)){let t,i,o;if(ip()){try{const t=e.getResponseHeader("Set-Cookie")||e.getResponseHeader("set-cookie")||void 0;t&&(i=ep(t))}catch(t){qd&&N.log("Could not extract cookies from response headers")}try{o=function(t){const e=t.getAllResponseHeaders();return e?e.split("\r\n").reduce(((t,e)=>{const[n,r]=e.split(": ");return t[n]=r,t}),{}):{}}(e)}catch(t){qd&&N.log("Could not extract headers from response")}t=r}ge(rp({url:e.responseURL,method:n,status:e.status,requestHeaders:t,responseHeaders:o,responseCookies:i}))}}(e,r,o,s)}catch(t){qd&&N.warn("Error while extracting response event form XHR response",t)}}))}(t,e)}}};function tp(t){if(t){const e=t["Content-Length"]||t["content-length"];if(e)return parseInt(e,10)}}function ep(t){return t.split("; ").reduce(((t,e)=>{const[n,r]=e.split("=");return t[n]=r,t}),{})}function np(t,e,n){return function(t,e){return t.some((t=>"number"==typeof t?t===e:e>=t[0]&&e<=t[1]))}(t.failedRequestStatusCodes,e)&&(r=t.failedRequestTargets,i=n,r.some((t=>"string"==typeof t?i.includes(t):t.test(i))))&&!as(n,Ae());var r,i}function rp(t){const e=`HTTP Client Error with status code: ${t.status}`,n={message:e,exception:{values:[{type:"Error",value:e}]},request:{url:t.url,method:t.method,headers:t.requestHeaders,cookies:t.requestCookies},contexts:{response:{status_code:t.status,headers:t.responseHeaders,cookies:t.responseCookies,body_size:tp(t.responseHeaders)}}};return tt(n,{type:"http.client",handled:!1}),n}function ip(){const t=Ae();return!!t&&Boolean(t.getOptions().sendDefaultPii)}ze(Qd,Zd);const op=b,sp="ReportingObserver",ap=new WeakMap,cp=(t={})=>{const e=t.types||["crash","deprecation","intervention"];function n(t){if(ap.has(Ae()))for(const e of t)Ce((t=>{t.setExtra("url",e.url);const n=`ReportingObserver [${e.type}]`;let r="No details available";if(e.body){const n={};for(const t in e.body)n[t]=e.body[t];if(t.setExtra("body",n),"crash"===e.type){const t=e.body;r=[t.crashId||"",t.reason||""].join(" ").trim()||r}else r=e.body.message||r}me(`${n}: ${r}`)}))}return{name:sp,setupOnce(){"ReportingObserver"in xi&&new op.ReportingObserver(n,{buffered:!0,types:e}).observe()},setup(t){ap.set(t,!0)}}};ze(sp,cp);const up=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function lp(...t){let e="",n=!1;for(let r=t.length-1;r>=-1&&!n;r--){const i=r>=0?t[r]:"/";i&&(e=`${i}/${e}`,n="/"===i.charAt(0))}return e=function(t,e){let n=0;for(let e=t.length-1;e>=0;e--){const r=t[e];"."===r?t.splice(e,1):".."===r?(t.splice(e,1),n++):n&&(t.splice(e,1),n--)}if(e)for(;n--;n)t.unshift("..");return t}(e.split("/").filter((t=>!!t)),!n).join("/"),(n?"/":"")+e||"."}function dp(t){let e=0;for(;e<t.length&&""===t[e];e++);let n=t.length-1;for(;n>=0&&""===t[n];n--);return e>n?[]:t.slice(e,n-e+1)}const pp="RewriteFrames",hp=(t={})=>{const e=t.root,n=t.prefix||"app:///",r=t.iteratee||(t=>{if(!t.filename)return t;const r=/^[a-zA-Z]:\\/.test(t.filename)||t.filename.includes("\\")&&!t.filename.includes("/"),i=/^\//.test(t.filename);if(r||i){const i=r?t.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):t.filename,o=e?function(t,e){t=lp(t).slice(1),e=lp(e).slice(1);const n=dp(t.split("/")),r=dp(e.split("/")),i=Math.min(n.length,r.length);let o=i;for(let t=0;t<i;t++)if(n[t]!==r[t]){o=t;break}let s=[];for(let t=o;t<n.length;t++)s.push("..");return s=s.concat(r.slice(o)),s.join("/")}(e,i):function(t,e){let n=function(t){const e=t.length>1024?`<truncated>${t.slice(-1024)}`:t,n=up.exec(e);return n?n.slice(1):[]}(t)[2];return n}(i);t.filename=`${n}${o}`}return t});return{name:pp,setupOnce(){},processEvent(t){let e=t;return t.exception&&Array.isArray(t.exception.values)&&(e=function(t){try{return{...t,exception:{...t.exception,values:t.exception.values.map((t=>{return{...t,...t.stacktrace&&{stacktrace:(e=t.stacktrace,{...e,frames:e&&e.frames&&e.frames.map((t=>r(t)))})}};var e}))}}}catch(e){return t}}(e)),e}}},fp=(ze(pp,hp),"SessionTiming"),mp=()=>{const t=Date.now();return{name:fp,setupOnce(){},processEvent(e){const n=Date.now();return{...e,extra:{...e.extra,"session:start":t,"session:duration":n-t,"session:end":n}}}}};function gp(){hs.document?hs.document.addEventListener("visibilitychange",(()=>{const t=sr();if(hs.document.hidden&&t){const e="cancelled",{op:n,status:r}=Lt(t);ds&&N.log(`[Tracing] Transaction: ${e} -> since tab moved to the background, op: ${n}`),r||t.setStatus(e),t.setTag("visibilitychange","document.hidden"),t.end()}})):ds&&N.warn("[Tracing] Could not set up background tab detection due to lack of global document")}function yp(t){return[{type:"span"},t]}function vp(t){return"number"==typeof t&&isFinite(t)}function _p(t,{startTimestamp:e,...n}){return e&&t.startTimestamp>e&&(t.startTimestamp=e),t.startChild({startTimestamp:e,...n})}ze(fp,mp);const bp=2147483647;function Sp(t){return t/1e3}function wp(){return hs&&hs.addEventListener&&hs.performance}let kp,xp,Cp=0,Ep={};function Tp(){const t=wp();if(t&&at){t.mark&&hs.performance.mark("sentry-tracing-init");const e=Zs("fid",(({metric:t})=>{const e=t.entries[t.entries.length-1];if(!e)return;const n=Sp(at),r=Sp(e.startTime);ds&&N.log("[Measurements] Adding FID"),Ep.fid={value:t.value,unit:"millisecond"},Ep["mark.fid"]={value:n+r,unit:"second"}}),Vs,Bs),n=function(t,e=!1){return Zs("cls",t,Js,Hs,e)}((({metric:t})=>{const e=t.entries[t.entries.length-1];e&&(ds&&N.log("[Measurements] Adding CLS"),Ep.cls={value:t.value,unit:""},xp=e)}),!0),r=Ws((({metric:t})=>{const e=t.entries[t.entries.length-1];e&&(ds&&N.log("[Measurements] Adding LCP"),Ep.lcp={value:t.value,unit:"millisecond"},kp=e)}),!0),i=Zs("ttfb",(({metric:t})=>{t.entries[t.entries.length-1]&&(ds&&N.log("[Measurements] Adding TTFB"),Ep.ttfb={value:t.value,unit:"millisecond"})}),Xs,qs);return()=>{e(),n(),r(),i()}}return()=>{}}function Ip(){Ys("longtask",(({entries:t})=>{for(const e of t){const t=sr();if(!t)return;const n=Sp(at+e.startTime),r=Sp(e.duration);t.startChild({description:"Main UI thread blocked",op:"ui.long-task",origin:"auto.ui.browser.metrics",startTimestamp:n,endTimestamp:n+r})}}))}function Rp(){Ys("event",(({entries:t})=>{for(const e of t){const t=sr();if(!t)return;if("click"===e.name){const n=Sp(at+e.startTime),r=Sp(e.duration),i={description:C(e.target),op:`ui.interaction.${e.name}`,origin:"auto.ui.browser.metrics",startTimestamp:n,endTimestamp:n+r},o=R(e.target);o&&(i.attributes={"ui.component_name":o}),t.startChild(i)}}}))}function Op(t,e){if(wp()&&at){const n=function(t,e){return n=({metric:n})=>{if(void 0===n.value)return;const r=n.entries.find((t=>t.duration===n.value&&void 0!==Mp[t.name])),i=Ae();if(!r||!i)return;const o=Mp[r.name],s=i.getOptions(),a=Sp(at+r.startTime),c=Sp(n.value),u=void 0!==r.interactionId?t[r.interactionId]:void 0;if(void 0===u)return;const{routeName:l,parentContext:d,activeTransaction:p,user:h,replayId:f}=u,m=void 0!==h?h.email||h.id||h.ip_address:void 0,g=void 0!==p?p.getProfileId():void 0,y=new Or({startTimestamp:a,endTimestamp:a+c,op:`ui.interaction.${o}`,name:C(r.target),attributes:{release:s.release,environment:s.environment,transaction:l,...void 0!==m&&""!==m?{user:m}:{},...void 0!==g?{profile_id:g}:{},...void 0!==f?{replay_id:f}:{}},exclusiveTime:n.value,measurements:{inp:{value:n.value,unit:"millisecond"}}}),v=function(t,e,n){if(!dr(e))return!1;let r;return r=void 0!==t&&"function"==typeof e.tracesSampler?e.tracesSampler({transactionContext:t,name:t.name,parentSampled:t.parentSampled,attributes:{...t.data,...t.attributes},location:hs.location}):void 0!==t&&void 0!==t.sampled?t.sampled:void 0!==e.tracesSampleRate?e.tracesSampleRate:1,Fr(r)?!0===r?n:!1===r?0:r*n:(ds&&N.warn("[Tracing] Discarding interaction span because of invalid sample rate."),!1)}(d,s,e);if(v&&Math.random()<v){const t=y?function(t,e){const n={sent_at:(new Date).toISOString()};return e&&(n.dsn=pn(e)),gn(n,t.map(yp))}([y],i.getDsn()):void 0,e=i&&i.getTransport();e&&t&&e.send(t).then(null,(t=>{ds&&N.error("Error while sending interaction:",t)}))}},Zs("inp",n,Qs,zs);var n}(t,e);return()=>{n()}}return()=>{}}const Mp={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function Ap(t){const e=wp();if(!e||!hs.performance.getEntries||!at)return;ds&&N.log("[Tracing] Adding & adjusting spans using Performance API");const n=Sp(at),r=e.getEntries(),{op:i,start_timestamp:o}=Lt(t);if(r.slice(Cp).forEach((e=>{const r=Sp(e.startTime),i=Sp(e.duration);if(!("navigation"===t.op&&o&&n+r<o))switch(e.entryType){case"navigation":!function(t,e,n){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach((r=>{Dp(t,e,r,n)})),Dp(t,e,"secureConnection",n,"TLS/SSL","connectEnd"),Dp(t,e,"fetch",n,"cache","domainLookupStart"),Dp(t,e,"domainLookup",n,"DNS"),function(t,e,n){e.responseEnd&&(_p(t,{op:"browser",origin:"auto.browser.browser.metrics",description:"request",startTimestamp:n+Sp(e.requestStart),endTimestamp:n+Sp(e.responseEnd)}),_p(t,{op:"browser",origin:"auto.browser.browser.metrics",description:"response",startTimestamp:n+Sp(e.responseStart),endTimestamp:n+Sp(e.responseEnd)}))}(t,e,n)}(t,e,n);break;case"mark":case"paint":case"measure":{!function(t,e,n,r,i){const o=i+n,s=o+r;_p(t,{description:e.name,endTimestamp:s,op:e.entryType,origin:"auto.resource.browser.metrics",startTimestamp:o})}(t,e,r,i,n);const o=Ss(),s=e.startTime<o.firstHiddenTime;"first-paint"===e.name&&s&&(ds&&N.log("[Measurements] Adding FP"),Ep.fp={value:e.startTime,unit:"millisecond"}),"first-contentful-paint"===e.name&&s&&(ds&&N.log("[Measurements] Adding FCP"),Ep.fcp={value:e.startTime,unit:"millisecond"});break}case"resource":!function(t,e,n,r,i,o){if("xmlhttprequest"===e.initiatorType||"fetch"===e.initiatorType)return;const s=So(n),a={};Np(a,e,"transferSize","http.response_transfer_size"),Np(a,e,"encodedBodySize","http.response_content_length"),Np(a,e,"decodedBodySize","http.decoded_response_content_length"),"renderBlockingStatus"in e&&(a["resource.render_blocking_status"]=e.renderBlockingStatus),s.protocol&&(a["url.scheme"]=s.protocol.split(":").pop()),s.host&&(a["server.address"]=s.host),a["url.same_origin"]=n.includes(hs.location.origin);const c=o+r,u=c+i;_p(t,{description:n.replace(hs.location.origin,""),endTimestamp:u,op:e.initiatorType?`resource.${e.initiatorType}`:"resource.other",origin:"auto.resource.browser.metrics",startTimestamp:c,data:a})}(t,e,e.name,r,i,n)}})),Cp=Math.max(r.length-1,0),function(t){const e=hs.navigator;if(!e)return;const n=e.connection;n&&(n.effectiveType&&t.setTag("effectiveConnectionType",n.effectiveType),n.type&&t.setTag("connectionType",n.type),vp(n.rtt)&&(Ep["connection.rtt"]={value:n.rtt,unit:"millisecond"})),vp(e.deviceMemory)&&t.setTag("deviceMemory",`${e.deviceMemory} GB`),vp(e.hardwareConcurrency)&&t.setTag("hardwareConcurrency",String(e.hardwareConcurrency))}(t),"pageload"===i){!function(t){const e=fs();if(!e)return;const{responseStart:n,requestStart:r}=e;r<=n&&(ds&&N.log("[Measurements] Adding TTFB Request Time"),t["ttfb.requestTime"]={value:n-r,unit:"millisecond"})}(Ep),["fcp","fp","lcp"].forEach((t=>{if(!Ep[t]||!o||n>=o)return;const e=Ep[t].value,r=n+Sp(e),i=Math.abs(1e3*(r-o)),s=i-e;ds&&N.log(`[Measurements] Normalized ${t} from ${e} to ${i} (${s})`),Ep[t].value=i}));const e=Ep["mark.fid"];e&&Ep.fid&&(_p(t,{description:"first input delay",endTimestamp:e.value+Sp(Ep.fid.value),op:"ui.action",origin:"auto.ui.browser.metrics",startTimestamp:e.value}),delete Ep["mark.fid"]),"fcp"in Ep||delete Ep.cls,Object.keys(Ep).forEach((t=>{oi(t,Ep[t].value,Ep[t].unit)})),function(t){kp&&(ds&&N.log("[Measurements] Adding LCP Data"),kp.element&&t.setTag("lcp.element",C(kp.element)),kp.id&&t.setTag("lcp.id",kp.id),kp.url&&t.setTag("lcp.url",kp.url.trim().slice(0,200)),t.setTag("lcp.size",kp.size)),xp&&xp.sources&&(ds&&N.log("[Measurements] Adding CLS Data"),xp.sources.forEach(((e,n)=>t.setTag(`cls.source.${n+1}`,C(e.node)))))}(t)}kp=void 0,xp=void 0,Ep={}}function Dp(t,e,n,r,i,o){const s=o?e[o]:e[`${n}End`],a=e[`${n}Start`];a&&s&&_p(t,{op:"browser",origin:"auto.browser.browser.metrics",description:i||n,startTimestamp:r+Sp(a),endTimestamp:r+Sp(s)})}function Np(t,e,n,r){const i=e[n];null!=i&&i<bp&&(t[r]=i)}const Lp=["localhost",/^\/(?!\/)/],Pp={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,tracingOrigins:Lp,tracePropagationTargets:Lp};function Fp(t){const{traceFetch:e,traceXHR:n,tracePropagationTargets:r,tracingOrigins:i,shouldCreateSpanForRequest:o,enableHTTPTimings:s}={traceFetch:Pp.traceFetch,traceXHR:Pp.traceXHR,...t},a="function"==typeof o?o:t=>!0,c=t=>function(t,e){return j(t,e||Lp)}(t,r||i),u={};e&&mo((t=>{const e=function(t,e,n,r,i="auto.http.browser"){if(!dr()||!t.fetchData)return;const o=e(t.fetchData.url);if(t.endTimestamp&&o){const e=t.fetchData.__span;if(!e)return;const n=r[e];return void(n&&(function(t,e){if(e.response){Ir(t,e.response.status);const n=e.response&&e.response.headers&&e.response.headers.get("content-length");if(n){const e=parseInt(n);e>0&&t.setAttribute("http.response_content_length",e)}}else e.error&&t.setStatus("internal_error");t.end()}(n,t),delete r[e]))}const s=Ne(),a=Ae(),{method:c,url:u}=t.fetchData,l=function(t){try{return new URL(t).href}catch(t){return}}(u),d=l?So(l).host:void 0,p=o?mr({name:`${c} ${u}`,onlyIfParent:!0,attributes:{url:u,type:"fetch","http.method":c,"http.url":l,"server.address":d,[Ln]:i},op:"http.client"}):void 0;if(p&&(t.fetchData.__span=p.spanContext().spanId,r[p.spanContext().spanId]=p),n(t.fetchData.url)&&a){const e=t.args[0];t.args[1]=t.args[1]||{};const n=t.args[1];n.headers=function(t,e,n,r,i){const o=i||n.getSpan(),s=Qt(),{traceId:a,spanId:c,sampled:u,dsc:l}={...s.getPropagationContext(),...n.getPropagationContext()},d=o?At(o):Rt(a,c,u),p=xt(l||(o?jt(o):Ft(a,e,n))),h=r.headers||("undefined"!=typeof Request&&y(t,Request)?t.headers:void 0);if(h){if("undefined"!=typeof Headers&&y(h,Headers)){const t=new Headers(h);return t.append("sentry-trace",d),p&&t.append(_t,p),t}if(Array.isArray(h)){const t=[...h,["sentry-trace",d]];return p&&t.push([_t,p]),t}{const t="baggage"in h?h.baggage:void 0,e=[];return Array.isArray(t)?e.push(...t):t&&e.push(t),p&&e.push(p),{...h,"sentry-trace":d,baggage:e.length>0?e.join(","):void 0}}}return{"sentry-trace":d,baggage:p}}(e,a,s,n,p)}return p}(t,a,c,u);if(e){const n=Hp(t.fetchData.url),r=n?So(n).host:void 0;e.setAttributes({"http.url":n,"server.address":r})}s&&e&&jp(e)})),n&&ho((t=>{const e=function(t,e,n,r){const i=t.xhr,o=i&&i[po];if(!dr()||!i||i.__sentry_own_request__||!o)return;const s=e(o.url);if(t.endTimestamp&&s){const t=i.__sentry_xhr_span_id__;if(!t)return;const e=r[t];return void(e&&void 0!==o.status_code&&(Ir(e,o.status_code),e.end(),delete r[t]))}const a=Ne(),c=Qt(),u=Hp(o.url),l=u?So(u).host:void 0,d=s?mr({name:`${o.method} ${o.url}`,onlyIfParent:!0,attributes:{type:"xhr","http.method":o.method,"http.url":u,url:o.url,"server.address":l,[Ln]:"auto.http.browser"},op:"http.client"}):void 0;d&&(i.__sentry_xhr_span_id__=d.spanContext().spanId,r[i.__sentry_xhr_span_id__]=d);const p=Ae();if(i.setRequestHeader&&n(o.url)&&p){const{traceId:t,spanId:e,sampled:n,dsc:r}={...c.getPropagationContext(),...a.getPropagationContext()};!function(t,e,n){try{t.setRequestHeader("sentry-trace",e),n&&t.setRequestHeader(_t,n)}catch(t){}}(i,d?At(d):Rt(t,e,n),xt(r||(d?jt(d):Ft(t,p,a))))}return d}(t,a,c,u);s&&e&&jp(e)}))}function jp(t){const{url:e}=Lt(t).data||{};if(!e||"string"!=typeof e)return;const n=Ys("resource",(({entries:r})=>{r.forEach((r=>{(function(t){return"resource"===t.entryType&&"initiatorType"in t&&"string"==typeof t.nextHopProtocol&&("fetch"===t.initiatorType||"xmlhttprequest"===t.initiatorType)})(r)&&r.name.endsWith(e)&&(function(t){const{name:e,version:n}=function(t){let e="unknown",n="unknown",r="";for(const i of t){if("/"===i){[e,n]=t.split("/");break}if(!isNaN(Number(i))){e="h"===r?"http":r,n=t.split(r)[1];break}r+=i}return r===t&&(e=r),{name:e,version:n}}(t.nextHopProtocol),r=[];return r.push(["network.protocol.version",n],["network.protocol.name",e]),at?[...r,["http.request.redirect_start",$p(t.redirectStart)],["http.request.fetch_start",$p(t.fetchStart)],["http.request.domain_lookup_start",$p(t.domainLookupStart)],["http.request.domain_lookup_end",$p(t.domainLookupEnd)],["http.request.connect_start",$p(t.connectStart)],["http.request.secure_connection_start",$p(t.secureConnectionStart)],["http.request.connection_end",$p(t.connectEnd)],["http.request.request_start",$p(t.requestStart)],["http.request.response_start",$p(t.responseStart)],["http.request.response_end",$p(t.responseEnd)]]:r}(r).forEach((e=>t.setAttribute(...e))),setTimeout(n))}))}))}function $p(t=0){return((at||performance.timeOrigin)+t)/1e3}function Hp(t){try{return new URL(t,hs.location.origin).href}catch(t){return}}const Bp={...Ar,markBackgroundTransactions:!0,routingInstrumentation:function(t,e=!0,n=!0){if(!hs||!hs.location)return void(ds&&N.warn("Could not initialize routing instrumentation due to invalid location"));let r,i=hs.location.href;e&&(r=t({name:hs.location.pathname,startTimestamp:at?at/1e3:void 0,op:"pageload",origin:"auto.pageload.browser",metadata:{source:"url"}})),n&&Qi((({to:e,from:n})=>{void 0===n&&i&&-1!==i.indexOf(e)?i=void 0:n!==e&&(i=void 0,r&&(ds&&N.log(`[Tracing] Finishing current transaction with op: ${r.op}`),r.end()),r=t({name:hs.location.pathname,op:"navigation",origin:"auto.navigation.browser",metadata:{source:"url"}}))}))},startTransactionOnLocationChange:!0,startTransactionOnPageLoad:!0,enableLongTask:!0,enableInp:!1,interactionsSampleRate:1,_experiments:{},...Pp};class Up{constructor(t){this.name="BrowserTracing",this._hasSetTracePropagationTargets=!1,Br(),ds&&(this._hasSetTracePropagationTargets=!(!t||!t.tracePropagationTargets&&!t.tracingOrigins)),this.options={...Bp,...t},void 0!==this.options._experiments.enableLongTask&&(this.options.enableLongTask=this.options._experiments.enableLongTask),t&&!t.tracePropagationTargets&&t.tracingOrigins&&(this.options.tracePropagationTargets=t.tracingOrigins),this._collectWebVitals=Tp(),this._interactionIdToRouteNameMapping={},this.options.enableInp&&Op(this._interactionIdToRouteNameMapping,this.options.interactionsSampleRate),this.options.enableLongTask&&Ip(),this.options._experiments.enableInteractions&&Rp(),this._latestRoute={name:void 0,context:void 0}}setupOnce(t,e){this._getCurrentHub=e;const n=e().getClient(),r=n&&n.getOptions(),{routingInstrumentation:i,startTransactionOnLocationChange:o,startTransactionOnPageLoad:s,markBackgroundTransactions:a,traceFetch:c,traceXHR:u,shouldCreateSpanForRequest:l,enableHTTPTimings:d,_experiments:p}=this.options,h=r&&r.tracePropagationTargets,f=h||this.options.tracePropagationTargets;ds&&this._hasSetTracePropagationTargets&&h&&N.warn("[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used."),i((t=>{const n=this._createRouteTransaction(t);return this.options._experiments.onStartRouteTransaction&&this.options._experiments.onStartRouteTransaction(n,t,e),n}),s,o),a&&gp(),p.enableInteractions&&this._registerInteractionListener(),this.options.enableInp&&this._registerInpInteractionListener(),Fp({traceFetch:c,traceXHR:u,tracePropagationTargets:f,shouldCreateSpanForRequest:l,enableHTTPTimings:d})}_createRouteTransaction(t){if(!this._getCurrentHub)return void(ds&&N.warn(`[Tracing] Did not create ${t.op} transaction because _getCurrentHub is invalid.`));const e=this._getCurrentHub(),{beforeNavigate:n,idleTimeout:r,finalTimeout:i,heartbeatInterval:o}=this.options,s="pageload"===t.op;let a;if(s){const e=s?qp("sentry-trace"):"",n=s?qp("baggage"):void 0,{traceId:r,dsc:i,parentSpanId:o,sampled:c}=It(e,n);a={traceId:r,parentSpanId:o,parentSampled:c,...t,metadata:{...t.metadata,dynamicSamplingContext:i},trimEnd:!0}}else a={trimEnd:!0,...t};const c="function"==typeof n?n(a):a,u=void 0===c?{...a,sampled:!1}:c;u.metadata=u.name!==a.name?{...u.metadata,source:"custom"}:u.metadata,this._latestRoute.name=u.name,this._latestRoute.context=u,!1===u.sampled&&ds&&N.log(`[Tracing] Will not send ${u.op} transaction because of beforeNavigate.`),ds&&N.log(`[Tracing] Starting ${u.op} transaction on scope`);const{location:l}=hs,d=Hr(e,u,r,i,!0,{location:l},o,s);return s&&hs.document&&(hs.document.addEventListener("readystatechange",(()=>{["interactive","complete"].includes(hs.document.readyState)&&d.sendAutoFinishSignal()})),["interactive","complete"].includes(hs.document.readyState)&&d.sendAutoFinishSignal()),d.registerBeforeFinishCallback((t=>{this._collectWebVitals(),Ap(t)})),d}_registerInteractionListener(){let t;const e=()=>{const{idleTimeout:e,finalTimeout:n,heartbeatInterval:r}=this.options,i="ui.action.click",o=sr();if(o&&o.op&&["navigation","pageload"].includes(o.op))return void(ds&&N.warn(`[Tracing] Did not create ${i} transaction because a pageload or navigation transaction is in progress.`));if(t&&(t.setFinishReason("interactionInterrupted"),t.end(),t=void 0),!this._getCurrentHub)return void(ds&&N.warn(`[Tracing] Did not create ${i} transaction because _getCurrentHub is invalid.`));if(!this._latestRoute.name)return void(ds&&N.warn(`[Tracing] Did not create ${i} transaction because _latestRouteName is missing.`));const s=this._getCurrentHub(),{location:a}=hs,c={name:this._latestRoute.name,op:i,trimEnd:!0,data:{[An]:this._latestRoute.context?zp(this._latestRoute.context):"url"}};t=Hr(s,c,e,n,!0,{location:a},r)};["click"].forEach((t=>{hs.document&&addEventListener(t,e,{once:!1,capture:!0})}))}_registerInpInteractionListener(){const t=({entries:t})=>{const e=Ae(),n=void 0!==e&&void 0!==e.getIntegrationByName?e.getIntegrationByName("Replay"):void 0,r=void 0!==n?n.getReplayId():void 0,i=sr(),o=Ne(),s=void 0!==o?o.getUser():void 0;t.forEach((t=>{if(function(t){return"duration"in t}(t)){const e=t.interactionId;if(void 0===e)return;const n=this._interactionIdToRouteNameMapping[e],o=t.duration,a=t.startTime,c=Object.keys(this._interactionIdToRouteNameMapping),u=c.length>0?c.reduce(((t,e)=>this._interactionIdToRouteNameMapping[t].duration<this._interactionIdToRouteNameMapping[e].duration?t:e)):void 0;if("first-input"===t.entryType&&c.map((t=>this._interactionIdToRouteNameMapping[t])).some((t=>t.duration===o&&t.startTime===a)))return;if(!e)return;if(n)n.duration=Math.max(n.duration,o);else if(c.length<10||void 0===u||o>this._interactionIdToRouteNameMapping[u].duration){const t=this._latestRoute.name,n=this._latestRoute.context;t&&n&&(u&&Object.keys(this._interactionIdToRouteNameMapping).length>=10&&delete this._interactionIdToRouteNameMapping[u],this._interactionIdToRouteNameMapping[e]={routeName:t,duration:o,parentContext:n,user:s,activeTransaction:i,replayId:r,startTime:a})}}}))};Ys("event",t),Ys("first-input",t)}}function qp(t){const e=I(`meta[name=${t}]`);return e?e.getAttribute("content"):void 0}function zp(t){const e=t.attributes&&t.attributes[An],n=t.data&&t.data[An],r=t.metadata&&t.metadata.source;return e||n||r}const Wp={...Ar,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableInp:!1,interactionsSampleRate:1,_experiments:{},...Pp},Yp=(t={})=>{const e=!(!ds||!t.tracePropagationTargets&&!t.tracingOrigins);Br(),!t.tracePropagationTargets&&t.tracingOrigins&&(t.tracePropagationTargets=t.tracingOrigins);const n={...Wp,...t},r=Tp(),i={};n.enableInp&&Op(i,n.interactionsSampleRate),n.enableLongTask&&Ip(),n._experiments.enableInteractions&&Rp();const o={name:void 0,context:void 0};function s(t){const e=Xt(),{beforeStartSpan:i,idleTimeout:s,finalTimeout:a,heartbeatInterval:c}=n,u="pageload"===t.op;let l;if(u){const e=u?Vp("sentry-trace"):"",n=u?Vp("baggage"):void 0,{traceId:r,dsc:i,parentSpanId:o,sampled:s}=It(e,n);l={traceId:r,parentSpanId:o,parentSampled:s,...t,metadata:{...t.metadata,dynamicSamplingContext:i},trimEnd:!0}}else l={trimEnd:!0,...t};const d=i?i(l):l;d.metadata=d.name!==l.name?{...d.metadata,source:"custom"}:d.metadata,o.name=d.name,o.context=d,!1===d.sampled&&ds&&N.log(`[Tracing] Will not send ${d.op} transaction because of beforeNavigate.`),ds&&N.log(`[Tracing] Starting ${d.op} transaction on scope`);const{location:p}=hs,h=Hr(e,d,s,a,!0,{location:p},c,u);return u&&hs.document&&(hs.document.addEventListener("readystatechange",(()=>{["interactive","complete"].includes(hs.document.readyState)&&h.sendAutoFinishSignal()})),["interactive","complete"].includes(hs.document.readyState)&&h.sendAutoFinishSignal()),h.registerBeforeFinishCallback((t=>{r(),Ap(t)})),h}return{name:"BrowserTracing",setupOnce:()=>{},afterAllSetup(t){const r=t.getOptions(),{markBackgroundSpan:a,traceFetch:c,traceXHR:u,shouldCreateSpanForRequest:l,enableHTTPTimings:d,_experiments:p}=n,h=r&&r.tracePropagationTargets,f=h||n.tracePropagationTargets;let m;ds&&e&&h&&N.warn("[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used.");let g=hs.location&&hs.location.href;if(t.on&&(t.on("startNavigationSpan",(t=>{m&&(ds&&N.log(`[Tracing] Finishing current transaction with op: ${Lt(m).op}`),m.end()),m=s({op:"navigation",...t})})),t.on("startPageLoadSpan",(t=>{m&&(ds&&N.log(`[Tracing] Finishing current transaction with op: ${Lt(m).op}`),m.end()),m=s({op:"pageload",...t})}))),n.instrumentPageLoad&&t.emit&&hs.location){const e={name:hs.location.pathname,startTimestamp:at?at/1e3:void 0,origin:"auto.pageload.browser",attributes:{[An]:"url"}};Gp(t,e)}n.instrumentNavigation&&t.emit&&hs.location&&Qi((({to:e,from:n})=>{if(void 0===n&&g&&-1!==g.indexOf(e))g=void 0;else if(n!==e){g=void 0;const e={name:hs.location.pathname,origin:"auto.navigation.browser",attributes:{[An]:"url"}};Jp(t,e)}})),a&&gp(),p.enableInteractions&&function(t,e){let n;const r=()=>{const{idleTimeout:r,finalTimeout:i,heartbeatInterval:o}=t,s="ui.action.click",a=sr();if(a&&a.op&&["navigation","pageload"].includes(a.op))return void(ds&&N.warn(`[Tracing] Did not create ${s} transaction because a pageload or navigation transaction is in progress.`));if(n&&(n.setFinishReason("interactionInterrupted"),n.end(),n=void 0),!e.name)return void(ds&&N.warn(`[Tracing] Did not create ${s} transaction because _latestRouteName is missing.`));const{location:c}=hs,u={name:e.name,op:s,trimEnd:!0,data:{[An]:e.context?Xp(e.context):"url"}};n=Hr(Xt(),u,r,i,!0,{location:c},o)};["click"].forEach((t=>{hs.document&&addEventListener(t,r,{once:!1,capture:!0})}))}(n,o),n.enableInp&&function(t,e){const n=({entries:n})=>{const r=Ae(),i=void 0!==r&&void 0!==r.getIntegrationByName?r.getIntegrationByName("Replay"):void 0,o=void 0!==i?i.getReplayId():void 0,s=sr(),a=Ne(),c=void 0!==a?a.getUser():void 0;n.forEach((n=>{if(function(t){return"duration"in t}(n)){const r=n.interactionId;if(void 0===r)return;const i=t[r],a=n.duration,u=n.startTime,l=Object.keys(t),d=l.length>0?l.reduce(((e,n)=>t[e].duration<t[n].duration?e:n)):void 0;if("first-input"===n.entryType&&l.map((e=>t[e])).some((t=>t.duration===a&&t.startTime===u)))return;if(!r)return;if(i)i.duration=Math.max(i.duration,a);else if(l.length<Kp||void 0===d||a>t[d].duration){const n=e.name,i=e.context;n&&i&&(d&&Object.keys(t).length>=Kp&&delete t[d],t[r]={routeName:n,duration:a,parentContext:i,user:c,activeTransaction:s,replayId:o,startTime:u})}}}))};Ys("event",n),Ys("first-input",n)}(i,o),Fp({traceFetch:c,traceXHR:u,tracePropagationTargets:f,shouldCreateSpanForRequest:l,enableHTTPTimings:d})},options:n}};function Gp(t,e){if(!t.emit)return;t.emit("startPageLoadSpan",e);const n=gr();return"pageload"===(n&&Lt(n).op)?n:void 0}function Jp(t,e){if(!t.emit)return;t.emit("startNavigationSpan",e);const n=gr();return"navigation"===(n&&Lt(n).op)?n:void 0}function Vp(t){const e=I(`meta[name=${t}]`);return e?e.getAttribute("content"):void 0}const Kp=10;function Xp(t){const e=t.attributes&&t.attributes[An],n=t.data&&t.data[An],r=t.metadata&&t.metadata.source;return e||n||r}const Qp=100,Zp=5e3,th=36e5;function eh(t,e){V&&N.info(`[Offline]: ${t}`,e)}function nh(t){return e=>{const n=t(e),r=e.createStore?e.createStore(e):void 0;let i,o=Zp;function s(t){r&&(i&&clearTimeout(i),i=setTimeout((async()=>{i=void 0;const t=await r.pop();t&&(eh("Attempting to send previously queued event"),c(t).catch((t=>{eh("Failed to retry sending",t)})))}),t),"number"!=typeof i&&i.unref&&i.unref())}function a(){i||(s(o),o=Math.min(2*o,th))}async function c(t){try{const e=await n.send(t);let r=Qp;if(e)if(e.headers&&e.headers["retry-after"])r=qr(e.headers["retry-after"]);else if((e.statusCode||0)>=400)return e;return s(r),o=Zp,e}catch(n){if(r&&await function(t,n,r){return i=["replay_event","replay_recording","client_report"],!vn(t,((t,e)=>i.includes(e)))&&(!e.shouldStore||e.shouldStore(t,n,r));var i}(t,n,o))return await r.insert(t),a(),eh("Error sending. Event queued",n),{};throw n}}return e.flushAtStartup&&a(),{send:c,flush:t=>n.flush(t)}}}function rh(t){return new Promise(((e,n)=>{t.oncomplete=t.onsuccess=()=>e(t.result),t.onabort=t.onerror=()=>n(t.error)}))}function ih(t){return rh(t.getAllKeys())}function oh(t){let e;function n(){return null==e&&(e=function(t,e){const n=indexedDB.open(t);n.onupgradeneeded=()=>n.result.createObjectStore(e);const r=rh(n);return t=>r.then((n=>t(n.transaction(e,"readwrite").objectStore(e))))}(t.dbName||"sentry-offline",t.storeName||"queue")),e}return{insert:async e=>{try{const r=await bn(e,t.textEncoder);await function(t,e,n){return t((t=>ih(t).then((r=>{if(!(r.length>=n))return t.put(e,Math.max(...r,0)+1),rh(t.transaction)}))))}(n(),r,t.maxQueueSize||30)}catch(t){}},pop:async()=>{try{const e=await function(t){return t((t=>ih(t).then((e=>{if(0!==e.length)return rh(t.get(e[0])).then((n=>(t.delete(e[0]),rh(t.transaction).then((()=>n)))))}))))}(n());if(e)return function(t,e,n){let r="string"==typeof t?e.encode(t):t;function i(t){const e=r.subarray(0,t);return r=r.subarray(t+1),e}function o(){let t=r.indexOf(10);return t<0&&(t=r.length),JSON.parse(n.decode(i(t)))}const s=o(),a=[];for(;r.length;){const t=o(),e="number"==typeof t.length?t.length:void 0;a.push([t,e?i(e):o()])}return[s,a]}(e,t.textEncoder||new TextEncoder,t.textDecoder||new TextDecoder)}catch(t){}}}}function sh(t){return function(t){return e=>t({...e,createStore:oh})}(nh(t))}const ah=1e6,ch=String(0),uh="main";let lh="",dh="",ph="",hh=si.navigator&&si.navigator.userAgent||"",fh="";const mh=si.navigator&&si.navigator.language||si.navigator&&si.navigator.languages&&si.navigator.languages[0]||"",gh=si.navigator&&si.navigator.userAgentData;var yh;function vh(t,e,n,r){if("transaction"!==r.type)throw new TypeError("Profiling events may only be attached to transactions, this should never occur.");if(null==n)throw new TypeError(`Cannot construct profiling event envelope without a valid profile. Got ${n} instead.`);const i=function(t){const e=t&&t.contexts&&t.contexts.trace&&t.contexts.trace.trace_id;return"string"==typeof e&&32!==e.length&&di&&N.log(`[Profiling] Invalid traceId: ${e} on profiled event`),"string"!=typeof e?"":e}(r),o=function(t){return!("thread_metadata"in t)}(c=n)?function(t){let e,n=0;const r={samples:[],stacks:[],frames:[],thread_metadata:{[ch]:{name:uh}}};if(!t.samples.length)return r;const i=t.samples[0].timestamp,o="number"==typeof performance.timeOrigin?performance.timeOrigin:at||0,s=o-(at||o);for(let o=0;o<t.samples.length;o++){const a=t.samples[o];if(void 0===a.stackId){void 0===e&&(e=n,r.stacks[e]=[],n++),r.samples[o]={elapsed_since_start_ns:((a.timestamp+s-i)*ah).toFixed(0),stack_id:e,thread_id:ch};continue}let c=t.stacks[a.stackId];const u=[];for(;c;){u.push(c.frameId);const e=t.frames[c.frameId];void 0===r.frames[c.frameId]&&(r.frames[c.frameId]={function:e.name,abs_path:"number"==typeof e.resourceId?t.resources[e.resourceId]:void 0,lineno:e.line,colno:e.column}),c=void 0===c.parentId?void 0:t.stacks[c.parentId]}const l={elapsed_since_start_ns:((a.timestamp+s-i)*ah).toFixed(0),stack_id:n,thread_id:ch};r.stacks[n]=u,r.samples[o]=l,n++}return r}(c):c,s=e||("number"==typeof r.start_timestamp?1e3*r.start_timestamp:Date.now()),a="number"==typeof r.timestamp?1e3*r.timestamp:Date.now();var c;return{event_id:t,timestamp:new Date(s).toISOString(),platform:"javascript",version:"1",release:r.release||"",environment:r.environment||J,runtime:{name:"javascript",version:si.navigator.userAgent},os:{name:lh,version:dh,build_number:hh},device:{locale:mh,model:fh,manufacturer:hh,architecture:ph,is_emulator:!1},debug_meta:{images:Sh(n.resources)},profile:o,transactions:[{name:r.transaction||"",id:r.event_id||K(),trace_id:i,active_thread_id:ch,relative_start_ns:"0",relative_end_ns:(1e6*(a-s)).toFixed(0)}]}}function _h(t){return"pageload"===t.op}"object"==typeof(yh=gh)&&null!==yh&&"getHighEntropyValues"in yh&&gh.getHighEntropyValues(["architecture","model","platform","platformVersion","fullVersionList"]).then((t=>{if(lh=t.platform||"",ph=t.architecture||"",fh=t.model||"",dh=t.platformVersion||"",t.fullVersionList&&t.fullVersionList.length>0){const e=t.fullVersionList[t.fullVersionList.length-1];hh=`${e.brand} ${e.version}`}})).catch((t=>{}));const bh=new WeakMap;function Sh(t){const e=b._sentryDebugIds;if(!e)return[];const n=Ae(),r=n&&n.getOptions(),i=r&&r.stackParser;if(!i)return[];let o;const s=bh.get(i);s?o=s:(o=new Map,bh.set(i,o));const a=Object.keys(e).reduce(((t,n)=>{let r;const s=o.get(n);s?r=s:(r=i(n),o.set(n,r));for(let i=r.length-1;i>=0;i--){const o=r[i],s=o&&o.filename;if(o&&s){t[s]=e[n];break}}return t}),{}),c=[];for(const e of t)e&&a[e]&&c.push({type:"sourcemap",code_file:e,debug_id:a[e]});return c}let wh=!1;const kh=3e4;function xh(t){if(wh)return di&&N.log("[Profiling] Profiling has been disabled for the duration of the current user session."),!1;if(!t.isRecording())return di&&N.log("[Profiling] Discarding profile because transaction was not sampled."),!1;const e=Ae(),n=e&&e.getOptions();if(!n)return di&&N.log("[Profiling] Profiling disabled, no options found."),!1;const r=n.profilesSampleRate;return("number"!=typeof(i=r)&&"boolean"!=typeof i||"number"==typeof i&&isNaN(i)?(di&&N.warn(`[Profiling] Invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(i)} of type ${JSON.stringify(typeof i)}.`),0):!0===i||!1===i||!(i<0||i>1)||(di&&N.warn(`[Profiling] Invalid sample rate. Sample rate must be between 0 and 1. Got ${i}.`),0))?r?!!(!0===r||Math.random()<r)||(di&&N.log(`[Profiling] Discarding profile because it's not included in the random sample (sampling rate = ${Number(r)})`),!1):(di&&N.log("[Profiling] Discarding profile because a negative sampling decision was inherited or profileSampleRate is set to 0"),!1):(di&&N.warn("[Profiling] Discarding profile because of invalid sample rate."),!1);var i}function Ch(t,e,n,r){return function(t){return t.samples.length<2?(di&&N.log("[Profiling] Discarding profile because it contains less than 2 samples"),!1):!!t.frames.length||(di&&N.log("[Profiling] Discarding profile because it contains no frames"),!1)}(n)?vh(t,e,n,r):null}const Eh=new Map;function Th(t){const e=Eh.get(t);return e&&Eh.delete(t),e}function Ih(t){return t?xh(t)?Rh(t):t:(di&&N.log("[Profiling] Transaction is undefined, skipping profiling"),t)}function Rh(t){let e;_h(t)&&(e=1e3*ot());const n=function(){const t=si.Profiler;if("function"!=typeof t)return void(di&&N.log("[Profiling] Profiling is not supported by this browser, Profiler interface missing on window object."));const e=Math.floor(kh/10);try{return new t({sampleInterval:10,maxBufferSize:e})}catch(t){di&&(N.log("[Profiling] Failed to initialize the Profiling constructor, this is likely due to a missing 'Document-Policy': 'js-profiling' header."),N.log("[Profiling] Disabling profiling for current user session.")),wh=!0}}();if(!n)return t;di&&N.log(`[Profiling] started profiling transaction: ${Lt(t).description}`);const r=K();async function i(){return t&&n?n.stop().then((e=>(o&&(si.clearTimeout(o),o=void 0),di&&N.log(`[Profiling] stopped profiling of transaction: ${Lt(t).description}`),e?(function(t,e){if(Eh.set(t,e),Eh.size>30){const t=Eh.keys().next().value;Eh.delete(t)}}(r,e),null):(di&&N.log(`[Profiling] profiler returned null profile for: ${Lt(t).description}`,"this may indicate an overlapping transaction or a call to stopProfiling with a profile title that was never started"),null)))).catch((t=>(di&&N.log("[Profiling] error while stopping profiler:",t),null))):null}let o=si.setTimeout((()=>{di&&N.log("[Profiling] max profile duration elapsed, stopping profiling for:",Lt(t).description),i()}),kh);const s=t.end.bind(t);return t.end=function(){return t?(i().then((()=>{t.setContext("profile",{profile_id:r,start_timestamp:e}),s()}),(()=>{s()})),t):s()},t}const Oh="BrowserProfiling",Mh=()=>({name:Oh,setupOnce(){},setup(t){const e=Ne().getTransaction();e&&_h(e)&&xh(e)&&Rh(e),"function"==typeof t.on?(t.on("startTransaction",(t=>{xh(t)&&Rh(t)})),t.on("beforeEnvelope",(t=>{if(!Eh.size)return;const e=function(t){const e=[];return vn(t,((t,n)=>{if("transaction"===n)for(let n=1;n<t.length;n++){const r=t[n];r&&r.contexts&&r.contexts.profile&&r.contexts.profile.profile_id&&e.push(t[n])}})),e}(t);if(!e.length)return;const n=[];for(const t of e){const e=t&&t.contexts,r=e&&e.profile&&e.profile.profile_id,i=e&&e.profile&&e.profile.start_timestamp;if("string"!=typeof r){di&&N.log("[Profiling] cannot find profile for a transaction without a profile context");continue}if(!r){di&&N.log("[Profiling] cannot find profile for a transaction without a profile context");continue}e&&e.profile&&delete e.profile;const o=Th(r);if(!o){di&&N.log(`[Profiling] Could not retrieve profile for transaction: ${r}`);continue}const s=Ch(r,i,o,t);s&&n.push(s)}!function(t,e){if(!e.length)return t;for(const n of e)t[1].push([{type:"profile"},n])}(t,n)}))):N.warn("[Profiling] Client does not support hooks, profiling will be disabled")}}),Ah=ze(Oh,Mh);let Dh={};si.Sentry&&si.Sentry.Integrations&&(Dh=si.Sentry.Integrations);const Nh={...Dh,...ln,...i}},6958:(t,e,n)=>{"use strict";function r(){return"undefined"!=typeof __SENTRY_BROWSER_BUNDLE__&&!!__SENTRY_BROWSER_BUNDLE__}function i(){return"npm"}n.d(e,{Z:()=>r,e:()=>i})},8609:(t,e,n)=>{"use strict";n.d(e,{wD:()=>i});var r=n(6958);function i(){return!(0,r.Z)()&&"[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0)}t=n.hmd(t)},1193:t=>{"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}}},3220:(t,e,n)=>{"use strict";var r=n(8508),i=n(8244),o=n(2209),s=Object.prototype.toString;t.exports=function(t){return e=t,"[object Date]"===s.call(e)?t:function(t){return"[object Number]"===s.call(t)}(t)?new Date((n=t)<315576e5?1e3*n:n):r.is(t)?r.parse(t):i.is(t)?i.parse(t):o.is(t)?o.parse(t):new Date(t);var e,n}},8244:(t,e)=>{"use strict";var n=/\d{13}/;e.is=function(t){return n.test(t)},e.parse=function(t){return t=parseInt(t,10),new Date(t)}},2209:(t,e)=>{"use strict";var n=/\d{10}/;e.is=function(t){return n.test(t)},e.parse=function(t){var e=1e3*parseInt(t,10);return new Date(e)}},6287:t=>{function e(t){return function(e,n,r,o){var s,a=o&&function(t){return"function"==typeof t}(o.normalizer)?o.normalizer:i;n=a(n);for(var c=!1;!c;)u();function u(){for(s in e){var t=a(s);if(0===n.indexOf(t)){var r=n.substr(t.length);if("."===r.charAt(0)||0===r.length){n=r.substr(1);var i=e[s];return null==i?void(c=!0):n.length?void(e=i):void(c=!0)}}}s=void 0,c=!0}if(s)return null==e?e:t(e,s,r)}}function n(t,e){return t.hasOwnProperty(e)&&delete t[e],t}function r(t,e,n){return t.hasOwnProperty(e)&&(t[e]=n),t}function i(t){return t.replace(/[^a-zA-Z0-9\.]+/g,"").toLowerCase()}t.exports=e((function(t,e){if(t.hasOwnProperty(e))return t[e]})),t.exports.find=t.exports,t.exports.replace=function(t,n,i,o){return e(r).call(this,t,n,i,o),t},t.exports.del=function(t,r,i){return e(n).call(this,t,r,null,i),t}},5123:t=>{t.exports=function(t){"use strict";var e=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function n(t,e){var n=t[0],r=t[1],i=t[2],o=t[3];r=((r+=((i=((i+=((o=((o+=((n=((n+=(r&i|~r&o)+e[0]-680876936|0)<<7|n>>>25)+r|0)&r|~n&i)+e[1]-389564586|0)<<12|o>>>20)+n|0)&n|~o&r)+e[2]+606105819|0)<<17|i>>>15)+o|0)&o|~i&n)+e[3]-1044525330|0)<<22|r>>>10)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r&i|~r&o)+e[4]-176418897|0)<<7|n>>>25)+r|0)&r|~n&i)+e[5]+1200080426|0)<<12|o>>>20)+n|0)&n|~o&r)+e[6]-1473231341|0)<<17|i>>>15)+o|0)&o|~i&n)+e[7]-45705983|0)<<22|r>>>10)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r&i|~r&o)+e[8]+1770035416|0)<<7|n>>>25)+r|0)&r|~n&i)+e[9]-1958414417|0)<<12|o>>>20)+n|0)&n|~o&r)+e[10]-42063|0)<<17|i>>>15)+o|0)&o|~i&n)+e[11]-1990404162|0)<<22|r>>>10)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r&i|~r&o)+e[12]+1804603682|0)<<7|n>>>25)+r|0)&r|~n&i)+e[13]-40341101|0)<<12|o>>>20)+n|0)&n|~o&r)+e[14]-1502002290|0)<<17|i>>>15)+o|0)&o|~i&n)+e[15]+1236535329|0)<<22|r>>>10)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r&o|i&~o)+e[1]-165796510|0)<<5|n>>>27)+r|0)&i|r&~i)+e[6]-1069501632|0)<<9|o>>>23)+n|0)&r|n&~r)+e[11]+643717713|0)<<14|i>>>18)+o|0)&n|o&~n)+e[0]-373897302|0)<<20|r>>>12)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r&o|i&~o)+e[5]-701558691|0)<<5|n>>>27)+r|0)&i|r&~i)+e[10]+38016083|0)<<9|o>>>23)+n|0)&r|n&~r)+e[15]-660478335|0)<<14|i>>>18)+o|0)&n|o&~n)+e[4]-405537848|0)<<20|r>>>12)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r&o|i&~o)+e[9]+568446438|0)<<5|n>>>27)+r|0)&i|r&~i)+e[14]-1019803690|0)<<9|o>>>23)+n|0)&r|n&~r)+e[3]-187363961|0)<<14|i>>>18)+o|0)&n|o&~n)+e[8]+1163531501|0)<<20|r>>>12)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r&o|i&~o)+e[13]-1444681467|0)<<5|n>>>27)+r|0)&i|r&~i)+e[2]-51403784|0)<<9|o>>>23)+n|0)&r|n&~r)+e[7]+1735328473|0)<<14|i>>>18)+o|0)&n|o&~n)+e[12]-1926607734|0)<<20|r>>>12)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r^i^o)+e[5]-378558|0)<<4|n>>>28)+r|0)^r^i)+e[8]-2022574463|0)<<11|o>>>21)+n|0)^n^r)+e[11]+1839030562|0)<<16|i>>>16)+o|0)^o^n)+e[14]-35309556|0)<<23|r>>>9)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r^i^o)+e[1]-1530992060|0)<<4|n>>>28)+r|0)^r^i)+e[4]+1272893353|0)<<11|o>>>21)+n|0)^n^r)+e[7]-155497632|0)<<16|i>>>16)+o|0)^o^n)+e[10]-1094730640|0)<<23|r>>>9)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r^i^o)+e[13]+681279174|0)<<4|n>>>28)+r|0)^r^i)+e[0]-358537222|0)<<11|o>>>21)+n|0)^n^r)+e[3]-722521979|0)<<16|i>>>16)+o|0)^o^n)+e[6]+76029189|0)<<23|r>>>9)+i|0,r=((r+=((i=((i+=((o=((o+=((n=((n+=(r^i^o)+e[9]-640364487|0)<<4|n>>>28)+r|0)^r^i)+e[12]-421815835|0)<<11|o>>>21)+n|0)^n^r)+e[15]+530742520|0)<<16|i>>>16)+o|0)^o^n)+e[2]-995338651|0)<<23|r>>>9)+i|0,r=((r+=((o=((o+=(r^((n=((n+=(i^(r|~o))+e[0]-198630844|0)<<6|n>>>26)+r|0)|~i))+e[7]+1126891415|0)<<10|o>>>22)+n|0)^((i=((i+=(n^(o|~r))+e[14]-1416354905|0)<<15|i>>>17)+o|0)|~n))+e[5]-57434055|0)<<21|r>>>11)+i|0,r=((r+=((o=((o+=(r^((n=((n+=(i^(r|~o))+e[12]+1700485571|0)<<6|n>>>26)+r|0)|~i))+e[3]-1894986606|0)<<10|o>>>22)+n|0)^((i=((i+=(n^(o|~r))+e[10]-1051523|0)<<15|i>>>17)+o|0)|~n))+e[1]-2054922799|0)<<21|r>>>11)+i|0,r=((r+=((o=((o+=(r^((n=((n+=(i^(r|~o))+e[8]+1873313359|0)<<6|n>>>26)+r|0)|~i))+e[15]-30611744|0)<<10|o>>>22)+n|0)^((i=((i+=(n^(o|~r))+e[6]-1560198380|0)<<15|i>>>17)+o|0)|~n))+e[13]+1309151649|0)<<21|r>>>11)+i|0,r=((r+=((o=((o+=(r^((n=((n+=(i^(r|~o))+e[4]-145523070|0)<<6|n>>>26)+r|0)|~i))+e[11]-1120210379|0)<<10|o>>>22)+n|0)^((i=((i+=(n^(o|~r))+e[2]+718787259|0)<<15|i>>>17)+o|0)|~n))+e[9]-343485551|0)<<21|r>>>11)+i|0,t[0]=n+t[0]|0,t[1]=r+t[1]|0,t[2]=i+t[2]|0,t[3]=o+t[3]|0}function r(t){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=t.charCodeAt(e)+(t.charCodeAt(e+1)<<8)+(t.charCodeAt(e+2)<<16)+(t.charCodeAt(e+3)<<24);return n}function i(t){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=t[e]+(t[e+1]<<8)+(t[e+2]<<16)+(t[e+3]<<24);return n}function o(t){var e,i,o,s,a,c,u=t.length,l=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=u;e+=64)n(l,r(t.substring(e-64,e)));for(i=(t=t.substring(e-64)).length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e=0;e<i;e+=1)o[e>>2]|=t.charCodeAt(e)<<(e%4<<3);if(o[e>>2]|=128<<(e%4<<3),e>55)for(n(l,o),e=0;e<16;e+=1)o[e]=0;return s=(s=8*u).toString(16).match(/(.*?)(.{0,8})$/),a=parseInt(s[2],16),c=parseInt(s[1],16)||0,o[14]=a,o[15]=c,n(l,o),l}function s(t){var n,r="";for(n=0;n<4;n+=1)r+=e[t>>8*n+4&15]+e[t>>8*n&15];return r}function a(t){var e;for(e=0;e<t.length;e+=1)t[e]=s(t[e]);return t.join("")}function c(t){return/[\u0080-\uFFFF]/.test(t)&&(t=unescape(encodeURIComponent(t))),t}function u(t){var e,n=[],r=t.length;for(e=0;e<r-1;e+=2)n.push(parseInt(t.substr(e,2),16));return String.fromCharCode.apply(String,n)}function l(){this.reset()}return a(o("hello")),"undefined"==typeof ArrayBuffer||ArrayBuffer.prototype.slice||function(){function t(t,e){return(t=0|t||0)<0?Math.max(t+e,0):Math.min(t,e)}ArrayBuffer.prototype.slice=function(e,n){var r,i,o,s,a=this.byteLength,c=t(e,a),u=a;return undefined!==n&&(u=t(n,a)),c>u?new ArrayBuffer(0):(r=u-c,i=new ArrayBuffer(r),o=new Uint8Array(i),s=new Uint8Array(this,c,r),o.set(s),i)}}(),l.prototype.append=function(t){return this.appendBinary(c(t)),this},l.prototype.appendBinary=function(t){this._buff+=t,this._length+=t.length;var e,i=this._buff.length;for(e=64;e<=i;e+=64)n(this._hash,r(this._buff.substring(e-64,e)));return this._buff=this._buff.substring(e-64),this},l.prototype.end=function(t){var e,n,r=this._buff,i=r.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<i;e+=1)o[e>>2]|=r.charCodeAt(e)<<(e%4<<3);return this._finish(o,i),n=a(this._hash),t&&(n=u(n)),this.reset(),n},l.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},l.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash.slice()}},l.prototype.setState=function(t){return this._buff=t.buff,this._length=t.length,this._hash=t.hash,this},l.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},l.prototype._finish=function(t,e){var r,i,o,s=e;if(t[s>>2]|=128<<(s%4<<3),s>55)for(n(this._hash,t),s=0;s<16;s+=1)t[s]=0;r=(r=8*this._length).toString(16).match(/(.*?)(.{0,8})$/),i=parseInt(r[2],16),o=parseInt(r[1],16)||0,t[14]=i,t[15]=o,n(this._hash,t)},l.hash=function(t,e){return l.hashBinary(c(t),e)},l.hashBinary=function(t,e){var n=a(o(t));return e?u(n):n},l.ArrayBuffer=function(){this.reset()},l.ArrayBuffer.prototype.append=function(t){var e,r,o,s,a,c=(r=this._buff.buffer,o=t,s=!0,(a=new Uint8Array(r.byteLength+o.byteLength)).set(new Uint8Array(r)),a.set(new Uint8Array(o),r.byteLength),s?a:a.buffer),u=c.length;for(this._length+=t.byteLength,e=64;e<=u;e+=64)n(this._hash,i(c.subarray(e-64,e)));return this._buff=e-64<u?new Uint8Array(c.buffer.slice(e-64)):new Uint8Array(0),this},l.ArrayBuffer.prototype.end=function(t){var e,n,r=this._buff,i=r.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<i;e+=1)o[e>>2]|=r[e]<<(e%4<<3);return this._finish(o,i),n=a(this._hash),t&&(n=u(n)),this.reset(),n},l.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},l.ArrayBuffer.prototype.getState=function(){var t,e=l.prototype.getState.call(this);return e.buff=(t=e.buff,String.fromCharCode.apply(null,new Uint8Array(t))),e},l.ArrayBuffer.prototype.setState=function(t){return t.buff=function(t,e){var n,r=t.length,i=new ArrayBuffer(r),o=new Uint8Array(i);for(n=0;n<r;n+=1)o[n]=t.charCodeAt(n);return e?o:i}(t.buff,!0),l.prototype.setState.call(this,t)},l.ArrayBuffer.prototype.destroy=l.prototype.destroy,l.ArrayBuffer.prototype._finish=l.prototype._finish,l.ArrayBuffer.hash=function(t,e){var r=a(function(t){var e,r,o,s,a,c,u=t.length,l=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=u;e+=64)n(l,i(t.subarray(e-64,e)));for(r=(t=e-64<u?t.subarray(e-64):new Uint8Array(0)).length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e=0;e<r;e+=1)o[e>>2]|=t[e]<<(e%4<<3);if(o[e>>2]|=128<<(e%4<<3),e>55)for(n(l,o),e=0;e<16;e+=1)o[e]=0;return s=(s=8*u).toString(16).match(/(.*?)(.{0,8})$/),a=parseInt(s[2],16),c=parseInt(s[1],16)||0,o[14]=a,o[15]=c,n(l,o),l}(new Uint8Array(t)));return e?u(r):r},l}()},6009:function(t,e,n){"use strict";var r=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(i,o){function s(t){try{c(r.next(t))}catch(t){o(t)}}function a(t){try{c(r.throw(t))}catch(t){o(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((r=r.apply(t,e||[])).next())}))},i=this&&this.__generator||function(t,e){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,r=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((i=(i=s.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}};e.__esModule=!0,e.runRetrievalOfVerificationTag=void 0;var o=n(2115),s=n(6309),a="".concat(Math.floor(Date.now()/1e3)),c=new o.Scope;e.runRetrievalOfVerificationTag=function(t,e,n){return r(void 0,void 0,void 0,(function(){var r,s;return i(this,(function(i){switch(i.label){case 0:return i.trys.push([0,6,,7]),null==n||n.track("[GGL] Re-verification & claiming Started"),[4,t("GET","shopping-websites/site-verification/token",{correlationId:a,onResponse:u})];case 1:return[4,i.sent().json()];case 2:return r=i.sent().token,[4,e("setWebsiteVerificationMeta",{websiteVerificationMeta:r})];case 3:return i.sent(),[4,t("POST","shopping-websites/site-verification/verify",{correlationId:a,onResponse:u})];case 4:return i.sent(),[4,t("POST","shopping-websites/site-verification/claim".concat("?overwrite=false"),{correlationId:a,onResponse:u})];case 5:return i.sent(),console.info("Marketing with Google - Google Verification tag has been refreshed."),null==n||n.track("[GGL] Re-verification & claiming Succeeded"),[3,7];case 6:return s=i.sent(),console.error("Marketing with Google - Google Verification tag refresh failed.",s),null==n||n.track("[GGL] Re-verification & claiming Failed"),c.setTag("correlationId",a),o.captureException(s,c),[3,7];case 7:return[2]}}))}))};var u=function(t){return r(void 0,void 0,void 0,(function(){var e,n;return i(this,(function(r){switch(r.label){case 0:if(t.ok)return[3,5];e=new s.HttpClientError(t.statusText,t.status),r.label=1;case 1:return r.trys.push([1,3,,4]),[4,t.text()];case 2:return n=r.sent(),c.setExtra("responseContent",n),t.url.includes("shopping-websites/site-verification/claim")&&n.includes('"needOverwrite":true')?[2,t]:[3,4];case 3:return r.sent(),[3,4];case 4:throw c.setTransactionName(t.url),e;case 5:return[2,t]}}))}))}},3657:function(t,e){"use strict";var n,r=this&&this.__extends||(n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},n(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});e.__esModule=!0,e.HttpClientError=void 0;var i=function(t){function e(e,n){var r=t.call(this,e)||this;return r.code=n,r.name=r.constructor.name,r.message=e,r.code=n,r}return r(e,t),e}(Error);e.HttpClientError=i,e.default={HttpClientError:i}},638:function(t,e,n){"use strict";var r=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(i,o){function s(t){try{c(r.next(t))}catch(t){o(t)}}function a(t){try{c(r.throw(t))}catch(t){o(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((r=r.apply(t,e||[])).next())}))},i=this&&this.__generator||function(t,e){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,r=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((i=(i=s.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}};e.__esModule=!0,e.fetchOnboarding=e.initOnboardingClient=e.noCorrelationIdValue=void 0;var o=n(3657);e.noCorrelationIdValue="no-correlation-id-provided";var s={apiUrl:"",token:""};e.initOnboardingClient=function(t){s.apiUrl=t.apiUrl,s.token=t.token};var a=function(t){return r(void 0,void 0,void 0,(function(){return i(this,(function(e){if(!t.ok)throw new o.HttpClientError(t.statusText,t.status);return[2,t]}))}))};e.fetchOnboarding=function(t,n,o){return r(void 0,void 0,void 0,(function(){var r;return i(this,(function(i){switch(i.label){case 0:if(!s.apiUrl.length)throw new Error("Cannot call onboarding API, client is not initialized (missing URL)");if(!s.token.length)throw new Error("Cannot call onboarding API, client is not initialized (missing token)");return[4,fetch("".concat(s.apiUrl,"/").concat(n),{method:t,headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer ".concat(s.token),"x-correlation-id":(null==o?void 0:o.correlationId)||e.noCorrelationIdValue},body:(null==o?void 0:o.body)&&JSON.stringify(null==o?void 0:o.body)})];case 1:return r=i.sent(),[2,(null==o?void 0:o.onResponse)?null==o?void 0:o.onResponse(r):a(r)]}}))}))},e.default={initOnboardingClient:e.initOnboardingClient,fetchOnboarding:e.fetchOnboarding}},7979:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},r.apply(this,arguments)},i=this&&this.__awaiter||function(t,e,n,r){return new(n||(n=Promise))((function(i,o){function s(t){try{c(r.next(t))}catch(t){o(t)}}function a(t){try{c(r.throw(t))}catch(t){o(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((r=r.apply(t,e||[])).next())}))},o=this&&this.__generator||function(t,e){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,r=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((i=(i=s.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}};e.__esModule=!0,e.fetchShop=e.initShopClient=void 0;var s=n(3657),a={shopUrl:""};e.initShopClient=function(t){a.shopUrl=t.shopUrl,a.onShopSessionLoggedOut=t.onShopSessionLoggedOut},e.fetchShop=function(t,e,n){return i(void 0,void 0,void 0,(function(){var i;return o(this,(function(o){switch(o.label){case 0:if(!a.shopUrl.length)throw new Error("Cannot call action ".concat(t,", API is not initialized (missing shop URL)"));return[4,fetch(a.shopUrl,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(r({action:t},e)),signal:n})];case 1:if((i=o.sent()).redirected&&-1!==i.url.indexOf("AdminLogin"))throw a.onShopSessionLoggedOut&&a.onShopSessionLoggedOut(),new s.HttpClientError("Unauthorized",401);if(!i.ok)throw new s.HttpClientError(i.statusText,i.status);return[2,i.json()]}}))}))},e.default={initShopClient:e.initShopClient,fetchShop:e.fetchShop}},6309:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(e,n);i&&!("get"in i?!e.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return e[n]}}),Object.defineProperty(t,r,i)}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]});e.__esModule=!0,e.fetchShop=e.fetchOnboarding=e.HttpClientError=void 0,r(e,n(3657),"HttpClientError"),r(e,n(638),"fetchOnboarding"),r(e,n(7979),"fetchShop")},3866:(t,e,n)=>{"use strict";function r(t,e){return e=e||{},new Promise((function(n,r){var i=new XMLHttpRequest,o=[],s=[],a={},c=function(){return{ok:2==(i.status/100|0),statusText:i.statusText,status:i.status,url:i.responseURL,text:function(){return Promise.resolve(i.responseText)},json:function(){return Promise.resolve(i.responseText).then(JSON.parse)},blob:function(){return Promise.resolve(new Blob([i.response]))},clone:c,headers:{keys:function(){return o},entries:function(){return s},get:function(t){return a[t.toLowerCase()]},has:function(t){return t.toLowerCase()in a}}}};for(var u in i.open(e.method||"get",t,!0),i.onload=function(){i.getAllResponseHeaders().replace(/^(.*?):[^\S\n]*([\s\S]*?)$/gm,(function(t,e,n){o.push(e=e.toLowerCase()),s.push([e,n]),a[e]=a[e]?a[e]+","+n:n})),n(c())},i.onerror=r,i.withCredentials="include"==e.credentials,e.headers)i.setRequestHeader(u,e.headers[u]);i.send(e.body||null)}))}n.d(e,{A:()=>r})},8973:(t,e,n)=>{"use strict";n.d(e,{v4:()=>s});for(var r,i=256,o=[];i--;)o[i]=(i+256).toString(16).substring(1);function s(){var t,e=0,n="";if(!r||i+16>256){for(r=Array(e=256);e--;)r[e]=256*Math.random()|0;e=i=0}for(;e<16;e++)t=r[i+e],n+=6==e?o[15&t|64]:8==e?o[63&t|128]:o[t],1&e&&e>1&&e<11&&(n+="-");return i++,n}},7407:(t,e,n)=>{"use strict";function r(t,e,n){e.split&&(e=e.split("."));for(var r,i,o=0,s=e.length,a=t;o<s&&"__proto__"!==(i=e[o++])&&"constructor"!==i&&"prototype"!==i;)a=a[i]=o===s?n:typeof(r=a[i])==typeof e?r:0*e[o]!=0||~(""+e[o]).indexOf(".")?{}:[]}n.d(e,{J:()=>r})},1558:(t,e,n)=>{"use strict";function r(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)t[r]=n[r]}return t}n.d(e,{A:()=>i});const i=function t(e,n){function i(t,i,o){if("undefined"!=typeof document){"number"==typeof(o=r({},n,o)).expires&&(o.expires=new Date(Date.now()+864e5*o.expires)),o.expires&&(o.expires=o.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var s="";for(var a in o)o[a]&&(s+="; "+a,!0!==o[a]&&(s+="="+o[a].split(";")[0]));return document.cookie=t+"="+e.write(i,t)+s}}return Object.create({set:i,get:function(t){if("undefined"!=typeof document&&(!arguments.length||t)){for(var n=document.cookie?document.cookie.split("; "):[],r={},i=0;i<n.length;i++){var o=n[i].split("="),s=o.slice(1).join("=");try{var a=decodeURIComponent(o[0]);if(r[a]=e.read(s,a),t===a)break}catch(t){}}return t?r[t]:r}},remove:function(t,e){i(t,"",r({},e,{expires:-1}))},withAttributes:function(e){return t(this.converter,r({},this.attributes,e))},withConverter:function(e){return t(r({},this.converter,e),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(e)}})}({read:function(t){return'"'===t[0]&&(t=t.slice(1,-1)),t.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(t){return encodeURIComponent(t).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})},5450:(t,e,n)=>{"use strict";n.d(e,{C6:()=>i,Cl:()=>o,Tt:()=>s,YH:()=>c,fX:()=>u,sH:()=>a});var r=function(t,e){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},r(t,e)};function i(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var o=function(){return o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},o.apply(this,arguments)};function s(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n}function a(t,e,n,r){return new(n||(n=Promise))((function(i,o){function s(t){try{c(r.next(t))}catch(t){o(t)}}function a(t){try{c(r.throw(t))}catch(t){o(t)}}function c(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((r=r.apply(t,e||[])).next())}))}function c(t,e){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,r=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((i=(i=s.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function u(t,e,n){if(n||2===arguments.length)for(var r,i=0,o=e.length;i<o;i++)!r&&i in e||(r||(r=Array.prototype.slice.call(e,0,i)),r[i]=e[i]);return t.concat(r||Array.prototype.slice.call(e))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError}},o={};function s(t){var e=o[t];if(void 0!==e)return e.exports;var n=o[t]={id:t,loaded:!1,exports:{}};return i[t].call(n.exports,n,n.exports,s),n.loaded=!0,n.exports}s.m=i,s.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return s.d(e,{a:e}),e},e=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__,s.t=function(n,r){if(1&r&&(n=this(n)),8&r)return n;if("object"==typeof n&&n){if(4&r&&n.__esModule)return n;if(16&r&&"function"==typeof n.then)return n}var i=Object.create(null);s.r(i);var o={};t=t||[null,e({}),e([]),e(e)];for(var a=2&r&&n;"object"==typeof a&&!~t.indexOf(a);a=e(a))Object.getOwnPropertyNames(a).forEach((t=>o[t]=()=>n[t]));return o.default=()=>n,s.d(i,o),i},s.d=(t,e)=>{for(var n in e)s.o(e,n)&&!s.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},s.f={},s.e=t=>Promise.all(Object.keys(s.f).reduce(((e,n)=>(s.f[n](t,e),e)),[])),s.u=t=>(({10:"tsub-middleware",50:"ajs-destination",104:"schemaFilter",248:"auto-track",521:"remoteMiddleware",538:"queryString",694:"legacyVideos"}[t]||t)+".js"),s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),s.hmd=t=>((t=Object.create(t)).children||(t.children=[]),Object.defineProperty(t,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+t.id)}}),t),s.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n={},r="marketing-with-google-verification-tag:",s.l=(t,e,i,o)=>{if(n[t])n[t].push(e);else{var a,c;if(void 0!==i)for(var u=document.getElementsByTagName("script"),l=0;l<u.length;l++){var d=u[l];if(d.getAttribute("src")==t||d.getAttribute("data-webpack")==r+i){a=d;break}}a||(c=!0,(a=document.createElement("script")).charset="utf-8",a.timeout=120,s.nc&&a.setAttribute("nonce",s.nc),a.setAttribute("data-webpack",r+i),a.src=t),n[t]=[e];var p=(e,r)=>{a.onerror=a.onload=null,clearTimeout(h);var i=n[t];if(delete n[t],a.parentNode&&a.parentNode.removeChild(a),i&&i.forEach((t=>t(r))),e)return e(r)},h=setTimeout(p.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=p.bind(null,a.onerror),a.onload=p.bind(null,a.onload),c&&document.head.appendChild(a)}},s.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{var t;s.g.importScripts&&(t=s.g.location+"");var e=s.g.document;if(!t&&e&&(e.currentScript&&"SCRIPT"===e.currentScript.tagName.toUpperCase()&&(t=e.currentScript.src),!t)){var n=e.getElementsByTagName("script");if(n.length)for(var r=n.length-1;r>-1&&(!t||!/^http(s?):/.test(t));)t=n[r--].src}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),s.p=t})(),(()=>{var t={68:0};s.f.j=(e,n)=>{var r=s.o(t,e)?t[e]:void 0;if(0!==r)if(r)n.push(r[2]);else{var i=new Promise(((n,i)=>r=t[e]=[n,i]));n.push(r[2]=i);var o=s.p+s.u(e),a=new Error;s.l(o,(n=>{if(s.o(t,e)&&(0!==(r=t[e])&&(t[e]=void 0),r)){var i=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;a.message="Loading chunk "+e+" failed.\n("+i+": "+o+")",a.name="ChunkLoadError",a.type=i,a.request=o,r[1](a)}}),"chunk-"+e,e)}};var e=(e,n)=>{var r,i,[o,a,c]=n,u=0;if(o.some((e=>0!==t[e]))){for(r in a)s.o(a,r)&&(s.m[r]=a[r]);c&&c(s)}for(e&&e(n);u<o.length;u++)i=o[u],s.o(t,i)&&t[i]&&t[i][0](),t[i]=0},n=self.webpackChunkmarketing_with_google_verification_tag=self.webpackChunkmarketing_with_google_verification_tag||[];n.forEach(e.bind(null,0)),n.push=e.bind(null,n.push.bind(n))})(),(()=>{"use strict";var t=s(638),e=s(7979),n=s(2115),r=s(6009),i=s(817);(0,e.initShopClient)({shopUrl:window.psxMktgWithGoogleControllerLink}),(0,t.initOnboardingClient)({apiUrl:window.psxMktgWithGoogleApiUrl,token:window.psxMktgWithGoogleTokenPsAccounts}),window.psxMktgWithGoogleOnProductionEnvironment&&n.init({dsn:window.psxMktgWithGoogleDsnSentry,allowUrls:["https://storage.googleapis.com/psxmarketing-cdn/"],sampleRate:.5,tracesSampleRate:1,initialScope:{user:{id:window.psxMktgWithGoogleShopIdPsAccounts?window.psxMktgWithGoogleShopIdPsAccounts.toString():"unknown"}},release:"dev"});var o=function(){var t=i.AnalyticsBrowser.load({writeKey:window.psxMktgWithGoogleSegmentId},{disableClientPersistence:!0});return t.identify(window.psxMktgWithGoogleShopIdPsAccounts),t}();(0,r.runRetrievalOfVerificationTag)(t.fetchOnboarding,e.fetchShop,o)})()})();