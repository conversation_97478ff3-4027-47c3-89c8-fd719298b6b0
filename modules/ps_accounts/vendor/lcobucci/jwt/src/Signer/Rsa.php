<?php

/**
 * This file is part of Lcobucci\JWT, a simple library to handle JW<PERSON> and JWS
 *
 * @license http://opensource.org/licenses/BSD-3-Clause BSD-3-Clause
 */
namespace PrestaShop\Module\PsAccounts\Vendor\<PERSON><PERSON>bucci\JWT\Signer;

use const OPENSSL_KEYTYPE_RSA;
/**
 * Base class for RSASSA-PKCS1 signers
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.1.0
 */
abstract class Rsa extends OpenSSL
{
    public final function getKeyType()
    {
        return OPENSSL_KEYTYPE_RSA;
    }
}
