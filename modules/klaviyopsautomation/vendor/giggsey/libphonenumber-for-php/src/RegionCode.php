<?php

/*
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

namespace libphonenumber;

/**
 * Class containing string constants of region codes for easier testing.
 * @internal
 */
class RegionCode
{
    // Region code for global networks (e.g. +800 numbers).
    const UN001 = '001';
    const AD = 'AD';
    const AE = 'AE';
    const AM = 'AM';
    const AO = 'AO';
    const AQ = 'AQ';
    const AR = 'AR';
    const AU = 'AU';
    const BB = 'BB';
    const BR = 'BR';
    const BS = 'BS';
    const BY = 'BY';
    const CA = 'CA';
    const CH = 'CH';
    const CL = 'CL';
    const CN = 'CN';
    const CO = 'CO';
    const CS = 'CS';
    const CX = 'CX';
    const DE = 'DE';
    const FR = 'FR';
    const GB = 'GB';
    const HU = 'HU';
    const IT = 'IT';
    const JP = 'JP';
    const KR = 'KR';
    const MX = 'MX';
    const NZ = 'NZ';
    const PG = 'PG';
    const PL = 'PL';
    const RE = 'RE';
    const RU = 'RU';
    const SE = 'SE';
    const SG = 'SG';
    const US = 'US';
    const UZ = 'UZ';
    const YT = 'YT';
    const ZW = 'ZW';
    // Official code for the unknown region.
    const ZZ = 'ZZ';
}
