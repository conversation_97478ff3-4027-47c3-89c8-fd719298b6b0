<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  1901 => 'Tennessee',
  1901213 => 'Memphis, TN',
  1901226 => 'Memphis, TN',
  1901252 => 'Memphis, TN',
  1901259 => 'Memphis, TN',
  1901260 => 'Memphis, TN',
  1901266 => 'Memphis, TN',
  190127 => 'Memphis, TN',
  1901287 => 'Memphis, TN',
  1901308 => 'Memphis, TN',
  1901312 => 'Memphis, TN',
  1901320 => 'Memphis, TN',
  1901322 => 'Memphis, TN',
  1901323 => 'Memphis, TN',
  1901324 => 'Memphis, TN',
  1901327 => 'Memphis, TN',
  1901332 => 'Memphis, TN',
  1901345 => 'Memphis, TN',
  1901346 => 'Memphis, TN',
  1901348 => 'Memphis, TN',
  1901353 => 'Memphis, TN',
  1901357 => 'Memphis, TN',
  1901358 => 'Memphis, TN',
  190136 => 'Memphis, TN',
  190137 => 'Memphis, TN',
  190138 => 'Memphis, TN',
  1901396 => 'Memphis, TN',
  1901398 => 'Memphis, TN',
  1901405 => 'Memphis, TN',
  1901416 => 'Memphis, TN',
  1901417 => 'Memphis, TN',
  1901433 => 'Memphis, TN',
  1901435 => 'Memphis, TN',
  1901448 => 'Memphis, TN',
  1901452 => 'Memphis, TN',
  1901454 => 'Memphis, TN',
  1901457 => 'Collierville, TN',
  1901458 => 'Memphis, TN',
  1901465 => 'Somerville, TN',
  1901475 => 'Covington, TN',
  1901476 => 'Covington, TN',
  1901495 => 'Memphis, TN',
  1901507 => 'Memphis, TN',
  1901516 => 'Memphis, TN',
  190152 => 'Memphis, TN',
  1901542 => 'Memphis, TN',
  1901543 => 'Memphis, TN',
  1901544 => 'Memphis, TN',
  1901545 => 'Memphis, TN',
  1901546 => 'Memphis, TN',
  1901552 => 'Memphis, TN',
  1901565 => 'Memphis, TN',
  1901576 => 'Memphis, TN',
  1901577 => 'Memphis, TN',
  1901578 => 'Memphis, TN',
  1901590 => 'Memphis, TN',
  1901595 => 'Memphis, TN',
  1901672 => 'Memphis, TN',
  190168 => 'Memphis, TN',
  1901722 => 'Memphis, TN',
  1901725 => 'Memphis, TN',
  1901726 => 'Memphis, TN',
  1901729 => 'Memphis, TN',
  1901730 => 'Memphis, TN',
  1901743 => 'Memphis, TN',
  1901744 => 'Memphis, TN',
  1901746 => 'Memphis, TN',
  1901747 => 'Memphis, TN',
  1901748 => 'Memphis, TN',
  1901761 => 'Memphis, TN',
  1901763 => 'Memphis, TN',
  1901765 => 'Memphis, TN',
  1901766 => 'Memphis, TN',
  1901767 => 'Memphis, TN',
  1901774 => 'Memphis, TN',
  1901775 => 'Memphis, TN',
  1901785 => 'Memphis, TN',
  1901789 => 'Memphis, TN',
  1901791 => 'Memphis, TN',
  1901794 => 'Memphis, TN',
  1901795 => 'Memphis, TN',
  1901797 => 'Memphis, TN',
  1901818 => 'Memphis, TN',
  1901820 => 'Memphis, TN',
  1901821 => 'Memphis, TN',
  1901850 => 'Collierville, TN',
  1901853 => 'Collierville, TN',
  1901854 => 'Collierville, TN',
  1901861 => 'Collierville, TN',
  1901866 => 'Memphis, TN',
  1901867 => 'Arlington, TN',
  1901872 => 'Millington, TN',
  1901873 => 'Millington, TN',
  1901881 => 'Memphis, TN',
  1901937 => 'Memphis, TN',
  1901942 => 'Memphis, TN',
  1901946 => 'Memphis, TN',
  1901947 => 'Memphis, TN',
  1901948 => 'Memphis, TN',
  1902 => 'Nova Scotia/Prince Edward Island',
  1902224 => 'Chéticamp, NS',
  1902245 => 'Digby, NS',
  1902254 => 'Parrsboro, NS',
  1902275 => 'Chester, NS',
  1902295 => 'Baddeck, NS',
  1902354 => 'Liverpool, NS',
  1902367 => 'Charlottetown, PE',
  1902368 => 'Charlottetown, PE',
  1902370 => 'Charlottetown, PE',
  1902393 => 'Charlottetown, PE',
  1902396 => 'New Glasgow, NS',
  1902404 => 'Halifax, NS',
  1902405 => 'Halifax, NS',
  1902406 => 'Halifax, NS',
  1902407 => 'Halifax, NS',
  190242 => 'Halifax, NS',
  1902431 => 'Halifax, NS',
  1902434 => 'Dartmouth, NS',
  1902435 => 'Dartmouth, NS',
  1902436 => 'Summerside, PE',
  1902442 => 'Halifax, NS',
  1902443 => 'Halifax, NS',
  1902444 => 'Halifax, NS',
  1902445 => 'Halifax, NS',
  1902446 => 'Halifax, NS',
  190245 => 'Halifax, NS',
  190246 => 'Dartmouth, NS',
  1902471 => 'Halifax, NS',
  1902477 => 'Halifax, NS',
  1902479 => 'Halifax, NS',
  1902481 => 'Dartmouth, NS',
  1902482 => 'Halifax, NS',
  1902485 => 'Pictou, NS',
  1902488 => 'Halifax, NS',
  1902492 => 'Halifax, NS',
  1902494 => 'Halifax, NS',
  1902497 => 'Halifax, NS',
  1902499 => 'Halifax, NS',
  1902527 => 'Bridgewater, NS',
  1902530 => 'Bridgewater, NS',
  1902532 => 'Annapolis Royal, NS',
  1902535 => 'St. Peter\'s, NS',
  1902538 => 'Berwick, NS',
  1902539 => 'Sydney, NS',
  1902542 => 'Wolfville, NS',
  1902543 => 'Bridgewater, NS',
  1902562 => 'Sydney, NS',
  1902564 => 'Sydney, NS',
  1902566 => 'Charlottetown, PE',
  1902567 => 'Sydney, NS',
  1902569 => 'Charlottetown, PE',
  1902582 => 'Canning, NS',
  1902624 => 'Mahone Bay, NS',
  1902625 => 'Port Hawkesbury, NS',
  1902626 => 'Charlottetown, PE',
  1902628 => 'Charlottetown, PE',
  1902629 => 'Charlottetown, PE',
  1902634 => 'Lunenburg, NS',
  1902637 => 'Barrington, NS',
  1902657 => 'Tatamagouche, NS',
  1902661 => 'Amherst, NS',
  1902662 => 'Debert, NS',
  1902665 => 'Bridgetown, NS',
  1902667 => 'Amherst, NS',
  1902678 => 'Kentville, NS',
  1902679 => 'Kentville, NS',
  1902687 => 'Souris, PE',
  1902695 => 'New Glasgow, NS',
  1902736 => 'North Sydney, NS',
  1902742 => 'Yarmouth, NS',
  1902752 => 'New Glasgow, NS',
  1902755 => 'New Glasgow, NS',
  1902758 => 'Shubenacadie, NS',
  1902762 => 'Pubnico, NS',
  1902765 => 'Kingston, NS',
  1902769 => 'Saulnierville, NS',
  1902794 => 'North Sydney, NS',
  1902798 => 'Windsor, NS',
  1902825 => 'Middleton, NS',
  1902826 => 'St Margaret Village, NS',
  1902827 => 'Chezzetcook, NS',
  1902830 => 'Halifax, NS',
  1902836 => 'Kensington, PE',
  1902837 => 'Weymouth, NS',
  1902838 => 'Montague, PE',
  1902842 => 'Glace Bay, NS',
  1902843 => 'Truro, NS',
  1902849 => 'Glace Bay, NS',
  1902853 => 'Alberton, PE',
  1902857 => 'Hubbards, NS',
  1902859 => 'O\'Leary, PE',
  1902860 => 'Waverley, NS',
  1902861 => 'Waverley, NS',
  1902862 => 'New Waterford, NS',
  1902863 => 'Antigonish, NS',
  1902875 => 'Shelburne, NS',
  1902876 => 'Halifax, NS',
  1902882 => 'Tignish, PE',
  1902883 => 'Elmsdale, NS',
  1902888 => 'Summerside, PE',
  1902889 => 'Musquodoboit Harbour, NS',
  1902892 => 'Charlottetown, PE',
  1902893 => 'Truro, NS',
  1902894 => 'Charlottetown, PE',
  1902895 => 'Truro, NS',
  1902897 => 'Truro, NS',
  1902963 => 'Rusticoville, PE',
  1903 => 'Texas',
  1903212 => 'Longview, TX',
  1903223 => 'Texarkana, TX',
  1903234 => 'Longview, TX',
  1903236 => 'Longview, TX',
  1903237 => 'Longview, TX',
  1903238 => 'Longview, TX',
  1903291 => 'Longview, TX',
  1903295 => 'Longview, TX',
  1903297 => 'Longview, TX',
  1903315 => 'Longview, TX',
  1903322 => 'Buffalo, TX',
  1903334 => 'Texarkana, TX',
  1903342 => 'Winnsboro, TX',
  1903356 => 'Quinlan, TX',
  1903364 => 'Whitewright, TX',
  1903378 => 'Honey Grove, TX',
  1903383 => 'Yantis, TX',
  1903389 => 'Fairfield, TX',
  1903395 => 'Cooper, TX',
  1903408 => 'Greenville, TX',
  1903416 => 'Denison, TX',
  1903427 => 'Clarksville, TX',
  1903438 => 'Sulphur Springs, TX',
  1903439 => 'Sulphur Springs, TX',
  1903447 => 'Quinlan, TX',
  1903450 => 'Greenville, TX',
  1903451 => 'Mabank, TX',
  1903454 => 'Greenville, TX',
  1903455 => 'Greenville, TX',
  1903463 => 'Denison, TX',
  1903465 => 'Denison, TX',
  1903473 => 'Emory, TX',
  1903482 => 'Van Alstyne, TX',
  1903489 => 'Malakoff, TX',
  1903498 => 'Kemp, TX',
  1903509 => 'Tyler, TX',
  1903510 => 'Tyler, TX',
  1903525 => 'Tyler, TX',
  1903526 => 'Tyler, TX',
  1903527 => 'Caddo Mills, TX',
  1903531 => 'Tyler, TX',
  1903533 => 'Tyler, TX',
  1903534 => 'Tyler, TX',
  1903535 => 'Tyler, TX',
  1903536 => 'Centerville, TX',
  1903537 => 'Mount Vernon, TX',
  1903547 => 'Hooks, TX',
  1903553 => 'Longview, TX',
  1903561 => 'Tyler, TX',
  1903564 => 'Whitesboro, TX',
  1903566 => 'Tyler, TX',
  1903567 => 'Canton, TX',
  1903569 => 'Mineola, TX',
  1903572 => 'Mount Pleasant, TX',
  1903575 => 'Mount Pleasant, TX',
  1903577 => 'Mount Pleasant, TX',
  1903581 => 'Tyler, TX',
  1903583 => 'Bonham, TX',
  1903586 => 'Jacksonville, TX',
  1903587 => 'Leonard, TX',
  1903589 => 'Jacksonville, TX',
  1903592 => 'Tyler, TX',
  1903593 => 'Tyler, TX',
  1903595 => 'Tyler, TX',
  1903596 => 'Tyler, TX',
  1903597 => 'Tyler, TX',
  1903614 => 'Texarkana, TX',
  1903626 => 'Jewett, TX',
  1903628 => 'New Boston, TX',
  1903636 => 'Big Sandy, TX',
  1903639 => 'Hughes Springs, TX',
  1903640 => 'Bonham, TX',
  1903641 => 'Corsicana, TX',
  1903643 => 'Longview, TX',
  1903645 => 'Daingerfield, TX',
  1903654 => 'Corsicana, TX',
  1903655 => 'Henderson, TX',
  1903657 => 'Henderson, TX',
  1903663 => 'Longview, TX',
  1903665 => 'Jefferson, TX',
  1903667 => 'De Kalb, TX',
  1903668 => 'Hallsville, TX',
  1903670 => 'Athens, TX',
  1903675 => 'Athens, TX',
  1903677 => 'Athens, TX',
  1903683 => 'Rusk, TX',
  1903687 => 'Waskom, TX',
  1903693 => 'Carthage, TX',
  1903694 => 'Carthage, TX',
  1903723 => 'Palestine, TX',
  1903729 => 'Palestine, TX',
  1903731 => 'Palestine, TX',
  1903734 => 'Gilmer, TX',
  1903737 => 'Paris, TX',
  1903739 => 'Paris, TX',
  1903753 => 'Longview, TX',
  1903756 => 'Linden, TX',
  1903757 => 'Longview, TX',
  1903758 => 'Longview, TX',
  1903759 => 'Longview, TX',
  1903763 => 'Quitman, TX',
  1903769 => 'Hawkins, TX',
  1903783 => 'Paris, TX',
  1903784 => 'Paris, TX',
  1903785 => 'Paris, TX',
  1903786 => 'Pottsboro, TX',
  1903791 => 'Texarkana, TX',
  1903792 => 'Texarkana, TX',
  1903793 => 'Texarkana, TX',
  1903794 => 'Texarkana, TX',
  1903796 => 'Atlanta, TX',
  1903798 => 'Texarkana, TX',
  1903799 => 'Atlanta, TX',
  1903813 => 'Sherman, TX',
  1903825 => 'Flint, TX',
  1903831 => 'Texarkana, TX',
  1903832 => 'Texarkana, TX',
  1903834 => 'Overton, TX',
  1903838 => 'Texarkana, TX',
  1903839 => 'Whitehouse, TX',
  1903842 => 'Troup, TX',
  1903843 => 'Gilmer, TX',
  1903845 => 'Gladewater, TX',
  1903849 => 'Chandler, TX',
  1903852 => 'Brownsboro, TX',
  1903856 => 'Pittsburg, TX',
  1903868 => 'Sherman, TX',
  1903870 => 'Sherman, TX',
  1903872 => 'Corsicana, TX',
  1903873 => 'Wills Point, TX',
  1903874 => 'Corsicana, TX',
  1903875 => 'Corsicana, TX',
  1903876 => 'Frankston, TX',
  1903877 => 'Tyler, TX',
  1903881 => 'Lindale, TX',
  1903882 => 'Lindale, TX',
  1903883 => 'Greenville, TX',
  1903885 => 'Sulphur Springs, TX',
  1903886 => 'Commerce, TX',
  1903887 => 'Gun Barrel City, TX',
  1903891 => 'Sherman, TX',
  1903892 => 'Sherman, TX',
  1903893 => 'Sherman, TX',
  1903894 => 'Bullard, TX',
  1903896 => 'Edgewood, TX',
  1903923 => 'Marshall, TX',
  1903927 => 'Marshall, TX',
  1903934 => 'Marshall, TX',
  1903935 => 'Marshall, TX',
  1903938 => 'Marshall, TX',
  1903939 => 'Tyler, TX',
  1903947 => 'Tatum, TX',
  1903962 => 'Grand Saline, TX',
  1903963 => 'Van, TX',
  1903965 => 'Bells, TX',
  1903968 => 'Ore City, TX',
  1903983 => 'Kilgore, TX',
  1903984 => 'Kilgore, TX',
  1903986 => 'Kilgore, TX',
  1904 => 'Florida',
  1904202 => 'Jacksonville, FL',
  1904209 => 'St. Augustine, FL',
  1904213 => 'Orange Park, FL',
  1904215 => 'Orange Park, FL',
  1904217 => 'St. Augustine, FL',
  1904220 => 'Jacksonville, FL',
  1904221 => 'Jacksonville, FL',
  1904223 => 'Jacksonville, FL',
  1904225 => 'Yulee, FL',
  1904232 => 'Jacksonville, FL',
  1904240 => 'Jacksonville, FL',
  1904244 => 'Jacksonville, FL',
  1904253 => 'Jacksonville, FL',
  1904259 => 'Macclenny, FL',
  1904260 => 'Jacksonville, FL',
  1904261 => 'Fernandina Beach, FL',
  1904262 => 'Jacksonville, FL',
  1904264 => 'Orange Park, FL',
  1904268 => 'Jacksonville, FL',
  1904269 => 'Orange Park, FL',
  1904272 => 'Orange Park, FL',
  1904273 => 'Ponte Vedra Bch, FL',
  1904276 => 'Orange Park, FL',
  1904277 => 'Fernandina Beach, FL',
  1904278 => 'Orange Park, FL',
  1904280 => 'Ponte Vedra Bch, FL',
  1904281 => 'Jacksonville, FL',
  1904282 => 'Middleburg, FL',
  1904284 => 'Green Cove Spgs, FL',
  1904285 => 'Ponte Vedra Bch, FL',
  1904288 => 'Jacksonville, FL',
  1904291 => 'Middleburg, FL',
  1904292 => 'Jacksonville, FL',
  1904296 => 'Jacksonville, FL',
  1904298 => 'Orange Park, FL',
  1904306 => 'Jacksonville, FL',
  1904308 => 'Jacksonville, FL',
  1904310 => 'Fernandina Beach, FL',
  1904317 => 'Jacksonville, FL',
  1904321 => 'Fernandina Beach, FL',
  1904329 => 'Jacksonville, FL',
  1904332 => 'Jacksonville, FL',
  1904338 => 'Jacksonville, FL',
  1904342 => 'St. Augustine, FL',
  1904346 => 'Jacksonville, FL',
  1904347 => 'St. Augustine, FL',
  1904348 => 'Jacksonville, FL',
  190435 => 'Jacksonville, FL',
  1904363 => 'Jacksonville, FL',
  1904367 => 'Jacksonville, FL',
  1904368 => 'Starke, FL',
  1904371 => 'Jacksonville, FL',
  1904374 => 'Jacksonville, FL',
  1904375 => 'Orange Park, FL',
  1904378 => 'Jacksonville, FL',
  1904379 => 'Jacksonville, FL',
  190438 => 'Jacksonville, FL',
  1904394 => 'Jacksonville, FL',
  1904396 => 'Jacksonville, FL',
  1904398 => 'Jacksonville, FL',
  1904399 => 'Jacksonville, FL',
  1904406 => 'Middleburg, FL',
  1904419 => 'Jacksonville, FL',
  1904421 => 'Jacksonville, FL',
  1904425 => 'Jacksonville, FL',
  1904448 => 'Jacksonville, FL',
  1904460 => 'St. Augustine, FL',
  1904461 => 'St. Augustine, FL',
  1904471 => 'St. Augustine, FL',
  1904491 => 'Fernandina Beach, FL',
  1904493 => 'Jacksonville, FL',
  1904501 => 'St. Augustine, FL',
  1904503 => 'Jacksonville, FL',
  1904519 => 'Jacksonville, FL',
  1904527 => 'Jacksonville, FL',
  1904529 => 'Green Cove Spgs, FL',
  1904538 => 'Jacksonville, FL',
  1904540 => 'St. Augustine, FL',
  1904541 => 'Orange Park, FL',
  1904542 => 'Jacksonville, FL',
  1904548 => 'Yulee, FL',
  1904551 => 'Jacksonville, FL',
  1904564 => 'Jacksonville, FL',
  1904565 => 'Jacksonville, FL',
  1904573 => 'Jacksonville, FL',
  1904579 => 'Orange Park, FL',
  1904598 => 'Jacksonville, FL',
  1904619 => 'Jacksonville, FL',
  1904620 => 'Jacksonville, FL',
  1904630 => 'Jacksonville, FL',
  1904632 => 'Jacksonville, FL',
  1904633 => 'Jacksonville, FL',
  1904634 => 'Jacksonville, FL',
  1904636 => 'Jacksonville, FL',
  1904641 => 'Jacksonville, FL',
  1904642 => 'Jacksonville, FL',
  1904644 => 'Orange Park, FL',
  1904645 => 'Jacksonville, FL',
  1904646 => 'Jacksonville, FL',
  1904647 => 'Jacksonville, FL',
  1904652 => 'Jacksonville, FL',
  1904669 => 'St. Augustine, FL',
  1904674 => 'Jacksonville, FL',
  1904683 => 'Jacksonville, FL',
  1904687 => 'St. Augustine, FL',
  1904692 => 'Hastings, FL',
  1904693 => 'Jacksonville, FL',
  1904695 => 'Jacksonville, FL',
  1904696 => 'Jacksonville, FL',
  1904697 => 'Jacksonville, FL',
  1904714 => 'Jacksonville, FL',
  190472 => 'Jacksonville, FL',
  190473 => 'Jacksonville, FL',
  1904741 => 'Jacksonville, FL',
  1904743 => 'Jacksonville, FL',
  1904744 => 'Jacksonville, FL',
  1904745 => 'Jacksonville, FL',
  1904751 => 'Jacksonville, FL',
  1904757 => 'Jacksonville, FL',
  1904764 => 'Jacksonville, FL',
  1904765 => 'Jacksonville, FL',
  1904766 => 'Jacksonville, FL',
  1904768 => 'Jacksonville, FL',
  1904771 => 'Jacksonville, FL',
  1904772 => 'Jacksonville, FL',
  1904777 => 'Jacksonville, FL',
  1904778 => 'Jacksonville, FL',
  1904779 => 'Jacksonville, FL',
  1904781 => 'Jacksonville, FL',
  1904783 => 'Jacksonville, FL',
  1904786 => 'Jacksonville, FL',
  1904794 => 'St. Augustine, FL',
  1904797 => 'St. Augustine, FL',
  1904807 => 'Jacksonville, FL',
  1904808 => 'St. Augustine, FL',
  1904810 => 'St. Augustine, FL',
  1904814 => 'St. Augustine, FL',
  1904819 => 'St. Augustine, FL',
  190482 => 'St. Augustine, FL',
  1904821 => 'Jacksonville, FL',
  1904845 => 'Hilliard, FL',
  1904854 => 'Jacksonville, FL',
  1904858 => 'Jacksonville, FL',
  1904879 => 'Callahan, FL',
  1904880 => 'Jacksonville, FL',
  1904886 => 'Jacksonville, FL',
  1904900 => 'Jacksonville, FL',
  1904908 => 'Jacksonville, FL',
  1904924 => 'Jacksonville, FL',
  1904928 => 'Jacksonville, FL',
  1904940 => 'St. Augustine, FL',
  1904953 => 'Jacksonville, FL',
  1904964 => 'Starke, FL',
  1904992 => 'Jacksonville, FL',
  1904996 => 'Jacksonville, FL',
  1904997 => 'Jacksonville, FL',
  1904998 => 'Jacksonville, FL',
  19052 => 'Ontario',
  1905201 => 'Markham, ON',
  1905206 => 'Mississauga, ON',
  1905209 => 'Markham, ON',
  1905212 => 'Mississauga, ON',
  1905216 => 'Brampton, ON',
  1905227 => 'Thorold, ON',
  1905230 => 'Brampton, ON',
  1905231 => 'Ajax, ON',
  1905232 => 'Mississauga, ON',
  1905235 => 'Newmarket, ON',
  1905237 => 'Richmond Hill, ON',
  1905238 => 'Mississauga, ON',
  1905239 => 'Ajax, ON',
  1905240 => 'Oshawa, ON',
  1905257 => 'Oakville, ON',
  1905263 => 'Hampton, ON',
  1905264 => 'Woodbridge, ON',
  1905265 => 'Woodbridge, ON',
  1905266 => 'Woodbridge, ON',
  1905267 => 'Mississauga, ON',
  1905268 => 'Mississauga, ON',
  190527 => 'Mississauga, ON',
  1905281 => 'Mississauga, ON',
  1905282 => 'Mississauga, ON',
  1905286 => 'Mississauga, ON',
  1905290 => 'Mississauga, ON',
  1905294 => 'Markham, ON',
  1905295 => 'Niagara Falls, ON',
  1905296 => 'Hamilton, ON',
  1905297 => 'Hamilton, ON',
  1905300 => 'Ontario',
  1905301 => 'Ontario',
  1905302 => 'Ontario',
  1905303 => 'Maple, ON',
  1905304 => 'Ancaster, ON',
  1905305 => 'Markham, ON',
  1905306 => 'Mississauga, ON',
  1905307 => 'Ontario',
  1905308 => 'Hamilton, ON',
  1905309 => 'Grimsby, ON',
  190531 => 'Ontario',
  1905312 => 'Hamilton, ON',
  1905315 => 'Burlington, ON',
  1905318 => 'Hamilton, ON',
  1905319 => 'Burlington, ON',
  190532 => 'Ontario',
  1905330 => 'Ontario',
  1905331 => 'Burlington, ON',
  1905332 => 'Burlington, ON',
  1905333 => 'Burlington, ON',
  1905334 => 'Ontario',
  1905335 => 'Burlington, ON',
  1905336 => 'Burlington, ON',
  1905337 => 'Oakville, ON',
  1905338 => 'Oakville, ON',
  1905339 => 'Oakville, ON',
  190534 => 'Ontario',
  1905346 => 'St. Catharines, ON',
  1905350 => 'Ontario',
  1905351 => 'Ontario',
  1905352 => 'Ontario',
  1905353 => 'Niagara Falls, ON',
  1905354 => 'Niagara Falls, ON',
  1905355 => 'Colborne, ON',
  1905356 => 'Niagara Falls, ON',
  1905357 => 'Niagara Falls, ON',
  1905358 => 'Niagara Falls, ON',
  1905359 => 'Ontario',
  190536 => 'Ontario',
  1905362 => 'Mississauga, ON',
  1905366 => 'Mississauga, ON',
  1905370 => 'Ontario',
  1905371 => 'Niagara Falls, ON',
  1905372 => 'Cobourg, ON',
  1905373 => 'Cobourg, ON',
  1905374 => 'Niagara Falls, ON',
  1905375 => 'Ontario',
  1905376 => 'Ontario',
  1905377 => 'Cobourg, ON',
  1905378 => 'Ontario',
  1905379 => 'Ontario',
  1905380 => 'Ontario',
  1905381 => 'Ontario',
  1905382 => 'Stevensville, ON',
  1905383 => 'Hamilton, ON',
  1905384 => 'Ontario',
  1905385 => 'Hamilton, ON',
  1905386 => 'Ontario',
  1905387 => 'Hamilton, ON',
  1905388 => 'Hamilton, ON',
  1905389 => 'Hamilton, ON',
  190539 => 'Ontario',
  1905397 => 'St. Catharines, ON',
  190540 => 'Ontario',
  1905403 => 'Mississauga, ON',
  1905404 => 'Oshawa, ON',
  1905405 => 'Mississauga, ON',
  190541 => 'Ontario',
  1905415 => 'Markham, ON',
  1905417 => 'Maple, ON',
  190542 => 'Ontario',
  1905420 => 'Pickering, ON',
  1905426 => 'Ajax, ON',
  1905427 => 'Ajax, ON',
  1905428 => 'Ajax, ON',
  1905430 => 'Whitby, ON',
  1905431 => 'Ontario',
  1905432 => 'Oshawa, ON',
  1905433 => 'Oshawa, ON',
  1905434 => 'Oshawa, ON',
  1905435 => 'Ontario',
  1905436 => 'Oshawa, ON',
  1905437 => 'Ontario',
  1905438 => 'Oshawa, ON',
  1905439 => 'Ontario',
  190544 => 'Ontario',
  1905448 => 'Oshawa, ON',
  190545 => 'Brampton, ON',
  1905460 => 'Brampton, ON',
  1905461 => 'Mississauga, ON',
  1905462 => 'Ontario',
  1905463 => 'Brampton, ON',
  1905464 => 'Ontario',
  1905465 => 'Oakville, ON',
  1905466 => 'Ontario',
  1905467 => 'Ontario',
  1905468 => 'Niagara-on-the-Lake, ON',
  1905469 => 'Oakville, ON',
  190547 => 'Markham, ON',
  1905473 => 'Mount Albert, ON',
  1905476 => 'Keswick, ON',
  1905478 => 'Queensville, ON',
  190548 => 'Ontario',
  1905480 => 'Markham, ON',
  1905487 => 'Brampton, ON',
  1905488 => 'Brampton, ON',
  1905489 => 'Markham, ON',
  1905490 => 'Ontario',
  1905491 => 'Ontario',
  1905492 => 'Pickering, ON',
  1905493 => 'Whitby, ON',
  1905494 => 'Brampton, ON',
  1905495 => 'Brampton, ON',
  1905496 => 'Ontario',
  1905497 => 'Brampton, ON',
  1905498 => 'Ontario',
  1905499 => 'Ontario',
  1905500 => 'Ontario',
  1905501 => 'Mississauga, ON',
  1905502 => 'Mississauga, ON',
  1905503 => 'Aurora, ON',
  1905504 => 'Ontario',
  1905505 => 'Ontario',
  1905506 => 'Ontario',
  1905507 => 'Mississauga, ON',
  1905508 => 'Richmond Hill, ON',
  1905509 => 'Pickering, ON',
  190551 => 'Ontario',
  1905513 => 'Markham, ON',
  190552 => 'Hamilton, ON',
  1905520 => 'Ontario',
  190553 => 'Ontario',
  1905538 => 'Hamilton, ON',
  190554 => 'Hamilton, ON',
  1905541 => 'Ontario',
  1905542 => 'Mississauga, ON',
  190555 => 'Ontario',
  1905554 => 'Markham, ON',
  190556 => 'Mississauga, ON',
  1905560 => 'Hamilton, ON',
  1905561 => 'Hamilton, ON',
  1905562 => 'Vineland, ON',
  1905563 => 'Beamsville, ON',
  190557 => 'Hamilton, ON',
  1905570 => 'Ontario',
  1905571 => 'Oshawa, ON',
  1905576 => 'Oshawa, ON',
  1905579 => 'Oshawa, ON',
  190558 => 'Ontario',
  1905582 => 'Oakville, ON',
  190559 => 'Ontario',
  1905592 => 'Burlington, ON',
  1905593 => 'Mississauga, ON',
  1905595 => 'Brampton, ON',
  1905600 => 'Ontario',
  1905601 => 'Ontario',
  1905602 => 'Mississauga, ON',
  1905603 => 'Ontario',
  1905604 => 'Markham, ON',
  1905605 => 'Woodbridge, ON',
  1905606 => 'Ontario',
  1905607 => 'Mississauga, ON',
  1905608 => 'Mississauga, ON',
  1905609 => 'Ontario',
  190561 => 'Ontario',
  1905612 => 'Mississauga, ON',
  1905614 => 'Mississauga, ON',
  1905615 => 'Mississauga, ON',
  1905619 => 'Ajax, ON',
  1905620 => 'Ontario',
  1905621 => 'Ontario',
  1905622 => 'Ontario',
  1905623 => 'Bowmanville, ON',
  1905624 => 'Mississauga, ON',
  1905625 => 'Mississauga, ON',
  1905626 => 'Ontario',
  1905627 => 'Dundas, ON',
  1905628 => 'Dundas, ON',
  1905629 => 'Mississauga, ON',
  190563 => 'Burlington, ON',
  1905630 => 'Ontario',
  1905636 => 'Milton, ON',
  1905638 => 'Ontario',
  1905640 => 'Whitchurch-Stouffville, ON',
  1905641 => 'St. Catharines, ON',
  1905642 => 'Whitchurch-Stouffville, ON',
  1905643 => 'Stoney Creek, ON',
  1905644 => 'Ontario',
  1905645 => 'Ontario',
  1905646 => 'St. Catharines, ON',
  1905647 => 'Ontario',
  1905648 => 'Ancaster, ON',
  1905649 => 'Claremont, ON',
  190565 => 'Ontario',
  1905655 => 'Brooklin, ON',
  1905659 => 'Freelton, ON',
  1905660 => 'Concord, ON',
  1905661 => 'Ontario',
  1905662 => 'Stoney Creek, ON',
  1905663 => 'Ontario',
  1905664 => 'Stoney Creek, ON',
  1905665 => 'Whitby, ON',
  1905666 => 'Whitby, ON',
  1905667 => 'Hamilton, ON',
  1905668 => 'Whitby, ON',
  1905669 => 'Concord, ON',
  190567 => 'Mississauga, ON',
  1905674 => 'Ontario',
  1905675 => 'Ontario',
  1905679 => 'Mount Hope, ON',
  1905680 => 'Ontario',
  1905681 => 'Burlington, ON',
  1905682 => 'St. Catharines, ON',
  1905683 => 'Ajax, ON',
  1905684 => 'St. Catharines, ON',
  1905685 => 'St. Catharines, ON',
  1905686 => 'Ajax, ON',
  1905687 => 'St. Catharines, ON',
  1905688 => 'St. Catharines, ON',
  1905689 => 'Waterdown, ON',
  1905690 => 'Waterdown, ON',
  1905691 => 'Ontario',
  1905692 => 'Binbrook, ON',
  1905693 => 'Milton, ON',
  1905694 => 'Ontario',
  1905695 => 'Ontario',
  1905696 => 'Mississauga, ON',
  1905697 => 'Bowmanville, ON',
  1905698 => 'Ontario',
  1905699 => 'Ontario',
  190570 => 'Ontario',
  1905701 => 'Dunnville, ON',
  1905702 => 'Georgetown, ON',
  1905704 => 'St. Catharines, ON',
  190571 => 'Ontario',
  1905712 => 'Mississauga, ON',
  1905713 => 'Aurora, ON',
  1905714 => 'Welland, ON',
  1905715 => 'Newmarket, ON',
  1905720 => 'Oshawa, ON',
  1905721 => 'Oshawa, ON',
  1905722 => 'Sutton West, ON',
  1905723 => 'Oshawa, ON',
  1905724 => 'Ontario',
  1905725 => 'Oshawa, ON',
  1905726 => 'Aurora, ON',
  1905727 => 'Aurora, ON',
  1905728 => 'Oshawa, ON',
  1905729 => 'Beeton, ON',
  1905730 => 'Ontario',
  1905731 => 'Thornhill, ON',
  1905732 => 'Welland, ON',
  1905733 => 'Ontario',
  1905734 => 'Welland, ON',
  1905735 => 'Welland, ON',
  1905736 => 'Ontario',
  1905737 => 'Richmond Hill, ON',
  1905738 => 'Concord, ON',
  1905739 => 'Ontario',
  190574 => 'Ontario',
  190575 => 'Ontario',
  1905751 => 'Aurora, ON',
  1905752 => 'Markham, ON',
  190576 => 'Ontario',
  1905760 => 'Concord, ON',
  1905761 => 'Concord, ON',
  1905765 => 'Caledonia, ON',
  1905768 => 'Hagersville, ON',
  1905770 => 'Richmond Hill, ON',
  1905771 => 'Ontario',
  1905772 => 'Cayuga, ON',
  1905773 => 'Ontario',
  1905774 => 'Dunnville, ON',
  1905775 => 'Bradford, ON',
  1905776 => 'Ontario',
  1905777 => 'Hamilton, ON',
  1905778 => 'Bradford, ON',
  1905779 => 'Ontario',
  1905780 => 'Richmond Hill, ON',
  1905781 => 'Ontario',
  1905782 => 'Ontario',
  1905783 => 'Ontario',
  1905784 => 'Ontario',
  1905785 => 'Mississauga, ON',
  1905786 => 'Ontario',
  1905787 => 'Richmond Hill, ON',
  1905788 => 'Welland, ON',
  1905789 => 'Brampton, ON',
  190579 => 'Brampton, ON',
  1905794 => 'Castlemore, ON',
  1905795 => 'Mississauga, ON',
  1905797 => 'Ontario',
  1905798 => 'Ontario',
  190580 => 'Ontario',
  1905803 => 'Mississauga, ON',
  1905804 => 'Mississauga, ON',
  1905810 => 'Ontario',
  1905811 => 'Ontario',
  1905812 => 'Mississauga, ON',
  1905813 => 'Mississauga, ON',
  1905814 => 'Mississauga, ON',
  1905815 => 'Oakville, ON',
  1905816 => 'Ontario',
  1905817 => 'Mississauga, ON',
  1905818 => 'Ontario',
  1905819 => 'Mississauga, ON',
  190582 => 'Mississauga, ON',
  1905825 => 'Oakville, ON',
  1905827 => 'Oakville, ON',
  1905829 => 'Oakville, ON',
  1905830 => 'Newmarket, ON',
  1905831 => 'Pickering, ON',
  1905832 => 'Maple, ON',
  1905833 => 'King City, ON',
  1905834 => 'Port Colborne, ON',
  1905835 => 'Port Colborne, ON',
  1905836 => 'Newmarket, ON',
  1905837 => 'Pickering, ON',
  1905838 => 'Ontario',
  1905839 => 'Pickering, ON',
  1905840 => 'Brampton, ON',
  1905841 => 'Aurora, ON',
  1905842 => 'Oakville, ON',
  1905843 => 'Ontario',
  1905844 => 'Oakville, ON',
  1905845 => 'Oakville, ON',
  1905846 => 'Brampton, ON',
  1905847 => 'Oakville, ON',
  1905848 => 'Mississauga, ON',
  1905849 => 'Oakville, ON',
  1905850 => 'Woodbridge, ON',
  1905851 => 'Woodbridge, ON',
  1905852 => 'Uxbridge, ON',
  1905853 => 'Newmarket, ON',
  1905854 => 'Campbellville, ON',
  1905855 => 'Mississauga, ON',
  1905856 => 'Woodbridge, ON',
  1905857 => 'Bolton, ON',
  1905858 => 'Mississauga, ON',
  1905859 => 'Nobleton, ON',
  190586 => 'Ontario',
  1905862 => 'Uxbridge, ON',
  1905864 => 'Milton, ON',
  1905868 => 'Newmarket, ON',
  1905870 => 'Ontario',
  1905871 => 'Fort Erie, ON',
  1905872 => 'Ontario',
  1905873 => 'Georgetown, ON',
  1905874 => 'Brampton, ON',
  1905875 => 'Milton, ON',
  1905876 => 'Milton, ON',
  1905877 => 'Georgetown, ON',
  1905878 => 'Milton, ON',
  1905879 => 'Ontario',
  1905880 => 'Ontario',
  1905881 => 'Thornhill, ON',
  1905882 => 'Ontario',
  1905883 => 'Richmond Hill, ON',
  1905884 => 'Richmond Hill, ON',
  1905885 => 'Port Hope, ON',
  1905886 => 'Ontario',
  1905887 => 'Ontario',
  1905888 => 'Bethesda, ON',
  1905889 => 'Thornhill, ON',
  1905890 => 'Mississauga, ON',
  1905891 => 'Mississauga, ON',
  1905892 => 'Ontario',
  1905893 => 'Kleinburg, ON',
  1905894 => 'Ridgeway, ON',
  1905895 => 'Newmarket, ON',
  1905896 => 'Mississauga, ON',
  1905897 => 'Mississauga, ON',
  1905898 => 'Newmarket, ON',
  1905899 => 'Wainfleet, ON',
  19059 => 'Ontario',
  1905901 => 'Oakville, ON',
  1905910 => 'Markham, ON',
  1905913 => 'Castlemore, ON',
  1905918 => 'Richmond Hill, ON',
  1905934 => 'St. Catharines, ON',
  1905935 => 'St. Catharines, ON',
  1905936 => 'Tottenham, ON',
  1905937 => 'St. Catharines, ON',
  1905938 => 'St. Catharines, ON',
  1905939 => 'Schomberg, ON',
  190594 => 'Markham, ON',
  1905945 => 'Grimsby, ON',
  1905949 => 'Mississauga, ON',
  1905951 => 'Bolton, ON',
  1905953 => 'Newmarket, ON',
  1905954 => 'Newmarket, ON',
  1905957 => 'Smithville, ON',
  1905967 => 'Newmarket, ON',
  1905970 => 'Brampton, ON',
  1905982 => 'Port Perry, ON',
  1905983 => 'Orono, ON',
  1905984 => 'St. Catharines, ON',
  1905985 => 'Port Perry, ON',
  1905987 => 'Newcastle, ON',
  1905988 => 'St. Catharines, ON',
  1905989 => 'Keswick, ON',
  1905990 => 'Mississauga, ON',
  1905994 => 'Fort Erie, ON',
  1905997 => 'Mississauga, ON',
  1906 => 'Michigan',
  1906225 => 'Marquette, MI',
  1906226 => 'Marquette, MI',
  1906227 => 'Marquette, MI',
  1906228 => 'Marquette, MI',
  1906233 => 'Escanaba, MI',
  1906248 => 'Brimley, MI',
  1906249 => 'Marquette, MI',
  1906253 => 'Sault Ste. Marie, MI',
  1906265 => 'Iron River, MI',
  1906293 => 'Newberry, MI',
  1906296 => 'Lake Linden, MI',
  1906337 => 'Calumet Township, MI',
  1906341 => 'Manistique, MI',
  1906346 => 'Gwinn, MI',
  1906353 => 'Baraga, MI',
  1906387 => 'Munising, MI',
  1906428 => 'Gladstone, MI',
  1906466 => 'Bark River, MI',
  1906475 => 'Negaunee, MI',
  1906484 => 'Cedarville, MI',
  1906485 => 'Ishpeming, MI',
  1906486 => 'Ishpeming, MI',
  1906487 => 'Houghton, MI',
  1906493 => 'Drummond, MI',
  1906495 => 'Kincheloe, MI',
  1906523 => 'Chassell, MI',
  1906524 => 'L\'Anse, MI',
  1906563 => 'Norway, MI',
  1906632 => 'Sault Ste. Marie, MI',
  1906635 => 'Sault Ste. Marie, MI',
  1906643 => 'St. Ignace, MI',
  1906647 => 'Pickford, MI',
  1906753 => 'Stephenson, MI',
  1906774 => 'Iron Mountain, MI',
  1906776 => 'Iron Mountain, MI',
  1906779 => 'Iron Mountain, MI',
  1906786 => 'Escanaba, MI',
  1906789 => 'Escanaba, MI',
  1906847 => 'Mackinac Island, MI',
  1906863 => 'Menominee, MI',
  1906864 => 'Menominee, MI',
  1906875 => 'Crystal Falls, MI',
  1906884 => 'Ontonagon, MI',
  1906932 => 'Ironwood, MI',
  1907 => 'Alaska',
  1907212 => 'Anchorage, AK',
  1907222 => 'Anchorage, AK',
  1907224 => 'Seward, AK',
  1907225 => 'Ketchikan, AK',
  1907228 => 'Ketchikan, AK',
  1907235 => 'Homer, AK',
  1907243 => 'Anchorage, AK',
  1907245 => 'Anchorage, AK',
  1907247 => 'Ketchikan, AK',
  1907248 => 'Anchorage, AK',
  1907257 => 'Anchorage, AK',
  1907258 => 'Anchorage, AK',
  1907260 => 'Soldotna, AK',
  1907262 => 'Soldotna, AK',
  1907264 => 'Anchorage, AK',
  1907269 => 'Anchorage, AK',
  190727 => 'Anchorage, AK',
  1907283 => 'Kenai, AK',
  190733 => 'Anchorage, AK',
  1907335 => 'Kenai, AK',
  1907343 => 'Anchorage, AK',
  1907344 => 'Anchorage, AK',
  1907345 => 'Anchorage, AK',
  1907346 => 'Anchorage, AK',
  1907349 => 'Anchorage, AK',
  1907352 => 'Wasilla, AK',
  1907357 => 'Wasilla, AK',
  1907373 => 'Wasilla, AK',
  1907374 => 'Fairbanks, AK',
  1907376 => 'Wasilla, AK',
  1907424 => 'Cordova, AK',
  1907442 => 'Kotzebue, AK',
  1907443 => 'Nome, AK',
  190745 => 'Fairbanks, AK',
  1907463 => 'Juneau, AK',
  1907465 => 'Juneau, AK',
  1907474 => 'Fairbanks, AK',
  1907479 => 'Fairbanks, AK',
  1907486 => 'Kodiak, AK',
  1907487 => 'Kodiak, AK',
  1907488 => 'North Pole, AK',
  1907490 => 'North Pole, AK',
  1907495 => 'Willow, AK',
  1907522 => 'Anchorage, AK',
  1907523 => 'Juneau, AK',
  1907543 => 'Bethel, AK',
  1907561 => 'Anchorage, AK',
  1907562 => 'Anchorage, AK',
  1907563 => 'Anchorage, AK',
  1907567 => 'Ninilchik, AK',
  1907569 => 'Anchorage, AK',
  1907580 => 'Elmendorf Air Force Base, AK',
  1907581 => 'Unalaska, AK',
  1907586 => 'Juneau, AK',
  1907622 => 'Eagle River, AK',
  1907644 => 'Anchorage, AK',
  1907646 => 'Anchorage, AK',
  1907677 => 'Anchorage, AK',
  1907683 => 'Healy, AK',
  1907688 => 'Chugiak, AK',
  1907694 => 'Eagle River, AK',
  1907696 => 'Eagle River, AK',
  1907714 => 'Soldotna, AK',
  1907729 => 'Anchorage, AK',
  1907733 => 'Talkeetna, AK',
  1907742 => 'Anchorage, AK',
  1907743 => 'Anchorage, AK',
  1907745 => 'Palmer, AK',
  1907746 => 'Palmer, AK',
  1907747 => 'Sitka, AK',
  1907766 => 'Haines, AK',
  1907770 => 'Anchorage, AK',
  1907772 => 'Petersburg, AK',
  1907776 => 'Kenai, AK',
  1907780 => 'Juneau, AK',
  1907783 => 'Girdwood, AK',
  1907789 => 'Juneau, AK',
  1907790 => 'Juneau, AK',
  1907822 => 'Glennallen, AK',
  1907826 => 'Craig, AK',
  1907835 => 'Valdez, AK',
  1907842 => 'Dillingham, AK',
  1907852 => 'Barrow, AK',
  1907868 => 'Anchorage, AK',
  1907874 => 'Wrangell, AK',
  1907883 => 'Tok, AK',
  1907895 => 'Delta Junction, AK',
  1907929 => 'Anchorage, AK',
  1907966 => 'Sitka, AK',
  1907983 => 'Skagway, AK',
  1908 => 'New Jersey',
  1908206 => 'Union, NJ',
  1908213 => 'Phillipsburg, NJ',
  1908221 => 'Bernardsville, NJ',
  1908232 => 'Westfield, NJ',
  1908233 => 'Westfield, NJ',
  1908236 => 'Lebanon, NJ',
  1908237 => 'Flemington, NJ',
  1908258 => 'Union, NJ',
  1908272 => 'Cranford, NJ',
  1908273 => 'Summit, NJ',
  1908276 => 'Cranford, NJ',
  1908277 => 'Summit, NJ',
  1908281 => 'Hillsborough Township, NJ',
  1908282 => 'Elizabeth, NJ',
  1908284 => 'Flemington, NJ',
  1908289 => 'Elizabeth, NJ',
  1908317 => 'Westfield, NJ',
  1908322 => 'Scotch Plains, NJ',
  1908351 => 'Elizabeth, NJ',
  1908352 => 'Elizabeth, NJ',
  1908353 => 'Elizabeth, NJ',
  1908354 => 'Elizabeth, NJ',
  1908355 => 'Elizabeth, NJ',
  1908359 => 'Hillsborough Township, NJ',
  1908362 => 'Blairstown, NJ',
  1908369 => 'Hillsborough Township, NJ',
  1908387 => 'Phillipsburg, NJ',
  1908431 => 'Hillsborough Township, NJ',
  1908436 => 'Elizabeth, NJ',
  1908453 => 'Oxford Township, NJ',
  1908454 => 'Phillipsburg, NJ',
  1908464 => 'Berkeley Heights, NJ',
  1908469 => 'Elizabeth, NJ',
  1908474 => 'Linden, NJ',
  1908475 => 'Belvidere, NJ',
  1908486 => 'Linden, NJ',
  1908496 => 'Columbia, NJ',
  1908497 => 'Cranford, NJ',
  1908522 => 'Summit, NJ',
  1908527 => 'Elizabeth, NJ',
  1908558 => 'Elizabeth, NJ',
  1908587 => 'Linden, NJ',
  1908598 => 'Summit, NJ',
  1908624 => 'Union, NJ',
  1908637 => 'Great Meadows, NJ',
  1908654 => 'Westfield, NJ',
  1908684 => 'Hackettstown, NJ',
  1908686 => 'Union, NJ',
  1908687 => 'Union, NJ',
  1908688 => 'Union, NJ',
  1908689 => 'Washington, NJ',
  1908709 => 'Cranford, NJ',
  1908719 => 'Bedminster Township, NJ',
  1908782 => 'Flemington, NJ',
  1908788 => 'Flemington, NJ',
  1908806 => 'Flemington, NJ',
  1908810 => 'Union, NJ',
  1908813 => 'Hackettstown, NJ',
  1908820 => 'Elizabeth, NJ',
  1908832 => 'Califon, NJ',
  1908835 => 'Washington, NJ',
  1908850 => 'Hackettstown, NJ',
  1908851 => 'Union, NJ',
  1908852 => 'Hackettstown, NJ',
  1908859 => 'Phillipsburg, NJ',
  1908862 => 'Linden, NJ',
  1908874 => 'Hillsborough Township, NJ',
  1908876 => 'Long Valley, NJ',
  1908879 => 'Chester Borough, NJ',
  1908904 => 'Hillsborough Township, NJ',
  1908925 => 'Linden, NJ',
  1908931 => 'Cranford, NJ',
  1908964 => 'Union, NJ',
  1908965 => 'Elizabeth, NJ',
  1908979 => 'Hackettstown, NJ',
  1908994 => 'Elizabeth, NJ',
  1908995 => 'Milford, NJ',
  1908996 => 'Frenchtown, NJ',
  1909 => 'California',
  1909305 => 'San Dimas, CA',
  1909307 => 'Redlands, CA',
  1909335 => 'Redlands, CA',
  1909336 => 'Lake Arrowhead, CA',
  1909337 => 'Lake Arrowhead, CA',
  1909338 => 'Crestline, CA',
  1909349 => 'Fontana, CA',
  1909350 => 'Fontana, CA',
  1909353 => 'Riverside, CA',
  1909355 => 'Fontana, CA',
  1909356 => 'Fontana, CA',
  1909357 => 'Fontana, CA',
  1909364 => 'Chino, CA',
  1909370 => 'Colton, CA',
  190938 => 'San Bernardino, CA',
  1909390 => 'Ontario, CA',
  1909391 => 'Ontario, CA',
  1909392 => 'La Verne, CA',
  1909393 => 'Chino Hills, CA',
  1909394 => 'San Dimas, CA',
  1909395 => 'Ontario, CA',
  1909396 => 'Diamond Bar, CA',
  1909397 => 'Pomona, CA',
  1909421 => 'Rialto, CA',
  1909422 => 'Colton, CA',
  1909427 => 'Fontana, CA',
  1909428 => 'Fontana, CA',
  1909429 => 'Fontana, CA',
  1909433 => 'Colton, CA',
  1909444 => 'Walnut, CA',
  1909460 => 'Ontario, CA',
  1909464 => 'Chino, CA',
  1909465 => 'Chino, CA',
  1909466 => 'Rancho Cucamonga, CA',
  1909467 => 'Ontario, CA',
  1909468 => 'Walnut, CA',
  1909469 => 'Pomona, CA',
  1909473 => 'San Bernardino, CA',
  1909475 => 'San Bernardino, CA',
  1909476 => 'Rancho Cucamonga, CA',
  1909477 => 'Rancho Cucamonga, CA',
  1909478 => 'Loma Linda, CA',
  1909481 => 'Rancho Cucamonga, CA',
  1909483 => 'Rancho Cucamonga, CA',
  1909484 => 'Rancho Cucamonga, CA',
  1909517 => 'Chino, CA',
  1909548 => 'Chino, CA',
  1909558 => 'Loma Linda, CA',
  1909574 => 'Fontana, CA',
  1909579 => 'Upland, CA',
  1909580 => 'Colton, CA',
  1909581 => 'Rancho Cucamonga, CA',
  1909584 => 'Big Bear, CA',
  1909585 => 'Big Bear, CA',
  1909590 => 'Chino, CA',
  1909591 => 'Chino, CA',
  1909592 => 'San Dimas, CA',
  1909593 => 'La Verne, CA',
  1909594 => 'Walnut, CA',
  1909595 => 'Walnut, CA',
  1909596 => 'La Verne, CA',
  1909598 => 'Walnut, CA',
  1909599 => 'San Dimas, CA',
  1909605 => 'Ontario, CA',
  1909606 => 'Chino Hills, CA',
  1909608 => 'Upland, CA',
  1909612 => 'Diamond Bar, CA',
  1909613 => 'Chino, CA',
  1909620 => 'Pomona, CA',
  1909622 => 'Pomona, CA',
  1909623 => 'Pomona, CA',
  1909627 => 'Chino, CA',
  1909628 => 'Chino, CA',
  1909629 => 'Pomona, CA',
  1909646 => 'Rancho Cucamonga, CA',
  1909673 => 'Ontario, CA',
  1909748 => 'Redlands, CA',
  1909758 => 'Rancho Cucamonga, CA',
  1909790 => 'Yucaipa, CA',
  1909792 => 'Redlands, CA',
  1909793 => 'Redlands, CA',
  1909797 => 'Yucaipa, CA',
  1909798 => 'Redlands, CA',
  1909799 => 'Loma Linda, CA',
  1909820 => 'Rialto, CA',
  1909822 => 'Fontana, CA',
  1909823 => 'Fontana, CA',
  1909829 => 'Fontana, CA',
  1909854 => 'Fontana, CA',
  1909860 => 'Diamond Bar, CA',
  1909861 => 'Diamond Bar, CA',
  1909862 => 'Highland, CA',
  1909863 => 'Highland, CA',
  1909864 => 'Highland, CA',
  1909865 => 'Pomona, CA',
  1909866 => 'Big Bear Lake, CA',
  1909867 => 'Running Springs, CA',
  1909868 => 'Pomona, CA',
  1909873 => 'Rialto, CA',
  1909874 => 'Rialto, CA',
  1909875 => 'Rialto, CA',
  1909877 => 'Bloomington, CA',
  1909878 => 'Big Bear Lake, CA',
  190988 => 'San Bernardino, CA',
  1909890 => 'San Bernardino, CA',
  1909902 => 'Chino, CA',
  1909920 => 'Upland, CA',
  1909923 => 'Ontario, CA',
  1909930 => 'Ontario, CA',
  1909931 => 'Upland, CA',
  1909937 => 'Ontario, CA',
  1909941 => 'Rancho Cucamonga, CA',
  1909944 => 'Rancho Cucamonga, CA',
  1909945 => 'Rancho Cucamonga, CA',
  1909946 => 'Upland, CA',
  1909947 => 'Ontario, CA',
  1909948 => 'Rancho Cucamonga, CA',
  1909949 => 'Upland, CA',
  1909980 => 'Rancho Cucamonga, CA',
  1909981 => 'Upland, CA',
  1909982 => 'Upland, CA',
  1909983 => 'Ontario, CA',
  1909984 => 'Ontario, CA',
  1909985 => 'Upland, CA',
  1909986 => 'Ontario, CA',
  1909987 => 'Rancho Cucamonga, CA',
  1909988 => 'Ontario, CA',
  1909989 => 'Rancho Cucamonga, CA',
  1910 => 'North Carolina',
  1910215 => 'Pinehurst, NC',
  1910219 => 'Jacksonville, NC',
  1910223 => 'Fayetteville, NC',
  1910228 => 'Wilmington, NC',
  1910232 => 'Wilmington, NC',
  1910235 => 'Pinehurst, NC',
  1910245 => 'Vass, NC',
  1910246 => 'Southern Pines, NC',
  1910251 => 'Wilmington, NC',
  1910253 => 'Bolivia, NC',
  1910254 => 'Wilmington, NC',
  1910256 => 'Wilmington, NC',
  1910259 => 'Burgaw, NC',
  1910262 => 'Wilmington, NC',
  1910264 => 'Wilmington, NC',
  1910267 => 'Faison, NC',
  1910270 => 'Hampstead, NC',
  1910272 => 'Lumberton, NC',
  1910276 => 'Laurinburg, NC',
  1910277 => 'Laurinburg, NC',
  1910278 => 'Oak Island, NC',
  1910285 => 'Wallace, NC',
  1910289 => 'Rose Hill, NC',
  1910293 => 'Warsaw, NC',
  1910295 => 'Pinehurst, NC',
  1910296 => 'Kenansville, NC',
  1910297 => 'Wilmington, NC',
  1910298 => 'Beulaville, NC',
  1910299 => 'Clinton, NC',
  1910313 => 'Wilmington, NC',
  1910321 => 'Fayetteville, NC',
  1910323 => 'Fayetteville, NC',
  1910324 => 'Richlands, NC',
  1910325 => 'Swansboro, NC',
  1910326 => 'Swansboro, NC',
  1910327 => 'Sneads Ferry, NC',
  1910328 => 'Surf City, NC',
  1910329 => 'Holly Ridge, NC',
  1910332 => 'Wilmington, NC',
  1910333 => 'Jacksonville, NC',
  1910338 => 'Wilmington, NC',
  1910339 => 'Fayetteville, NC',
  1910341 => 'Wilmington, NC',
  1910343 => 'Wilmington, NC',
  1910346 => 'Jacksonville, NC',
  1910347 => 'Jacksonville, NC',
  1910350 => 'Wilmington, NC',
  1910352 => 'Wilmington, NC',
  1910353 => 'Jacksonville, NC',
  1910355 => 'Jacksonville, NC',
  1910362 => 'Wilmington, NC',
  1910371 => 'Leland, NC',
  1910383 => 'Leland, NC',
  1910392 => 'Wilmington, NC',
  1910395 => 'Wilmington, NC',
  1910396 => 'Fort Bragg, NC',
  1910397 => 'Wilmington, NC',
  1910399 => 'Wilmington, NC',
  1910417 => 'Rockingham, NC',
  1910422 => 'Rowland, NC',
  1910423 => 'Fayetteville, NC',
  1910424 => 'Fayetteville, NC',
  1910425 => 'Fayetteville, NC',
  1910426 => 'Fayetteville, NC',
  1910428 => 'Biscoe, NC',
  1910429 => 'Fayetteville, NC',
  1910433 => 'Fayetteville, NC',
  1910436 => 'Spring Lake, NC',
  1910439 => 'Mount Gilead, NC',
  1910442 => 'Wilmington, NC',
  1910450 => 'Camp Lejeune, NC',
  1910451 => 'Camp Lejeune, NC',
  1910452 => 'Wilmington, NC',
  1910454 => 'Southport, NC',
  1910455 => 'Jacksonville, NC',
  1910457 => 'Southport, NC',
  1910458 => 'Carolina Beach, NC',
  191048 => 'Fayetteville, NC',
  1910497 => 'Spring Lake, NC',
  1910509 => 'Wilmington, NC',
  1910520 => 'Wilmington, NC',
  1910521 => 'Pembroke, NC',
  1910522 => 'Pembroke, NC',
  1910525 => 'Roseboro, NC',
  1910538 => 'Wilmington, NC',
  1910564 => 'Clinton, NC',
  1910572 => 'Troy, NC',
  1910576 => 'Troy, NC',
  1910577 => 'Jacksonville, NC',
  1910582 => 'Hamlet, NC',
  1910590 => 'Clinton, NC',
  1910592 => 'Clinton, NC',
  1910594 => 'Newton Grove, NC',
  1910609 => 'Fayetteville, NC',
  1910615 => 'Fayetteville, NC',
  1910616 => 'Wilmington, NC',
  1910618 => 'Lumberton, NC',
  1910620 => 'Wilmington, NC',
  1910628 => 'Fairmont, NC',
  1910630 => 'Fayetteville, NC',
  1910640 => 'Whiteville, NC',
  1910641 => 'Whiteville, NC',
  1910642 => 'Whiteville, NC',
  1910648 => 'Bladenboro, NC',
  1910652 => 'Ellerbe, NC',
  1910653 => 'Tabor City, NC',
  1910654 => 'Chadbourn, NC',
  1910671 => 'Lumberton, NC',
  1910673 => 'West End, NC',
  1910675 => 'Castle Hayne, NC',
  1910678 => 'Fayetteville, NC',
  1910686 => 'Wilmington, NC',
  1910692 => 'Southern Pines, NC',
  1910693 => 'Southern Pines, NC',
  1910695 => 'Southern Pines, NC',
  1910715 => 'Pinehurst, NC',
  1910738 => 'Lumberton, NC',
  1910739 => 'Lumberton, NC',
  1910762 => 'Wilmington, NC',
  1910763 => 'Wilmington, NC',
  1910764 => 'Fayetteville, NC',
  1910772 => 'Wilmington, NC',
  1910777 => 'Wilmington, NC',
  191079 => 'Wilmington, NC',
  1910814 => 'Lillington, NC',
  1910815 => 'Wilmington, NC',
  1910822 => 'Fayetteville, NC',
  1910826 => 'Fayetteville, NC',
  1910842 => 'Supply, NC',
  1910843 => 'Red Springs, NC',
  1910844 => 'Maxton, NC',
  1910848 => 'Raeford, NC',
  1910860 => 'Fayetteville, NC',
  1910862 => 'Elizabethtown, NC',
  1910863 => 'Bladenboro, NC',
  1910864 => 'Fayetteville, NC',
  1910865 => 'St. Pauls, NC',
  1910867 => 'Fayetteville, NC',
  1910868 => 'Fayetteville, NC',
  1910875 => 'Raeford, NC',
  1910891 => 'Dunn, NC',
  1910892 => 'Dunn, NC',
  1910893 => 'Lillington, NC',
  1910895 => 'Rockingham, NC',
  1910904 => 'Raeford, NC',
  1910907 => 'E. E. Smith, Fort Bragg, NC',
  1910920 => 'Fayetteville, NC',
  1910938 => 'Jacksonville, NC',
  1910944 => 'Aberdeen, NC',
  1910947 => 'Carthage, NC',
  1910948 => 'Robbins, NC',
  1910974 => 'Candor, NC',
  1910997 => 'Rockingham, NC',
  1912 => 'Georgia',
  1912201 => 'Savannah, GA',
  191223 => 'Savannah, GA',
  1912261 => 'Brunswick, GA',
  1912262 => 'Brunswick, GA',
  1912264 => 'Brunswick, GA',
  1912265 => 'Brunswick, GA',
  1912267 => 'Brunswick, GA',
  1912275 => 'Brunswick, GA',
  1912280 => 'Brunswick, GA',
  1912283 => 'Waycross, GA',
  1912284 => 'Waycross, GA',
  1912285 => 'Waycross, GA',
  1912287 => 'Waycross, GA',
  1912289 => 'Brunswick, GA',
  1912303 => 'Savannah, GA',
  1912330 => 'Pooler, GA',
  1912335 => 'Savannah, GA',
  1912342 => 'Brunswick, GA',
  1912349 => 'Savannah, GA',
  191235 => 'Savannah, GA',
  1912359 => 'Broxton, GA',
  1912366 => 'Baxley, GA',
  1912367 => 'Baxley, GA',
  1912368 => 'Hinesville, GA',
  1912369 => 'Hinesville, GA',
  1912375 => 'Hazlehurst, GA',
  1912383 => 'Douglas, GA',
  1912384 => 'Douglas, GA',
  1912389 => 'Douglas, GA',
  1912422 => 'Pearson, GA',
  1912427 => 'Jesup, GA',
  1912435 => 'Fort Stewart, GA',
  1912437 => 'Darien, GA',
  1912443 => 'Savannah, GA',
  1912447 => 'Savannah, GA',
  1912449 => 'Blackshear, GA',
  1912459 => 'Richmond Hill, GA',
  1912462 => 'Nahunta, GA',
  1912466 => 'Brunswick, GA',
  1912487 => 'Homerville, GA',
  1912489 => 'Statesboro, GA',
  1912496 => 'Folkston, GA',
  1912526 => 'Lyons, GA',
  1912529 => 'Soperton, GA',
  1912530 => 'Jesup, GA',
  1912537 => 'Vidalia, GA',
  1912538 => 'Vidalia, GA',
  1912545 => 'Ludowici, GA',
  1912554 => 'Brunswick, GA',
  1912557 => 'Reidsville, GA',
  1912564 => 'Sylvania, GA',
  1912583 => 'Mount Vernon, GA',
  1912587 => 'Statesboro, GA',
  1912588 => 'Jesup, GA',
  1912598 => 'Savannah, GA',
  1912629 => 'Savannah, GA',
  1912632 => 'Alma, GA',
  1912634 => 'Saint Simons Island, GA',
  1912638 => 'Saint Simons Island, GA',
  1912644 => 'Savannah, GA',
  1912651 => 'Savannah, GA',
  1912652 => 'Savannah, GA',
  1912653 => 'Pembroke, GA',
  1912654 => 'Glennville, GA',
  1912673 => 'St. Marys, GA',
  1912681 => 'Statesboro, GA',
  1912685 => 'Metter, GA',
  1912691 => 'Savannah, GA',
  1912692 => 'Savannah, GA',
  1912727 => 'Richmond Hill, GA',
  1912729 => 'Kingsland, GA',
  1912739 => 'Claxton, GA',
  1912748 => 'Pooler, GA',
  1912754 => 'Springfield, GA',
  1912756 => 'Richmond Hill, GA',
  1912764 => 'Statesboro, GA',
  1912772 => 'Guyton, GA',
  1912786 => 'Tybee Island, GA',
  1912790 => 'Savannah, GA',
  1912819 => 'Savannah, GA',
  1912826 => 'Rincon, GA',
  1912832 => 'Townsend, GA',
  1912839 => 'Statesboro, GA',
  1912842 => 'Brooklet, GA',
  1912863 => 'Sylvania, GA',
  1912865 => 'Portal, GA',
  1912871 => 'Statesboro, GA',
  1912876 => 'Hinesville, GA',
  1912877 => 'Hinesville, GA',
  1912882 => 'St. Marys, GA',
  1912884 => 'Midway, GA',
  1912897 => 'Savannah, GA',
  1912898 => 'Savannah, GA',
  1912920 => 'Savannah, GA',
  1912921 => 'Savannah, GA',
  1912925 => 'Savannah, GA',
  1912927 => 'Savannah, GA',
  1912961 => 'Savannah, GA',
  1912964 => 'Savannah, GA',
  1913 => 'Kansas',
  1913233 => 'Kansas City, KS',
  1913239 => 'Overland Park, KS',
  1913248 => 'Shawnee, KS',
  1913250 => 'Leavenworth, KS',
  1913254 => 'Olathe, KS',
  1913281 => 'Kansas City, KS',
  1913287 => 'Kansas City, KS',
  1913294 => 'Paola, KS',
  1913299 => 'Kansas City, KS',
  1913317 => 'Overland Park, KS',
  1913321 => 'Kansas City, KS',
  1913328 => 'Kansas City, KS',
  1913334 => 'Kansas City, KS',
  1913342 => 'Kansas City, KS',
  1913352 => 'Pleasanton, KS',
  1913367 => 'Atchison, KS',
  1913371 => 'Kansas City, KS',
  1913390 => 'Olathe, KS',
  1913393 => 'Olathe, KS',
  1913397 => 'Olathe, KS',
  1913402 => 'Overland Park, KS',
  1913498 => 'Overland Park, KS',
  1913557 => 'Paola, KS',
  1913573 => 'Kansas City, KS',
  1913583 => 'De Soto, KS',
  1913588 => 'Kansas City, KS',
  1913592 => 'Spring Hill, KS',
  1913596 => 'Kansas City, KS',
  1913621 => 'Kansas City, KS',
  1913651 => 'Leavenworth, KS',
  1913680 => 'Leavenworth, KS',
  1913681 => 'Overland Park, KS',
  1913682 => 'Leavenworth, KS',
  1913685 => 'Overland Park, KS',
  1913696 => 'Leawood, KS',
  1913715 => 'Olathe, KS',
  1913721 => 'Kansas City, KS',
  1913724 => 'Basehor, KS',
  1913727 => 'Lansing, KS',
  1913755 => 'Osawatomie, KS',
  1913757 => 'LaCygne, KS',
  1913758 => 'Leavenworth, KS',
  1913764 => 'Olathe, KS',
  1913768 => 'Olathe, KS',
  1913780 => 'Olathe, KS',
  1913782 => 'Olathe, KS',
  1913788 => 'Kansas City, KS',
  1913791 => 'Olathe, KS',
  1913795 => 'Mound City, KS',
  1913814 => 'Overland Park, KS',
  1913829 => 'Olathe, KS',
  1913837 => 'Louisburg, KS',
  1913845 => 'Tonganoxie, KS',
  1913851 => 'Overland Park, KS',
  1913856 => 'Gardner, KS',
  1913884 => 'Gardner, KS',
  1913901 => 'Overland Park, KS',
  1914 => 'New York',
  1914207 => 'Yonkers, NY',
  1914232 => 'Katonah, NY',
  1914234 => 'Bedford, NY',
  1914235 => 'New Rochelle, NY',
  1914237 => 'Yonkers, NY',
  1914238 => 'Chappaqua, NY',
  1914241 => 'Mount Kisco, NY',
  1914242 => 'Mount Kisco, NY',
  1914243 => 'Yorktown Heights, NY',
  1914244 => 'Mount Kisco, NY',
  1914245 => 'Yorktown Heights, NY',
  1914271 => 'Croton-on-Hudson, NY',
  1914273 => 'Armonk, NY',
  1914276 => 'Somers, NY',
  1914277 => 'Somers, NY',
  1914285 => 'White Plains, NY',
  1914287 => 'White Plains, NY',
  1914288 => 'White Plains, NY',
  1914304 => 'White Plains, NY',
  1914305 => 'Port Chester, NY',
  1914328 => 'White Plains, NY',
  1914332 => 'Tarrytown, NY',
  1914333 => 'Tarrytown, NY',
  1914345 => 'Elmsford, NY',
  1914347 => 'Elmsford, NY',
  1914355 => 'New Rochelle, NY',
  1914358 => 'White Plains, NY',
  1914366 => 'Sleepy Hollow, NY',
  1914375 => 'Yonkers, NY',
  1914376 => 'Yonkers, NY',
  1914377 => 'Yonkers, NY',
  1914378 => 'Yonkers, NY',
  1914381 => 'Mamaroneck, NY',
  1914421 => 'White Plains, NY',
  1914422 => 'White Plains, NY',
  1914423 => 'Yonkers, NY',
  1914428 => 'White Plains, NY',
  1914457 => 'Yonkers, NY',
  1914472 => 'Scarsdale, NY',
  1914476 => 'Yonkers, NY',
  1914481 => 'Port Chester, NY',
  1914493 => 'Valhalla, NY',
  1914524 => 'Tarrytown, NY',
  1914528 => 'Mohegan Lake, NY',
  1914533 => 'South Salem, NY',
  1914576 => 'New Rochelle, NY',
  1914591 => 'Irvington, NY',
  1914592 => 'Elmsford, NY',
  1914631 => 'Tarrytown, NY',
  1914632 => 'New Rochelle, NY',
  1914633 => 'New Rochelle, NY',
  1914636 => 'New Rochelle, NY',
  1914637 => 'New Rochelle, NY',
  1914654 => 'New Rochelle, NY',
  1914663 => 'Mount Vernon, NY',
  1914664 => 'Mount Vernon, NY',
  1914665 => 'Mount Vernon, NY',
  1914666 => 'Mount Kisco, NY',
  1914667 => 'Mount Vernon, NY',
  1914668 => 'Mount Vernon, NY',
  1914669 => 'North Salem, NY',
  1914681 => 'White Plains, NY',
  1914682 => 'White Plains, NY',
  1914683 => 'White Plains, NY',
  1914684 => 'White Plains, NY',
  1914686 => 'White Plains, NY',
  1914698 => 'Mamaroneck, NY',
  1914699 => 'Mount Vernon, NY',
  1914713 => 'Scarsdale, NY',
  1914722 => 'Scarsdale, NY',
  1914723 => 'Scarsdale, NY',
  1914725 => 'Scarsdale, NY',
  1914734 => 'Peekskill, NY',
  1914738 => 'Pelham, NY',
  1914740 => 'New Rochelle, NY',
  1914751 => 'Yonkers, NY',
  1914761 => 'White Plains, NY',
  1914764 => 'Pound Ridge, NY',
  1914776 => 'Yonkers, NY',
  1914777 => 'Mamaroneck, NY',
  1914813 => 'New Rochelle, NY',
  1914831 => 'White Plains, NY',
  1914833 => 'Larchmont, NY',
  1914834 => 'Larchmont, NY',
  1914835 => 'Harrison, NY',
  1914864 => 'Mount Kisco, NY',
  1914921 => 'Rye, NY',
  1914923 => 'Ossining, NY',
  1914925 => 'Rye, NY',
  1914930 => 'Peekskill, NY',
  1914934 => 'Port Chester, NY',
  1914935 => 'Port Chester, NY',
  1914937 => 'Port Chester, NY',
  1914939 => 'Port Chester, NY',
  1914941 => 'Ossining, NY',
  1914944 => 'Ossining, NY',
  1914946 => 'White Plains, NY',
  1914948 => 'White Plains, NY',
  1914949 => 'White Plains, NY',
  191496 => 'Yonkers, NY',
  1914962 => 'Yorktown Heights, NY',
  1914967 => 'Rye, NY',
  1914993 => 'White Plains, NY',
  1914997 => 'White Plains, NY',
  1915 => 'Texas',
  1915231 => 'El Paso, TX',
  1915257 => 'El Paso, TX',
  1915307 => 'El Paso, TX',
  1915313 => 'El Paso, TX',
  1915351 => 'El Paso, TX',
  1915532 => 'El Paso, TX',
  1915533 => 'El Paso, TX',
  1915534 => 'El Paso, TX',
  191554 => 'El Paso, TX',
  1915562 => 'El Paso, TX',
  1915564 => 'El Paso, TX',
  1915565 => 'El Paso, TX',
  1915566 => 'El Paso, TX',
  1915569 => 'El Paso, TX',
  1915577 => 'El Paso, TX',
  1915581 => 'El Paso, TX',
  1915584 => 'El Paso, TX',
  1915585 => 'El Paso, TX',
  1915587 => 'El Paso, TX',
  191559 => 'El Paso, TX',
  1915613 => 'El Paso, TX',
  1915629 => 'El Paso, TX',
  1915633 => 'El Paso, TX',
  1915751 => 'El Paso, TX',
  1915755 => 'El Paso, TX',
  1915757 => 'El Paso, TX',
  1915759 => 'El Paso, TX',
  1915760 => 'El Paso, TX',
  1915764 => 'Fabens, TX',
  191577 => 'El Paso, TX',
  1915781 => 'El Paso, TX',
  1915790 => 'El Paso, TX',
  1915821 => 'El Paso, TX',
  1915833 => 'El Paso, TX',
  1915838 => 'El Paso, TX',
  1915842 => 'El Paso, TX',
  1915843 => 'El Paso, TX',
  1915845 => 'El Paso, TX',
  1915849 => 'El Paso, TX',
  191585 => 'El Paso, TX',
  1915860 => 'El Paso, TX',
  1915872 => 'El Paso, TX',
  1915875 => 'El Paso, TX',
  1915877 => 'Canutillo, TX',
  1915881 => 'El Paso, TX',
  1915886 => 'Anthony, TX',
  1915921 => 'El Paso, TX',
  1916 => 'California',
  1916285 => 'Sacramento, CA',
  1916294 => 'Folsom, CA',
  1916315 => 'Rocklin, CA',
  1916325 => 'Sacramento, CA',
  1916333 => 'Sacramento, CA',
  1916338 => 'Sacramento, CA',
  1916351 => 'Folsom, CA',
  1916353 => 'Folsom, CA',
  1916354 => 'Rancho Murieta, CA',
  1916355 => 'Folsom, CA',
  1916358 => 'El Dorado Hills, CA',
  191636 => 'Sacramento, CA',
  191637 => 'West Sacramento, CA',
  1916379 => 'Sacramento, CA',
  1916381 => 'Sacramento, CA',
  1916383 => 'Sacramento, CA',
  1916386 => 'Sacramento, CA',
  1916387 => 'Sacramento, CA',
  1916388 => 'Sacramento, CA',
  191639 => 'Sacramento, CA',
  1916408 => 'Lincoln, CA',
  1916419 => 'Sacramento, CA',
  191642 => 'Sacramento, CA',
  1916434 => 'Lincoln, CA',
  1916435 => 'Rocklin, CA',
  191644 => 'Sacramento, CA',
  191645 => 'Sacramento, CA',
  1916473 => 'Sacramento, CA',
  1916476 => 'Sacramento, CA',
  1916478 => 'Elk Grove, CA',
  191648 => 'Sacramento, CA',
  1916492 => 'Sacramento, CA',
  1916498 => 'Sacramento, CA',
  1916514 => 'Sacramento, CA',
  1916515 => 'Sacramento, CA',
  1916525 => 'Sacramento, CA',
  1916537 => 'Carmichael, CA',
  1916543 => 'Lincoln, CA',
  1916550 => 'Sacramento, CA',
  1916564 => 'Sacramento, CA',
  1916565 => 'Sacramento, CA',
  1916567 => 'Sacramento, CA',
  1916568 => 'Sacramento, CA',
  1916608 => 'Folsom, CA',
  1916609 => 'Carmichael, CA',
  1916614 => 'Sacramento, CA',
  1916624 => 'Rocklin, CA',
  1916625 => 'Rocklin, CA',
  1916630 => 'Rocklin, CA',
  1916631 => 'Rancho Cordova, CA',
  1916632 => 'Rocklin, CA',
  1916635 => 'Rancho Cordova, CA',
  1916638 => 'Rancho Cordova, CA',
  1916641 => 'Sacramento, CA',
  1916645 => 'Lincoln, CA',
  1916646 => 'Sacramento, CA',
  1916648 => 'Sacramento, CA',
  1916649 => 'Sacramento, CA',
  1916652 => 'Loomis, CA',
  1916660 => 'Loomis, CA',
  1916663 => 'Newcastle, CA',
  1916681 => 'Sacramento, CA',
  1916682 => 'Sacramento, CA',
  1916683 => 'Elk Grove, CA',
  1916684 => 'Elk Grove, CA',
  1916685 => 'Elk Grove, CA',
  1916686 => 'Elk Grove, CA',
  1916687 => 'Wilton, CA',
  1916688 => 'Sacramento, CA',
  1916689 => 'Sacramento, CA',
  1916691 => 'Elk Grove, CA',
  1916706 => 'Sacramento, CA',
  1916714 => 'Elk Grove, CA',
  191672 => 'Citrus Heights, CA',
  191673 => 'Sacramento, CA',
  1916771 => 'Roseville, CA',
  1916772 => 'Roseville, CA',
  1916773 => 'Roseville, CA',
  1916774 => 'Roseville, CA',
  1916776 => 'Walnut Grove, CA',
  1916777 => 'Isleton, CA',
  191678 => 'Roseville, CA',
  1916808 => 'Sacramento, CA',
  1916817 => 'Folsom, CA',
  1916851 => 'Rancho Cordova, CA',
  1916852 => 'Rancho Cordova, CA',
  1916853 => 'Rancho Cordova, CA',
  1916858 => 'Rancho Cordova, CA',
  1916863 => 'Fair Oaks, CA',
  1916874 => 'Sacramento, CA',
  1916875 => 'Sacramento, CA',
  191692 => 'Sacramento, CA',
  1916930 => 'Sacramento, CA',
  1916932 => 'Folsom, CA',
  1916933 => 'El Dorado Hills, CA',
  1916939 => 'El Dorado Hills, CA',
  1916941 => 'El Dorado Hills, CA',
  1916944 => 'Carmichael, CA',
  1916962 => 'Fair Oaks, CA',
  1916965 => 'Fair Oaks, CA',
  1916966 => 'Fair Oaks, CA',
  1916967 => 'Fair Oaks, CA',
  1916972 => 'Sacramento, CA',
  1916973 => 'Sacramento, CA',
  1916978 => 'Sacramento, CA',
  1916979 => 'Sacramento, CA',
  1916983 => 'Folsom, CA',
  1916984 => 'Folsom, CA',
  1916985 => 'Folsom, CA',
  1916987 => 'Orangevale, CA',
  1916988 => 'Orangevale, CA',
  1916989 => 'Orangevale, CA',
  1916991 => 'Rio Linda, CA',
  1916992 => 'Rio Linda, CA',
  1917 => 'New York',
  1918 => 'Oklahoma',
  1918207 => 'Tahlequah, OK',
  1918224 => 'Sapulpa, OK',
  1918225 => 'Cushing, OK',
  1918227 => 'Sapulpa, OK',
  1918234 => 'Tulsa, OK',
  1918241 => 'Sand Springs, OK',
  1918245 => 'Sand Springs, OK',
  1918246 => 'Sand Springs, OK',
  1918249 => 'Tulsa, OK',
  1918250 => 'Tulsa, OK',
  1918251 => 'Broken Arrow, OK',
  1918252 => 'Tulsa, OK',
  1918253 => 'Jay, OK',
  1918254 => 'Tulsa, OK',
  1918256 => 'Vinita, OK',
  1918257 => 'Afton, OK',
  1918258 => 'Broken Arrow, OK',
  1918259 => 'Broken Arrow, OK',
  1918266 => 'Catoosa, OK',
  1918267 => 'Beggs, OK',
  1918270 => 'Tulsa, OK',
  1918272 => 'Owasso, OK',
  1918273 => 'Nowata, OK',
  1918274 => 'Owasso, OK',
  1918279 => 'Coweta, OK',
  1918283 => 'Claremore, OK',
  1918286 => 'Broken Arrow, OK',
  1918287 => 'Pawhuska, OK',
  1918289 => 'Tulsa, OK',
  1918291 => 'Glenpool, OK',
  1918293 => 'Tulsa, OK',
  1918294 => 'Tulsa, OK',
  1918295 => 'Tulsa, OK',
  1918297 => 'Hartshorne, OK',
  1918298 => 'Tulsa, OK',
  1918302 => 'McAlester, OK',
  1918307 => 'Tulsa, OK',
  1918321 => 'Kiefer, OK',
  1918331 => 'Bartlesville, OK',
  1918333 => 'Bartlesville, OK',
  1918335 => 'Bartlesville, OK',
  1918336 => 'Bartlesville, OK',
  1918337 => 'Bartlesville, OK',
  1918341 => 'Claremore, OK',
  1918342 => 'Claremore, OK',
  1918343 => 'Claremore, OK',
  1918352 => 'Drumright, OK',
  1918355 => 'Broken Arrow, OK',
  1918357 => 'Broken Arrow, OK',
  1918358 => 'Cleveland, OK',
  1918366 => 'Bixby, OK',
  1918367 => 'Bristow, OK',
  1918369 => 'Bixby, OK',
  1918371 => 'Collinsville, OK',
  1918376 => 'Owasso, OK',
  1918382 => 'Tulsa, OK',
  1918392 => 'Tulsa, OK',
  1918394 => 'Tulsa, OK',
  1918396 => 'Skiatook, OK',
  1918398 => 'Tulsa, OK',
  1918420 => 'McAlester, OK',
  1918421 => 'McAlester, OK',
  1918422 => 'Colcord, OK',
  1918423 => 'McAlester, OK',
  1918425 => 'Tulsa, OK',
  1918426 => 'McAlester, OK',
  1918427 => 'Muldrow, OK',
  1918429 => 'McAlester, OK',
  1918431 => 'Tahlequah, OK',
  1918434 => 'Salina, OK',
  1918436 => 'Pocola, OK',
  1918437 => 'Tulsa, OK',
  1918438 => 'Tulsa, OK',
  1918439 => 'Tulsa, OK',
  1918443 => 'Oologah, OK',
  1918445 => 'Tulsa, OK',
  1918446 => 'Tulsa, OK',
  1918447 => 'Tulsa, OK',
  1918449 => 'Broken Arrow, OK',
  1918451 => 'Broken Arrow, OK',
  1918453 => 'Tahlequah, OK',
  1918455 => 'Broken Arrow, OK',
  1918456 => 'Tahlequah, OK',
  1918458 => 'Tahlequah, OK',
  1918459 => 'Tulsa, OK',
  1918461 => 'Tulsa, OK',
  1918465 => 'Wilburton, OK',
  1918473 => 'Checotah, OK',
  1918476 => 'Chouteau, OK',
  1918477 => 'Tulsa, OK',
  1918478 => 'Fort Gibson, OK',
  1918479 => 'Locust Grove, OK',
  1918481 => 'Tulsa, OK',
  1918482 => 'Haskell, OK',
  1918485 => 'Wagoner, OK',
  1918486 => 'Coweta, OK',
  1918488 => 'Tulsa, OK',
  191849 => 'Tulsa, OK',
  1918502 => 'Tulsa, OK',
  1918534 => 'Dewey, OK',
  1918540 => 'Miami, OK',
  1918542 => 'Miami, OK',
  1918543 => 'Inola, OK',
  1918567 => 'Talihina, OK',
  1918569 => 'Clayton, OK',
  1918574 => 'Tulsa, OK',
  1918579 => 'Tulsa, OK',
  191858 => 'Tulsa, OK',
  1918591 => 'Tulsa, OK',
  1918592 => 'Tulsa, OK',
  1918594 => 'Tulsa, OK',
  1918596 => 'Tulsa, OK',
  1918599 => 'Tulsa, OK',
  1918609 => 'Owasso, OK',
  1918610 => 'Tulsa, OK',
  1918619 => 'Tulsa, OK',
  1918622 => 'Tulsa, OK',
  1918623 => 'Okemah, OK',
  1918627 => 'Tulsa, OK',
  1918628 => 'Tulsa, OK',
  1918647 => 'Poteau, OK',
  1918649 => 'Poteau, OK',
  1918652 => 'Henryetta, OK',
  1918653 => 'Heavener, OK',
  1918660 => 'Tulsa, OK',
  1918663 => 'Tulsa, OK',
  1918664 => 'Tulsa, OK',
  1918665 => 'Tulsa, OK',
  191868 => 'Muskogee, OK',
  1918689 => 'Eufaula, OK',
  1918696 => 'Stilwell, OK',
  1918712 => 'Tulsa, OK',
  1918723 => 'Westville, OK',
  1918728 => 'Tulsa, OK',
  191874 => 'Tulsa, OK',
  1918756 => 'Okmulgee, OK',
  1918758 => 'Okmulgee, OK',
  1918762 => 'Pawnee, OK',
  1918770 => 'Tulsa, OK',
  1918773 => 'Vian, OK',
  1918775 => 'Sallisaw, OK',
  1918779 => 'Tulsa, OK',
  1918786 => 'Grove, OK',
  1918787 => 'Grove, OK',
  1918789 => 'Chelsea, OK',
  1918794 => 'Tulsa, OK',
  1918806 => 'Broken Arrow, OK',
  1918824 => 'Pryor Creek, OK',
  1918825 => 'Pryor Creek, OK',
  1918828 => 'Tulsa, OK',
  1918832 => 'Tulsa, OK',
  1918834 => 'Tulsa, OK',
  1918835 => 'Tulsa, OK',
  1918836 => 'Tulsa, OK',
  1918838 => 'Tulsa, OK',
  1918865 => 'Mannford, OK',
  1918868 => 'Kansas, OK',
  1918872 => 'Broken Arrow, OK',
  1918877 => 'Tulsa, OK',
  1918885 => 'Hominy, OK',
  1918895 => 'Tulsa, OK',
  1918933 => 'Tulsa, OK',
  1918938 => 'Tulsa, OK',
  1918949 => 'Tulsa, OK',
  1918962 => 'Spiro, OK',
  1918967 => 'Stigler, OK',
  1918968 => 'Stroud, OK',
  1919 => 'North Carolina',
  1919207 => 'Benson, NC',
  1919209 => 'Smithfield, NC',
  1919212 => 'Raleigh, NC',
  1919217 => 'Knightdale, NC',
  1919220 => 'Durham, NC',
  1919231 => 'Raleigh, NC',
  1919232 => 'Raleigh, NC',
  1919237 => 'Durham, NC',
  1919240 => 'Chapel Hill, NC',
  1919242 => 'Fremont, NC',
  1919245 => 'Hillsborough, NC',
  1919250 => 'Raleigh, NC',
  1919251 => 'Durham, NC',
  1919255 => 'Raleigh, NC',
  1919256 => 'Raleigh, NC',
  1919258 => 'Broadway, NC',
  1919261 => 'Knightdale, NC',
  1919266 => 'Knightdale, NC',
  1919267 => 'Apex, NC',
  1919269 => 'Zebulon, NC',
  1919284 => 'Kenly, NC',
  1919286 => 'Durham, NC',
  1919303 => 'Apex, NC',
  1919304 => 'Mebane, NC',
  1919309 => 'Durham, NC',
  1919313 => 'Durham, NC',
  1919319 => 'Cary, NC',
  1919331 => 'Angier, NC',
  1919340 => 'Louisburg, NC',
  1919350 => 'Raleigh, NC',
  1919359 => 'Clayton, NC',
  1919361 => 'Durham, NC',
  1919362 => 'Apex, NC',
  1919363 => 'Apex, NC',
  1919365 => 'Wendell, NC',
  1919366 => 'Wendell, NC',
  1919367 => 'Apex, NC',
  1919380 => 'Cary, NC',
  1919381 => 'Durham, NC',
  1919382 => 'Durham, NC',
  1919383 => 'Durham, NC',
  1919387 => 'Apex, NC',
  1919388 => 'Cary, NC',
  1919401 => 'Durham, NC',
  1919402 => 'Durham, NC',
  1919403 => 'Durham, NC',
  1919404 => 'Zebulon, NC',
  1919405 => 'Durham, NC',
  1919416 => 'Durham, NC',
  1919419 => 'Durham, NC',
  1919420 => 'Raleigh, NC',
  1919424 => 'Raleigh, NC',
  1919453 => 'Wake Forest, NC',
  191946 => 'Cary, NC',
  1919470 => 'Durham, NC',
  1919471 => 'Durham, NC',
  1919477 => 'Durham, NC',
  1919479 => 'Durham, NC',
  1919481 => 'Cary, NC',
  1919484 => 'Durham, NC',
  1919489 => 'Durham, NC',
  1919490 => 'Durham, NC',
  1919493 => 'Durham, NC',
  1919494 => 'Franklinton, NC',
  1919496 => 'Louisburg, NC',
  1919497 => 'Louisburg, NC',
  1919499 => 'Sanford, NC',
  1919510 => 'Raleigh, NC',
  1919515 => 'Raleigh, NC',
  1919518 => 'Raleigh, NC',
  1919528 => 'Creedmoor, NC',
  1919530 => 'Durham, NC',
  1919542 => 'Pittsboro, NC',
  1919544 => 'Durham, NC',
  1919545 => 'Pittsboro, NC',
  1919550 => 'Clayton, NC',
  1919552 => 'Fuquay-Varina, NC',
  1919553 => 'Clayton, NC',
  1919554 => 'Wake Forest, NC',
  1919556 => 'Wake Forest, NC',
  1919557 => 'Fuquay-Varina, NC',
  1919560 => 'Durham, NC',
  1919562 => 'Wake Forest, NC',
  1919563 => 'Mebane, NC',
  1919567 => 'Fuquay-Varina, NC',
  1919571 => 'Raleigh, NC',
  1919572 => 'Durham, NC',
  1919575 => 'Butner, NC',
  1919577 => 'Fuquay-Varina, NC',
  1919580 => 'Goldsboro, NC',
  1919585 => 'Clayton, NC',
  1919596 => 'Durham, NC',
  1919598 => 'Durham, NC',
  1919603 => 'Oxford, NC',
  1919620 => 'Durham, NC',
  1919639 => 'Angier, NC',
  1919644 => 'Hillsborough, NC',
  1919658 => 'Mount Olive, NC',
  1919660 => 'Durham, NC',
  1919661 => 'Garner, NC',
  1919662 => 'Garner, NC',
  1919663 => 'Siler City, NC',
  1919668 => 'Durham, NC',
  1919676 => 'Raleigh, NC',
  1919677 => 'Cary, NC',
  1919678 => 'Cary, NC',
  191968 => 'Durham, NC',
  1919689 => 'Goldsboro, NC',
  1919690 => 'Oxford, NC',
  1919693 => 'Oxford, NC',
  1919708 => 'Sanford, NC',
  1919718 => 'Sanford, NC',
  1919731 => 'Goldsboro, NC',
  1919732 => 'Hillsborough, NC',
  1919733 => 'Raleigh, NC',
  1919734 => 'Goldsboro, NC',
  1919735 => 'Goldsboro, NC',
  1919736 => 'Goldsboro, NC',
  1919739 => 'Goldsboro, NC',
  1919742 => 'Siler City, NC',
  1919751 => 'Goldsboro, NC',
  1919755 => 'Raleigh, NC',
  1919772 => 'Garner, NC',
  1919773 => 'Garner, NC',
  1919774 => 'Sanford, NC',
  1919775 => 'Sanford, NC',
  1919776 => 'Sanford, NC',
  1919777 => 'Sanford, NC',
  1919778 => 'Goldsboro, NC',
  1919779 => 'Garner, NC',
  191978 => 'Raleigh, NC',
  1919790 => 'Raleigh, NC',
  1919791 => 'Raleigh, NC',
  1919792 => 'Raleigh, NC',
  1919803 => 'Raleigh, NC',
  1919806 => 'Durham, NC',
  1919821 => 'Raleigh, NC',
  1919828 => 'Raleigh, NC',
  1919829 => 'Raleigh, NC',
  191983 => 'Raleigh, NC',
  191984 => 'Raleigh, NC',
  1919840 => 'Morrisville, NC',
  1919843 => 'Chapel Hill, NC',
  1919850 => 'Raleigh, NC',
  1919855 => 'Raleigh, NC',
  1919856 => 'Raleigh, NC',
  1919861 => 'Raleigh, NC',
  1919862 => 'Raleigh, NC',
  1919863 => 'Raleigh, NC',
  191987 => 'Raleigh, NC',
  1919881 => 'Raleigh, NC',
  1919890 => 'Raleigh, NC',
  1919894 => 'Benson, NC',
  1919896 => 'Raleigh, NC',
  1919918 => 'Chapel Hill, NC',
  1919928 => 'Chapel Hill, NC',
  1919929 => 'Chapel Hill, NC',
  1919932 => 'Chapel Hill, NC',
  1919933 => 'Chapel Hill, NC',
  1919934 => 'Smithfield, NC',
  1919936 => 'Princeton, NC',
  1919938 => 'Smithfield, NC',
  1919941 => 'Durham, NC',
  1919942 => 'Chapel Hill, NC',
  1919954 => 'Raleigh, NC',
  1919956 => 'Durham, NC',
  1919957 => 'Durham, NC',
  191996 => 'Chapel Hill, NC',
  1919963 => 'Four Oaks, NC',
  1919965 => 'Selma, NC',
  1919981 => 'Raleigh, NC',
  1919989 => 'Smithfield, NC',
  1920 => 'Wisconsin',
  1920206 => 'Watertown, WI',
  1920208 => 'Sheboygan, WI',
  1920223 => 'Oshkosh, WI',
  192023 => 'Oshkosh, WI',
  1920261 => 'Watertown, WI',
  1920262 => 'Watertown, WI',
  1920269 => 'Lomira, WI',
  1920288 => 'Green Bay, WI',
  1920294 => 'Green Lake, WI',
  1920295 => 'Princeton, WI',
  1920303 => 'Oshkosh, WI',
  1920320 => 'Manitowoc, WI',
  1920322 => 'Fond du Lac, WI',
  1920324 => 'Waupun, WI',
  1920326 => 'Randolph, WI',
  1920330 => 'De Pere, WI',
  1920336 => 'De Pere, WI',
  1920337 => 'De Pere, WI',
  1920338 => 'De Pere, WI',
  1920339 => 'De Pere, WI',
  1920347 => 'De Pere, WI',
  1920356 => 'Beaver Dam, WI',
  1920361 => 'Berlin, WI',
  1920380 => 'Appleton, WI',
  1920386 => 'Juneau, WI',
  1920387 => 'Mayville, WI',
  1920388 => 'Kewaunee, WI',
  1920398 => 'Markesan, WI',
  1920405 => 'Green Bay, WI',
  1920406 => 'Green Bay, WI',
  1920424 => 'Oshkosh, WI',
  1920426 => 'Oshkosh, WI',
  192043 => 'Green Bay, WI',
  1920446 => 'Fremont, WI',
  1920448 => 'Green Bay, WI',
  1920451 => 'Sheboygan, WI',
  1920452 => 'Sheboygan, WI',
  1920457 => 'Sheboygan, WI',
  1920458 => 'Sheboygan, WI',
  1920459 => 'Sheboygan, WI',
  1920465 => 'Green Bay, WI',
  1920467 => 'Sheboygan Falls, WI',
  1920468 => 'Green Bay, WI',
  1920469 => 'Green Bay, WI',
  1920478 => 'Waterloo, WI',
  1920485 => 'Horicon, WI',
  1920487 => 'Algoma, WI',
  192049 => 'Green Bay, WI',
  1920532 => 'Wrightstown, WI',
  1920533 => 'Campbellsport, WI',
  1920544 => 'Green Bay, WI',
  1920563 => 'Fort Atkinson, WI',
  1920564 => 'Oostburg, WI',
  1920568 => 'Fort Atkinson, WI',
  1920574 => 'Appleton, WI',
  1920582 => 'Winneconne, WI',
  1920593 => 'Green Bay, WI',
  1920596 => 'Manawa, WI',
  1920622 => 'Wild Rose, WI',
  1920623 => 'Columbus, WI',
  1920648 => 'Lake Mills, WI',
  1920652 => 'Manitowoc, WI',
  1920662 => 'Green Bay, WI',
  1920668 => 'Cedar Grove, WI',
  1920674 => 'Jefferson, WI',
  1920682 => 'Manitowoc, WI',
  1920683 => 'Manitowoc, WI',
  1920684 => 'Manitowoc, WI',
  1920685 => 'Omro, WI',
  1920686 => 'Manitowoc, WI',
  1920693 => 'Cleveland, WI',
  1920699 => 'Johnson Creek, WI',
  1920720 => 'Neenah, WI',
  1920722 => 'Neenah, WI',
  1920725 => 'Neenah, WI',
  1920727 => 'Neenah, WI',
  1920729 => 'Neenah, WI',
  192073 => 'Appleton, WI',
  1920743 => 'Sturgeon Bay, WI',
  1920746 => 'Sturgeon Bay, WI',
  1920748 => 'Ripon, WI',
  1920749 => 'Appleton, WI',
  1920751 => 'Neenah, WI',
  1920755 => 'Mishicot, WI',
  1920756 => 'Brillion, WI',
  1920757 => 'Greenville, WI',
  1920758 => 'Manitowoc, WI',
  1920759 => 'Kaukauna, WI',
  1920766 => 'Kaukauna, WI',
  1920775 => 'Valders, WI',
  1920779 => 'Hortonville, WI',
  1920787 => 'Wautoma, WI',
  1920793 => 'Two Rivers, WI',
  1920794 => 'Two Rivers, WI',
  1920803 => 'Sheboygan, WI',
  1920822 => 'Pulaski, WI',
  1920826 => 'Abrams, WI',
  1920830 => 'Appleton, WI',
  1920831 => 'Appleton, WI',
  1920832 => 'Appleton, WI',
  1920833 => 'Seymour, WI',
  1920834 => 'Oconto, WI',
  1920836 => 'Larsen, WI',
  1920837 => 'Casco, WI',
  1920839 => 'Baileys Harbor, WI',
  1920842 => 'Suring, WI',
  1920845 => 'Luxemburg, WI',
  1920846 => 'Oconto Falls, WI',
  1920849 => 'Chilton, WI',
  1920853 => 'Hilbert, WI',
  1920854 => 'Sister Bay, WI',
  1920855 => 'Gillett, WI',
  1920863 => 'Denmark, WI',
  1920864 => 'Greenleaf, WI',
  1920866 => 'New Franken, WI',
  1920867 => 'Weyauwega, WI',
  1920868 => 'Fish Creek, WI',
  1920869 => 'Oneida, WI',
  1920876 => 'Elkhart Lake, WI',
  1920882 => 'Appleton, WI',
  1920884 => 'Green Bay, WI',
  1920885 => 'Beaver Dam, WI',
  1920886 => 'Neenah, WI',
  1920887 => 'Beaver Dam, WI',
  1920892 => 'Plymouth, WI',
  1920893 => 'Plymouth, WI',
  1920894 => 'Kiel, WI',
  1920897 => 'Coleman, WI',
  1920898 => 'New Holstein, WI',
  1920907 => 'Fond du Lac, WI',
  192092 => 'Fond du Lac, WI',
  1920928 => 'Fox Lake, WI',
  1920933 => 'Fond du Lac, WI',
  1920954 => 'Appleton, WI',
  1920964 => 'De Pere, WI',
  1920965 => 'Green Bay, WI',
  1920968 => 'Appleton, WI',
  1920969 => 'Neenah, WI',
  1920982 => 'New London, WI',
  1920983 => 'De Pere, WI',
  1920984 => 'Black Creek, WI',
  1920992 => 'Rio, WI',
  1920993 => 'Appleton, WI',
  1920994 => 'Random Lake, WI',
  1920996 => 'Appleton, WI',
  1920997 => 'Appleton, WI',
  1925 => 'California',
  1925210 => 'Walnut Creek, CA',
  1925225 => 'Pleasanton, CA',
  1925227 => 'Pleasanton, CA',
  1925228 => 'Martinez, CA',
  1925229 => 'Martinez, CA',
  1925240 => 'Brentwood, CA',
  1925242 => 'San Ramon, CA',
  1925243 => 'Livermore, CA',
  1925244 => 'San Ramon, CA',
  1925245 => 'Livermore, CA',
  1925249 => 'Pleasanton, CA',
  1925251 => 'Pleasanton, CA',
  1925252 => 'Pittsburg, CA',
  1925253 => 'Orinda, CA',
  1925254 => 'Orinda, CA',
  1925256 => 'Walnut Creek, CA',
  1925258 => 'Orinda, CA',
  1925274 => 'Walnut Creek, CA',
  1925275 => 'San Ramon, CA',
  1925277 => 'San Ramon, CA',
  1925280 => 'Walnut Creek, CA',
  1925283 => 'Lafayette, CA',
  1925284 => 'Lafayette, CA',
  1925287 => 'Walnut Creek, CA',
  1925288 => 'Concord, CA',
  1925292 => 'Livermore, CA',
  1925294 => 'Livermore, CA',
  1925295 => 'Walnut Creek, CA',
  1925296 => 'Walnut Creek, CA',
  1925299 => 'Lafayette, CA',
  1925308 => 'Brentwood, CA',
  1925313 => 'Martinez, CA',
  1925314 => 'Danville, CA',
  1925335 => 'Martinez, CA',
  1925355 => 'San Ramon, CA',
  1925356 => 'Concord, CA',
  1925363 => 'Concord, CA',
  1925370 => 'Martinez, CA',
  1925371 => 'Livermore, CA',
  1925372 => 'Martinez, CA',
  1925373 => 'Livermore, CA',
  1925376 => 'Moraga, CA',
  1925377 => 'Moraga, CA',
  1925416 => 'Pleasanton, CA',
  1925417 => 'Pleasanton, CA',
  1925426 => 'Pleasanton, CA',
  1925427 => 'Pittsburg, CA',
  1925432 => 'Pittsburg, CA',
  1925439 => 'Pittsburg, CA',
  1925443 => 'Livermore, CA',
  1925447 => 'Livermore, CA',
  1925449 => 'Livermore, CA',
  1925454 => 'Livermore, CA',
  1925455 => 'Livermore, CA',
  1925456 => 'Livermore, CA',
  1925458 => 'Bay Point, CA',
  1925460 => 'Pleasanton, CA',
  1925461 => 'Pleasanton, CA',
  1925462 => 'Pleasanton, CA',
  1925463 => 'Pleasanton, CA',
  1925469 => 'Pleasanton, CA',
  1925472 => 'Walnut Creek, CA',
  1925473 => 'Pittsburg, CA',
  1925478 => 'Walnut Creek, CA',
  1925484 => 'Pleasanton, CA',
  1925485 => 'Pleasanton, CA',
  1925513 => 'Brentwood, CA',
  1925516 => 'Brentwood, CA',
  1925521 => 'Concord, CA',
  1925522 => 'Antioch, CA',
  1925543 => 'San Ramon, CA',
  1925551 => 'Dublin, CA',
  1925556 => 'Dublin, CA',
  1925560 => 'Dublin, CA',
  1925600 => 'Pleasanton, CA',
  1925603 => 'Concord, CA',
  1925606 => 'Livermore, CA',
  1925609 => 'Concord, CA',
  1925625 => 'Oakley, CA',
  1925631 => 'Moraga, CA',
  1925634 => 'Brentwood, CA',
  1925648 => 'Danville, CA',
  1925671 => 'Concord, CA',
  1925672 => 'Clayton, CA',
  1925673 => 'Clayton, CA',
  1925674 => 'Concord, CA',
  1925676 => 'Concord, CA',
  1925679 => 'Oakley, CA',
  192568 => 'Concord, CA',
  1925684 => 'Bethel Island, CA',
  1925691 => 'Concord, CA',
  1925706 => 'Antioch, CA',
  1925734 => 'Pleasanton, CA',
  1925735 => 'San Ramon, CA',
  1925736 => 'Danville, CA',
  1925743 => 'Danville, CA',
  1925754 => 'Antioch, CA',
  1925755 => 'Antioch, CA',
  1925756 => 'Antioch, CA',
  1925757 => 'Antioch, CA',
  1925776 => 'Antioch, CA',
  1925777 => 'Antioch, CA',
  1925778 => 'Antioch, CA',
  1925779 => 'Antioch, CA',
  1925798 => 'Concord, CA',
  1925803 => 'Dublin, CA',
  1925813 => 'Antioch, CA',
  1925820 => 'Danville, CA',
  1925825 => 'Concord, CA',
  1925827 => 'Concord, CA',
  1925828 => 'Dublin, CA',
  1925829 => 'Dublin, CA',
  1925830 => 'San Ramon, CA',
  1925833 => 'Dublin, CA',
  1925837 => 'Danville, CA',
  1925846 => 'Pleasanton, CA',
  1925847 => 'Pleasanton, CA',
  1925849 => 'Concord, CA',
  1925866 => 'San Ramon, CA',
  1925875 => 'Dublin, CA',
  1925906 => 'Walnut Creek, CA',
  1925924 => 'Pleasanton, CA',
  192593 => 'Walnut Creek, CA',
  1925931 => 'Pleasanton, CA',
  1925943 => 'Walnut Creek, CA',
  1925944 => 'Walnut Creek, CA',
  1925945 => 'Walnut Creek, CA',
  1925946 => 'Walnut Creek, CA',
  1925947 => 'Walnut Creek, CA',
  1925952 => 'Walnut Creek, CA',
  1925954 => 'Walnut Creek, CA',
  1925957 => 'Martinez, CA',
  1925960 => 'Livermore, CA',
  1925962 => 'Lafayette, CA',
  1925969 => 'Concord, CA',
  1925978 => 'Antioch, CA',
  1925979 => 'Walnut Creek, CA',
  1925988 => 'Walnut Creek, CA',
  1928 => 'Arizona',
  1928203 => 'Sedona, AZ',
  1928204 => 'Sedona, AZ',
  1928213 => 'Flagstaff, AZ',
  1928214 => 'Flagstaff, AZ',
  1928226 => 'Flagstaff, AZ',
  1928237 => 'Prescott, AZ',
  1928282 => 'Sedona, AZ',
  1928283 => 'Tuba City, AZ',
  1928284 => 'Sedona, AZ',
  1928289 => 'Winslow, AZ',
  1928314 => 'Yuma, AZ',
  1928317 => 'Yuma, AZ',
  1928329 => 'Yuma, AZ',
  1928333 => 'Springerville, AZ',
  1928337 => 'St. Johns, AZ',
  1928338 => 'Whiteriver, AZ',
  1928341 => 'Yuma, AZ',
  1928342 => 'Yuma, AZ',
  1928343 => 'Yuma, AZ',
  1928344 => 'Yuma, AZ',
  1928345 => 'Yuma, AZ',
  1928348 => 'Safford, AZ',
  1928367 => 'Pinetop, AZ',
  1928368 => 'Lakeside, AZ',
  1928373 => 'Yuma, AZ',
  1928425 => 'Globe, AZ',
  1928428 => 'Safford, AZ',
  1928442 => 'Prescott, AZ',
  1928443 => 'Prescott, AZ',
  1928445 => 'Prescott, AZ',
  1928453 => 'Lake Havasu City, AZ',
  1928468 => 'Payson, AZ',
  1928472 => 'Payson, AZ',
  1928474 => 'Payson, AZ',
  1928475 => 'San Carlos, AZ',
  1928476 => 'Pine, AZ',
  1928505 => 'Lake Havasu City, AZ',
  1928522 => 'Flagstaff, AZ',
  1928524 => 'Holbrook, AZ',
  1928526 => 'Flagstaff, AZ',
  1928527 => 'Flagstaff, AZ',
  1928532 => 'Show Low, AZ',
  1928536 => 'Snowflake, AZ',
  1928537 => 'Show Low, AZ',
  1928541 => 'Prescott, AZ',
  1928556 => 'Flagstaff, AZ',
  1928565 => 'Golden Valley, AZ',
  1928567 => 'Camp Verde, AZ',
  1928607 => 'Flagstaff, AZ',
  1928634 => 'Cottonwood, AZ',
  1928635 => 'Williams, AZ',
  1928636 => 'Chino Valley, AZ',
  1928638 => 'Grand Canyon Village, AZ',
  1928639 => 'Cottonwood, AZ',
  1928645 => 'Page, AZ',
  1928649 => 'Cottonwood, AZ',
  1928669 => 'Parker, AZ',
  1928674 => 'Chinle, AZ',
  1928680 => 'Lake Havasu City, AZ',
  1928681 => 'Kingman, AZ',
  1928684 => 'Wickenburg, AZ',
  1928692 => 'Kingman, AZ',
  1928697 => 'Kayenta, AZ',
  1928699 => 'Flagstaff, AZ',
  1928704 => 'Bullhead City, AZ',
  1928708 => 'Prescott, AZ',
  1928710 => 'Prescott, AZ',
  1928714 => 'Flagstaff, AZ',
  1928717 => 'Prescott, AZ',
  1928718 => 'Kingman, AZ',
  1928726 => 'Yuma, AZ',
  1928729 => 'Fort Defiance, AZ',
  1928753 => 'Kingman, AZ',
  1928754 => 'Bullhead City, AZ',
  1928757 => 'Kingman, AZ',
  1928758 => 'Bullhead City, AZ',
  1928759 => 'Prescott Valley, AZ',
  1928763 => 'Bullhead City, AZ',
  1928764 => 'Lake Havasu City, AZ',
  1928771 => 'Prescott, AZ',
  1928772 => 'Prescott Valley, AZ',
  1928773 => 'Flagstaff, AZ',
  1928774 => 'Flagstaff, AZ',
  1928775 => 'Prescott Valley, AZ',
  1928776 => 'Prescott, AZ',
  1928777 => 'Prescott, AZ',
  1928778 => 'Prescott, AZ',
  1928779 => 'Flagstaff, AZ',
  1928782 => 'Yuma, AZ',
  1928783 => 'Yuma, AZ',
  1928785 => 'Wellton, AZ',
  1928788 => 'Fort Mohave, AZ',
  1928853 => 'Flagstaff, AZ',
  1928854 => 'Lake Havasu City, AZ',
  1928855 => 'Lake Havasu City, AZ',
  1928859 => 'Salome, AZ',
  1928865 => 'Clifton, AZ',
  1928871 => 'Window Rock, AZ',
  1928899 => 'Prescott, AZ',
  1928927 => 'Quartzsite, AZ',
  1929 => 'New York',
  1930 => 'Indiana',
  1931 => 'Tennessee',
  1931221 => 'Clarksville, TN',
  1931223 => 'Columbia, TN',
  1931232 => 'Dover, TN',
  1931243 => 'Celina, TN',
  1931245 => 'Clarksville, TN',
  1931268 => 'Gainesboro, TN',
  1931270 => 'Lewisburg, TN',
  1931289 => 'Erin, TN',
  1931296 => 'Waverly, TN',
  1931320 => 'Clarksville, TN',
  1931358 => 'Clarksville, TN',
  1931359 => 'Lewisburg, TN',
  1931363 => 'Pulaski, TN',
  1931364 => 'Chapel Hill, TN',
  1931372 => 'Cookeville, TN',
  1931379 => 'Mount Pleasant, TN',
  1931380 => 'Columbia, TN',
  1931381 => 'Columbia, TN',
  1931388 => 'Columbia, TN',
  1931393 => 'Tullahoma, TN',
  1931424 => 'Pulaski, TN',
  1931427 => 'Ardmore, TN',
  1931431 => 'Clarksville, TN',
  1931432 => 'Cookeville, TN',
  1931433 => 'Fayetteville, TN',
  1931438 => 'Fayetteville, TN',
  1931454 => 'Tullahoma, TN',
  1931455 => 'Tullahoma, TN',
  1931456 => 'Crossville, TN',
  1931461 => 'Tullahoma, TN',
  1931473 => 'McMinnville, TN',
  1931474 => 'McMinnville, TN',
  1931484 => 'Crossville, TN',
  1931486 => 'Spring Hill, TN',
  1931490 => 'Columbia, TN',
  1931503 => 'Clarksville, TN',
  1931507 => 'McMinnville, TN',
  1931520 => 'Cookeville, TN',
  1931525 => 'Cookeville, TN',
  1931526 => 'Cookeville, TN',
  1931528 => 'Cookeville, TN',
  1931535 => 'New Johnsonville, TN',
  1931537 => 'Cookeville, TN',
  1931542 => 'Clarksville, TN',
  1931551 => 'Clarksville, TN',
  1931552 => 'Clarksville, TN',
  1931553 => 'Clarksville, TN',
  1931582 => 'McEwen, TN',
  1931589 => 'Linden, TN',
  1931592 => 'Tracy City, TN',
  1931598 => 'Sewanee, TN',
  1931645 => 'Clarksville, TN',
  1931647 => 'Clarksville, TN',
  1931648 => 'Clarksville, TN',
  1931668 => 'McMinnville, TN',
  1931670 => 'Lyles, TN',
  1931680 => 'Shelbyville, TN',
  1931684 => 'Shelbyville, TN',
  1931685 => 'Shelbyville, TN',
  1931686 => 'Rock Island, TN',
  1931707 => 'Crossville, TN',
  1931722 => 'Waynesboro, TN',
  1931723 => 'Manchester, TN',
  1931724 => 'Collinwood, TN',
  1931728 => 'Manchester, TN',
  1931729 => 'Centerville, TN',
  1931738 => 'Sparta, TN',
  1931759 => 'Lynchburg, TN',
  1931761 => 'Sparta, TN',
  1931762 => 'Lawrenceburg, TN',
  1931766 => 'Lawrenceburg, TN',
  1931779 => 'Gruetli-Laager, TN',
  1931787 => 'Crossville, TN',
  1931788 => 'Crossville, TN',
  1931796 => 'Hohenwald, TN',
  1931802 => 'Clarksville, TN',
  1931815 => 'McMinnville, TN',
  1931823 => 'Livingston, TN',
  1931836 => 'Sparta, TN',
  1931837 => 'Sparta, TN',
  1931839 => 'Monterey, TN',
  1931840 => 'Columbia, TN',
  1931852 => 'Leoma, TN',
  1931853 => 'Loretto, TN',
  1931858 => 'Baxter, TN',
  1931863 => 'Clarkrange, TN',
  1931864 => 'Byrdstown, TN',
  1931879 => 'Jamestown, TN',
  1931905 => 'Clarksville, TN',
  1931906 => 'Clarksville, TN',
  1931920 => 'Clarksville, TN',
  1931924 => 'Monteagle, TN',
  1931946 => 'Spencer, TN',
  1931962 => 'Winchester, TN',
  1931967 => 'Winchester, TN',
  1934 => 'New York, NY',
  1936 => 'Texas',
  1936231 => 'Conroe, TX',
  1936254 => 'Timpson, TX',
  1936257 => 'Dayton, TX',
  1936258 => 'Dayton, TX',
  1936264 => 'Conroe, TX',
  1936269 => 'Joaquin, TX',
  1936271 => 'Conroe, TX',
  1936273 => 'Conroe, TX',
  1936275 => 'San Augustine, TX',
  1936291 => 'Huntsville, TX',
  1936293 => 'Huntsville, TX',
  1936294 => 'Huntsville, TX',
  1936295 => 'Huntsville, TX',
  1936327 => 'Livingston, TX',
  1936328 => 'Livingston, TX',
  1936334 => 'Liberty, TX',
  1936336 => 'Liberty, TX',
  1936344 => 'New Waverly, TX',
  1936348 => 'Madisonville, TX',
  1936372 => 'Waller, TX',
  1936398 => 'Corrigan, TX',
  1936435 => 'Huntsville, TX',
  1936436 => 'Huntsville, TX',
  1936439 => 'Huntsville, TX',
  1936441 => 'Conroe, TX',
  1936448 => 'Montgomery, TX',
  1936449 => 'Montgomery, TX',
  1936462 => 'Nacogdoches, TX',
  1936494 => 'Conroe, TX',
  1936522 => 'Conroe, TX',
  1936539 => 'Conroe, TX',
  1936544 => 'Crockett, TX',
  1936559 => 'Nacogdoches, TX',
  1936560 => 'Nacogdoches, TX',
  1936564 => 'Nacogdoches, TX',
  1936569 => 'Nacogdoches, TX',
  1936582 => 'Montgomery, TX',
  1936588 => 'Montgomery, TX',
  1936590 => 'Center, TX',
  1936591 => 'Center, TX',
  1936594 => 'Trinity, TX',
  1936597 => 'Montgomery, TX',
  1936598 => 'Center, TX',
  1936628 => 'Shepherd, TX',
  1936632 => 'Lufkin, TX',
  1936633 => 'Lufkin, TX',
  1936634 => 'Lufkin, TX',
  1936637 => 'Lufkin, TX',
  1936639 => 'Lufkin, TX',
  1936642 => 'Groveton, TX',
  1936646 => 'Onalaska, TX',
  1936647 => 'Conroe, TX',
  1936653 => 'Coldspring, TX',
  1936687 => 'Grapeland, TX',
  1936699 => 'Lufkin, TX',
  1936756 => 'Conroe, TX',
  1936760 => 'Conroe, TX',
  1936788 => 'Conroe, TX',
  1936825 => 'Navasota, TX',
  1936829 => 'Diboll, TX',
  1936856 => 'Willis, TX',
  1936858 => 'Alto, TX',
  1936875 => 'Lufkin, TX',
  1936890 => 'Willis, TX',
  1936931 => 'Waller, TX',
  1936967 => 'Livingston, TX',
  1937 => 'Ohio',
  1937208 => 'Dayton, OH',
  193722 => 'Dayton, OH',
  1937233 => 'Dayton, OH',
  1937235 => 'Dayton, OH',
  1937236 => 'Dayton, OH',
  1937237 => 'Dayton, OH',
  193725 => 'Dayton, OH',
  1937262 => 'Dayton, OH',
  1937263 => 'Dayton, OH',
  1937264 => 'Dayton, OH',
  1937268 => 'Dayton, OH',
  193727 => 'Dayton, OH',
  193729 => 'Dayton, OH',
  1937292 => 'Bellefontaine, OH',
  1937295 => 'Fort Loramie, OH',
  1937312 => 'Dayton, OH',
  1937320 => 'Beavercreek, OH',
  1937322 => 'Springfield, OH',
  1937323 => 'Springfield, OH',
  1937324 => 'Springfield, OH',
  1937325 => 'Springfield, OH',
  1937328 => 'Springfield, OH',
  1937332 => 'Troy, OH',
  1937333 => 'Dayton, OH',
  1937335 => 'Troy, OH',
  1937339 => 'Troy, OH',
  1937342 => 'Springfield, OH',
  1937352 => 'Xenia, OH',
  1937364 => 'Lynchburg, OH',
  1937372 => 'Xenia, OH',
  1937374 => 'Xenia, OH',
  1937376 => 'Xenia, OH',
  1937378 => 'Georgetown, OH',
  1937382 => 'Wilmington, OH',
  1937383 => 'Wilmington, OH',
  1937384 => 'Miamisburg, OH',
  1937386 => 'Seaman, OH',
  1937390 => 'Springfield, OH',
  1937392 => 'Ripley, OH',
  1937393 => 'Hillsboro, OH',
  1937399 => 'Springfield, OH',
  1937415 => 'Dayton, OH',
  1937424 => 'Dayton, OH',
  1937426 => 'Beavercreek, OH',
  1937427 => 'Beavercreek, OH',
  1937428 => 'Dayton, OH',
  1937429 => 'Beavercreek, OH',
  193743 => 'Dayton, OH',
  1937431 => 'Beavercreek, OH',
  1937437 => 'New Paris, OH',
  1937440 => 'Troy, OH',
  1937443 => 'Dayton, OH',
  1937444 => 'Mount Orab, OH',
  1937446 => 'Sardinia, OH',
  1937452 => 'Camden, OH',
  1937454 => 'Dayton, OH',
  1937456 => 'Eaton, OH',
  1937461 => 'Dayton, OH',
  1937465 => 'West Liberty, OH',
  1937473 => 'Covington, OH',
  1937484 => 'Urbana, OH',
  1937492 => 'Sidney, OH',
  1937496 => 'Dayton, OH',
  1937497 => 'Sidney, OH',
  1937498 => 'Sidney, OH',
  1937525 => 'Springfield, OH',
  1937526 => 'Versailles, OH',
  1937544 => 'West Union, OH',
  1937547 => 'Greenville, OH',
  1937548 => 'Greenville, OH',
  1937549 => 'Manchester, OH',
  1937558 => 'Dayton, OH',
  1937578 => 'Marysville, OH',
  1937584 => 'Sabina, OH',
  1937585 => 'De Graff, OH',
  1937587 => 'Peebles, OH',
  1937592 => 'Bellefontaine, OH',
  1937593 => 'Bellefontaine, OH',
  1937596 => 'Jackson Center, OH',
  1937599 => 'Bellefontaine, OH',
  1937610 => 'Dayton, OH',
  1937615 => 'Piqua, OH',
  1937641 => 'Dayton, OH',
  1937642 => 'Marysville, OH',
  1937644 => 'Marysville, OH',
  1937652 => 'Urbana, OH',
  1937653 => 'Urbana, OH',
  1937660 => 'Dayton, OH',
  1937663 => 'St. Paris, OH',
  1937667 => 'Tipp City, OH',
  1937669 => 'Tipp City, OH',
  1937675 => 'Jamestown, OH',
  1937687 => 'New Lebanon, OH',
  1937692 => 'Arcanum, OH',
  1937698 => 'West Milton, OH',
  1937723 => 'Dayton, OH',
  1937743 => 'Franklin, OH',
  1937746 => 'Franklin, OH',
  1937748 => 'Springboro, OH',
  1937754 => 'Fairborn, OH',
  1937766 => 'Cedarville, OH',
  1937767 => 'Yellow Springs, OH',
  1937773 => 'Piqua, OH',
  1937778 => 'Piqua, OH',
  1937780 => 'Leesburg, OH',
  1937783 => 'Blanchester, OH',
  1937833 => 'Brookville, OH',
  1937834 => 'Mechanicsburg, OH',
  1937836 => 'Englewood, OH',
  1937839 => 'West Alexandria, OH',
  1937845 => 'New Carlisle, OH',
  1937847 => 'Miamisburg, OH',
  1937848 => 'Bellbrook, OH',
  1937849 => 'New Carlisle, OH',
  1937855 => 'Germantown, OH',
  1937864 => 'Enon, OH',
  1937865 => 'Miamisburg, OH',
  1937866 => 'Miamisburg, OH',
  1937878 => 'Fairborn, OH',
  1937879 => 'Fairborn, OH',
  1937890 => 'Dayton, OH',
  1937898 => 'Vandalia, OH',
  1937912 => 'Beavercreek, OH',
  1937938 => 'Dayton, OH',
  1937962 => 'Lewisburg, OH',
  1937964 => 'Springfield, OH',
  1937981 => 'Greenfield, OH',
  1938 => 'Alabama',
  1940 => 'Texas',
  1940228 => 'Wichita Falls, TX',
  1940243 => 'Denton, TX',
  1940320 => 'Denton, TX',
  1940322 => 'Wichita Falls, TX',
  1940323 => 'Denton, TX',
  1940325 => 'Mineral Wells, TX',
  1940328 => 'Mineral Wells, TX',
  1940349 => 'Denton, TX',
  1940365 => 'Aubrey, TX',
  194038 => 'Denton, TX',
  1940397 => 'Wichita Falls, TX',
  1940433 => 'Boyd, TX',
  1940442 => 'Denton, TX',
  1940458 => 'Sanger, TX',
  1940464 => 'Argyle, TX',
  1940479 => 'Ponder, TX',
  1940482 => 'Krum, TX',
  1940484 => 'Denton, TX',
  1940495 => 'Electra, TX',
  1940521 => 'Graham, TX',
  1940538 => 'Henrietta, TX',
  1940549 => 'Graham, TX',
  1940552 => 'Vernon, TX',
  1940553 => 'Vernon, TX',
  1940564 => 'Olney, TX',
  1940565 => 'Denton, TX',
  1940566 => 'Denton, TX',
  1940567 => 'Jacksboro, TX',
  1940569 => 'Burkburnett, TX',
  1940574 => 'Archer City, TX',
  1940591 => 'Denton, TX',
  1940592 => 'Iowa Park, TX',
  1940612 => 'Gainesville, TX',
  1940626 => 'Decatur, TX',
  1940627 => 'Decatur, TX',
  1940644 => 'Chico, TX',
  1940648 => 'Justin, TX',
  1940663 => 'Quanah, TX',
  1940665 => 'Gainesville, TX',
  1940668 => 'Gainesville, TX',
  1940676 => 'Sheppard AFB, Wichita Falls, TX',
  1940683 => 'Bridgeport, TX',
  1940686 => 'Pilot Point, TX',
  1940687 => 'Wichita Falls, TX',
  1940689 => 'Wichita Falls, TX',
  1940691 => 'Wichita Falls, TX',
  1940692 => 'Wichita Falls, TX',
  1940696 => 'Wichita Falls, TX',
  1940716 => 'Wichita Falls, TX',
  1940723 => 'Wichita Falls, TX',
  1940759 => 'Muenster, TX',
  1940761 => 'Wichita Falls, TX',
  1940766 => 'Wichita Falls, TX',
  1940767 => 'Wichita Falls, TX',
  1940779 => 'Graford, TX',
  1940825 => 'Nocona, TX',
  1940851 => 'Wichita Falls, TX',
  1940855 => 'Wichita Falls, TX',
  1940864 => 'Haskell, TX',
  1940872 => 'Bowie, TX',
  1940889 => 'Seymour, TX',
  1940891 => 'Denton, TX',
  1940898 => 'Denton, TX',
  1940937 => 'Childress, TX',
  1941 => 'Florida',
  1941205 => 'Punta Gorda, FL',
  1941235 => 'Port Charlotte, FL',
  1941240 => 'North Port, FL',
  1941244 => 'Venice, FL',
  1941255 => 'Port Charlotte, FL',
  1941284 => 'Sarasota, FL',
  1941302 => 'Sarasota, FL',
  1941306 => 'Sarasota, FL',
  1941308 => 'Sarasota, FL',
  1941312 => 'Sarasota, FL',
  1941316 => 'Sarasota, FL',
  1941320 => 'Sarasota, FL',
  1941321 => 'Sarasota, FL',
  1941322 => 'Myakka City, FL',
  1941330 => 'Sarasota, FL',
  1941342 => 'Sarasota, FL',
  1941343 => 'Sarasota, FL',
  1941346 => 'Sarasota, FL',
  1941347 => 'Punta Gorda, FL',
  1941349 => 'Sarasota, FL',
  1941350 => 'Sarasota, FL',
  1941351 => 'Sarasota, FL',
  1941355 => 'Sarasota, FL',
  1941358 => 'Sarasota, FL',
  1941359 => 'Sarasota, FL',
  194136 => 'Sarasota, FL',
  1941371 => 'Sarasota, FL',
  1941373 => 'Sarasota, FL',
  1941377 => 'Sarasota, FL',
  1941378 => 'Sarasota, FL',
  1941379 => 'Sarasota, FL',
  1941383 => 'Longboat Key, FL',
  1941387 => 'Longboat Key, FL',
  1941388 => 'Sarasota, FL',
  1941391 => 'Port Charlotte, FL',
  1941400 => 'Sarasota, FL',
  1941408 => 'Venice, FL',
  1941412 => 'Venice, FL',
  1941423 => 'North Port, FL',
  1941426 => 'North Port, FL',
  1941429 => 'North Port, FL',
  1941444 => 'Sarasota, FL',
  1941460 => 'Englewood, FL',
  1941473 => 'Englewood, FL',
  1941474 => 'Englewood, FL',
  1941475 => 'Englewood, FL',
  194148 => 'Venice, FL',
  1941487 => 'Sarasota, FL',
  1941492 => 'Venice, FL',
  1941493 => 'Venice, FL',
  1941496 => 'Venice, FL',
  1941497 => 'Venice, FL',
  1941505 => 'Punta Gorda, FL',
  1941544 => 'Sarasota, FL',
  1941552 => 'Sarasota, FL',
  1941554 => 'Sarasota, FL',
  1941556 => 'Sarasota, FL',
  1941564 => 'North Port, FL',
  1941567 => 'Bradenton, FL',
  1941575 => 'Punta Gorda, FL',
  1941586 => 'Sarasota, FL',
  1941613 => 'Port Charlotte, FL',
  1941623 => 'Port Charlotte, FL',
  1941624 => 'Port Charlotte, FL',
  1941625 => 'Port Charlotte, FL',
  1941627 => 'Port Charlotte, FL',
  1941629 => 'Port Charlotte, FL',
  1941637 => 'Punta Gorda, FL',
  1941639 => 'Punta Gorda, FL',
  1941706 => 'Sarasota, FL',
  1941708 => 'Bradenton, FL',
  1941721 => 'Palmetto, FL',
  1941722 => 'Palmetto, FL',
  1941723 => 'Palmetto, FL',
  1941727 => 'Bradenton, FL',
  1941729 => 'Palmetto, FL',
  1941739 => 'Bradenton, FL',
  194174 => 'Bradenton, FL',
  1941743 => 'Port Charlotte, FL',
  194175 => 'Bradenton, FL',
  1941761 => 'Bradenton, FL',
  1941764 => 'Port Charlotte, FL',
  1941766 => 'Port Charlotte, FL',
  1941776 => 'Parrish, FL',
  1941782 => 'Bradenton, FL',
  1941792 => 'Bradenton, FL',
  1941794 => 'Bradenton, FL',
  1941795 => 'Bradenton, FL',
  1941798 => 'Bradenton, FL',
  1941809 => 'Sarasota, FL',
  1941822 => 'Sarasota, FL',
  1941833 => 'Punta Gorda, FL',
  1941861 => 'Sarasota, FL',
  1941870 => 'Sarasota, FL',
  1941894 => 'Sarasota, FL',
  1941896 => 'Bradenton, FL',
  1941906 => 'Sarasota, FL',
  1941917 => 'Sarasota, FL',
  194192 => 'Sarasota, FL',
  1941932 => 'Bradenton, FL',
  194195 => 'Sarasota, FL',
  1941964 => 'Boca Grande, FL',
  1941979 => 'Port Charlotte, FL',
  1943 => 'Georgia',
  1945 => 'Texas',
  1947 => 'Michigan',
  1948 => 'Virginia',
  1949 => 'California',
  1949221 => 'Irvine, CA',
  1949249 => 'Laguna Niguel, CA',
  1949253 => 'Irvine, CA',
  1949258 => 'Newport Beach, CA',
  1949260 => 'Irvine, CA',
  1949262 => 'Irvine, CA',
  1949333 => 'Irvine, CA',
  1949336 => 'Irvine, CA',
  1949341 => 'Irvine, CA',
  1949347 => 'Mission Viejo, CA',
  1949361 => 'San Clemente, CA',
  1949363 => 'Laguna Niguel, CA',
  1949364 => 'Mission Viejo, CA',
  1949366 => 'San Clemente, CA',
  1949369 => 'San Clemente, CA',
  1949376 => 'Laguna Beach, CA',
  1949387 => 'Irvine, CA',
  1949425 => 'Aliso Viejo, CA',
  1949428 => 'Irvine, CA',
  1949442 => 'Irvine, CA',
  1949450 => 'Irvine, CA',
  1949452 => 'Laguna Hills, CA',
  1949453 => 'Irvine, CA',
  1949474 => 'Irvine, CA',
  1949477 => 'Irvine, CA',
  1949492 => 'San Clemente, CA',
  1949494 => 'Laguna Beach, CA',
  1949495 => 'Laguna Niguel, CA',
  1949497 => 'Laguna Beach, CA',
  1949498 => 'San Clemente, CA',
  1949499 => 'Laguna Beach, CA',
  1949502 => 'Irvine, CA',
  1949509 => 'Irvine, CA',
  1949515 => 'Costa Mesa, CA',
  1949548 => 'Costa Mesa, CA',
  1949551 => 'Irvine, CA',
  1949552 => 'Irvine, CA',
  1949553 => 'Irvine, CA',
  1949559 => 'Irvine, CA',
  1949585 => 'Irvine, CA',
  1949622 => 'Irvine, CA',
  1949631 => 'Costa Mesa, CA',
  1949640 => 'Newport Beach, CA',
  1949642 => 'Costa Mesa, CA',
  1949644 => 'Newport Beach, CA',
  1949645 => 'Costa Mesa, CA',
  1949646 => 'Costa Mesa, CA',
  1949650 => 'Costa Mesa, CA',
  1949651 => 'Irvine, CA',
  1949653 => 'Irvine, CA',
  1949654 => 'Irvine, CA',
  1949660 => 'Irvine, CA',
  1949673 => 'Newport Beach, CA',
  1949675 => 'Newport Beach, CA',
  1949679 => 'Irvine, CA',
  1949706 => 'Newport Beach, CA',
  1949715 => 'Laguna Beach, CA',
  1949717 => 'Newport Beach, CA',
  1949718 => 'Newport Beach, CA',
  1949719 => 'Newport Beach, CA',
  1949720 => 'Newport Beach, CA',
  1949721 => 'Newport Beach, CA',
  1949722 => 'Costa Mesa, CA',
  1949723 => 'Newport Beach, CA',
  1949724 => 'Irvine, CA',
  1949725 => 'Irvine, CA',
  1949726 => 'Irvine, CA',
  1949727 => 'Irvine, CA',
  1949733 => 'Irvine, CA',
  1949748 => 'Irvine, CA',
  1949753 => 'Irvine, CA',
  1949757 => 'Irvine, CA',
  1949759 => 'Newport Beach, CA',
  1949760 => 'Newport Beach, CA',
  1949764 => 'Newport Beach, CA',
  1949769 => 'Irvine, CA',
  1949786 => 'Irvine, CA',
  1949788 => 'Irvine, CA',
  1949824 => 'Irvine, CA',
  1949854 => 'Irvine, CA',
  1949857 => 'Irvine, CA',
  1949861 => 'Irvine, CA',
  1949940 => 'San Clemente, CA',
  1951 => 'California',
  1951222 => 'Riverside, CA',
  1951225 => 'Temecula, CA',
  1951242 => 'Moreno Valley, CA',
  1951243 => 'Moreno Valley, CA',
  1951244 => 'Canyon Lake, CA',
  1951245 => 'Lake Elsinore, CA',
  1951247 => 'Moreno Valley, CA',
  1951248 => 'Riverside, CA',
  195127 => 'Corona, CA',
  1951274 => 'Riverside, CA',
  1951275 => 'Riverside, CA',
  1951276 => 'Riverside, CA',
  1951280 => 'Corona, CA',
  1951296 => 'Temecula, CA',
  1951302 => 'Temecula, CA',
  1951303 => 'Temecula, CA',
  1951304 => 'Murrieta, CA',
  1951308 => 'Temecula, CA',
  1951328 => 'Riverside, CA',
  1951340 => 'Corona, CA',
  1951343 => 'Riverside, CA',
  195135 => 'Riverside, CA',
  1951369 => 'Riverside, CA',
  1951371 => 'Corona, CA',
  1951372 => 'Corona, CA',
  1951413 => 'Moreno Valley, CA',
  1951443 => 'Perris, CA',
  1951445 => 'Murrieta, CA',
  1951461 => 'Murrieta, CA',
  1951471 => 'Lake Elsinore, CA',
  1951485 => 'Moreno Valley, CA',
  1951486 => 'Moreno Valley, CA',
  1951487 => 'San Jacinto, CA',
  1951491 => 'Temecula, CA',
  1951506 => 'Temecula, CA',
  1951509 => 'Riverside, CA',
  1951520 => 'Corona, CA',
  1951549 => 'Corona, CA',
  1951571 => 'Moreno Valley, CA',
  1951582 => 'Corona, CA',
  1951587 => 'Temecula, CA',
  1951600 => 'Murrieta, CA',
  1951601 => 'Moreno Valley, CA',
  1951609 => 'Wildomar, CA',
  1951637 => 'Riverside, CA',
  1951652 => 'Hemet, CA',
  1951653 => 'Moreno Valley, CA',
  1951654 => 'San Jacinto, CA',
  1951657 => 'Perris, CA',
  1951658 => 'Hemet, CA',
  1951659 => 'Idyllwild-Pine Cove, CA',
  1951674 => 'Lake Elsinore, CA',
  1951676 => 'Temecula, CA',
  1951677 => 'Murrieta, CA',
  195168 => 'Riverside, CA',
  1951693 => 'Temecula, CA',
  1951694 => 'Temecula, CA',
  1951695 => 'Temecula, CA',
  1951696 => 'Murrieta, CA',
  1951698 => 'Murrieta, CA',
  1951699 => 'Temecula, CA',
  1951719 => 'Temecula, CA',
  195173 => 'Corona, CA',
  1951763 => 'Anza, CA',
  1951765 => 'Hemet, CA',
  1951766 => 'Hemet, CA',
  1951769 => 'Beaumont, CA',
  1951776 => 'Riverside, CA',
  1951779 => 'Riverside, CA',
  195178 => 'Riverside, CA',
  1951808 => 'Corona, CA',
  1951817 => 'Corona, CA',
  1951845 => 'Beaumont, CA',
  1951849 => 'Banning, CA',
  1951894 => 'Murrieta, CA',
  1951898 => 'Corona, CA',
  1951922 => 'Banning, CA',
  1951924 => 'Moreno Valley, CA',
  1951925 => 'Hemet, CA',
  1951927 => 'Hemet, CA',
  1951929 => 'Hemet, CA',
  1951940 => 'Perris, CA',
  1951943 => 'Perris, CA',
  1951955 => 'Riverside, CA',
  1951977 => 'Riverside, CA',
  1952 => 'Minnesota',
  1952233 => 'Shakopee, MN',
  1952361 => 'Chaska, MN',
  1952368 => 'Chaska, MN',
  1952403 => 'Shakopee, MN',
  1952423 => 'Apple Valley, MN',
  1952432 => 'Apple Valley, MN',
  1952435 => 'Burnsville, MN',
  1952440 => 'Prior Lake, MN',
  1952442 => 'Waconia, MN',
  1952443 => 'Victoria, MN',
  1952445 => 'Shakopee, MN',
  1952446 => 'St. Bonifacius, MN',
  1952447 => 'Prior Lake, MN',
  1952448 => 'Chaska, MN',
  1952466 => 'Cologne, MN',
  1952469 => 'Lakeville, MN',
  1952472 => 'Mound, MN',
  1952473 => 'Wayzata, MN',
  1952474 => 'Excelsior, MN',
  1952475 => 'Wayzata, MN',
  1952476 => 'Wayzata, MN',
  1952492 => 'Jordan, MN',
  1952496 => 'Shakopee, MN',
  1952556 => 'Chaska, MN',
  1952707 => 'Burnsville, MN',
  1952736 => 'Burnsville, MN',
  1952758 => 'New Prague, MN',
  1952808 => 'Burnsville, MN',
  1952829 => 'Eden Prairie, MN',
  1952848 => 'Edina, MN',
  1952854 => 'Bloomington, MN',
  1952858 => 'Bloomington, MN',
  1952873 => 'Belle Plaine, MN',
  1952882 => 'Burnsville, MN',
  1952883 => 'Minneapolis, MN',
  1952885 => 'East Bloomington, Bloomington, MN',
  1952890 => 'Burnsville, MN',
  1952892 => 'Burnsville, MN',
  1952894 => 'Burnsville, MN',
  1952895 => 'Burnsville, MN',
  1952924 => 'Minneapolis, MN',
  1952934 => 'Eden Prairie, MN',
  1952937 => 'Eden Prairie, MN',
  1952941 => 'Eden Prairie, MN',
  1952942 => 'Eden Prairie, MN',
  1952944 => 'Eden Prairie, MN',
  1952949 => 'Eden Prairie, MN',
  1952955 => 'Watertown, MN',
  1952975 => 'Eden Prairie, MN',
  1952985 => 'Lakeville, MN',
  1954 => 'Florida',
  1954202 => 'Fort Lauderdale, FL',
  1954217 => 'Weston, FL',
  1954227 => 'Coral Springs, FL',
  1954229 => 'Fort Lauderdale, FL',
  1954255 => 'Coral Springs, FL',
  1954262 => 'Davie, FL',
  1954265 => 'Hollywood, FL',
  1954267 => 'Fort Lauderdale, FL',
  1954332 => 'Fort Lauderdale, FL',
  1954340 => 'Coral Springs, FL',
  1954341 => 'Coral Springs, FL',
  1954344 => 'Coral Springs, FL',
  1954345 => 'Coral Springs, FL',
  1954346 => 'Coral Springs, FL',
  1954349 => 'Weston, FL',
  1954351 => 'Fort Lauderdale, FL',
  1954355 => 'Fort Lauderdale, FL',
  1954359 => 'Fort Lauderdale, FL',
  1954360 => 'Deerfield Beach, FL',
  1954384 => 'Weston, FL',
  1954385 => 'Weston, FL',
  1954389 => 'Weston, FL',
  1954396 => 'Fort Lauderdale, FL',
  1954418 => 'Deerfield Beach, FL',
  195442 => 'Deerfield Beach, FL',
  195443 => 'Pembroke Pines, FL',
  1954441 => 'Pembroke Pines, FL',
  1954442 => 'Pembroke Pines, FL',
  1954443 => 'Pembroke Pines, FL',
  1954450 => 'Pembroke Pines, FL',
  1954454 => 'Hallandale Beach, FL',
  1954455 => 'Hallandale Beach, FL',
  1954456 => 'Hallandale Beach, FL',
  1954457 => 'Hallandale Beach, FL',
  1954458 => 'Hallandale Beach, FL',
  1954462 => 'Fort Lauderdale, FL',
  1954463 => 'Fort Lauderdale, FL',
  1954467 => 'Fort Lauderdale, FL',
  1954468 => 'Fort Lauderdale, FL',
  1954473 => 'Plantation, FL',
  1954480 => 'Deerfield Beach, FL',
  1954481 => 'Deerfield Beach, FL',
  1954489 => 'Fort Lauderdale, FL',
  1954491 => 'Fort Lauderdale, FL',
  1954492 => 'Fort Lauderdale, FL',
  1954493 => 'Fort Lauderdale, FL',
  1954509 => 'Coral Springs, FL',
  1954510 => 'Coral Springs, FL',
  1954522 => 'Fort Lauderdale, FL',
  1954523 => 'Fort Lauderdale, FL',
  1954524 => 'Fort Lauderdale, FL',
  1954525 => 'Fort Lauderdale, FL',
  1954527 => 'Fort Lauderdale, FL',
  1954531 => 'Deerfield Beach, FL',
  1954532 => 'Pompano Beach, FL',
  1954537 => 'Fort Lauderdale, FL',
  1954545 => 'Pompano Beach, FL',
  1954563 => 'Fort Lauderdale, FL',
  1954564 => 'Fort Lauderdale, FL',
  1954565 => 'Fort Lauderdale, FL',
  1954566 => 'Fort Lauderdale, FL',
  1954568 => 'Fort Lauderdale, FL',
  1954570 => 'Deerfield Beach, FL',
  1954571 => 'Deerfield Beach, FL',
  1954572 => 'Sunrise, FL',
  1954575 => 'Coral Springs, FL',
  1954578 => 'Sunrise, FL',
  1954580 => 'Pompano Beach, FL',
  1954596 => 'Deerfield Beach, FL',
  1954597 => 'Tamarac, FL',
  1954659 => 'Weston, FL',
  1954693 => 'Plantation, FL',
  1954698 => 'Deerfield Beach, FL',
  1954704 => 'Pembroke Pines, FL',
  1954712 => 'Fort Lauderdale, FL',
  1954718 => 'Tamarac, FL',
  1954720 => 'Tamarac, FL',
  1954721 => 'Tamarac, FL',
  1954722 => 'Tamarac, FL',
  1954724 => 'Tamarac, FL',
  1954725 => 'Deerfield Beach, FL',
  1954726 => 'Tamarac, FL',
  1954728 => 'Fort Lauderdale, FL',
  195474 => 'Sunrise, FL',
  1954752 => 'Coral Springs, FL',
  1954753 => 'Coral Springs, FL',
  1954755 => 'Coral Springs, FL',
  1954757 => 'Coral Springs, FL',
  1954759 => 'Fort Lauderdale, FL',
  195476 => 'Fort Lauderdale, FL',
  1954771 => 'Fort Lauderdale, FL',
  1954772 => 'Fort Lauderdale, FL',
  1954776 => 'Fort Lauderdale, FL',
  1954779 => 'Fort Lauderdale, FL',
  195478 => 'Pompano Beach, FL',
  1954796 => 'Coral Springs, FL',
  1954828 => 'Fort Lauderdale, FL',
  1954831 => 'Fort Lauderdale, FL',
  1954835 => 'Sunrise, FL',
  1954838 => 'Sunrise, FL',
  1954845 => 'Sunrise, FL',
  1954846 => 'Sunrise, FL',
  1954851 => 'Sunrise, FL',
  1954894 => 'Hollywood, FL',
  195492 => 'Hollywood, FL',
  1954933 => 'Pompano Beach, FL',
  1954938 => 'Fort Lauderdale, FL',
  1954941 => 'Pompano Beach, FL',
  1954942 => 'Pompano Beach, FL',
  1954943 => 'Pompano Beach, FL',
  1954946 => 'Pompano Beach, FL',
  1954958 => 'Fort Lauderdale, FL',
  195496 => 'Hollywood, FL',
  1954960 => 'Pompano Beach, FL',
  195498 => 'Hollywood, FL',
  1956 => 'Texas',
  1956213 => 'McAllen, TX',
  1956233 => 'Los Fresnos, TX',
  1956283 => 'Pharr, TX',
  1956287 => 'Edinburg, TX',
  1956289 => 'Edinburg, TX',
  1956316 => 'Edinburg, TX',
  1956318 => 'Edinburg, TX',
  1956350 => 'Brownsville, TX',
  1956361 => 'San Benito, TX',
  1956364 => 'Harlingen, TX',
  1956365 => 'Harlingen, TX',
  1956380 => 'Edinburg, TX',
  1956381 => 'Edinburg, TX',
  1956383 => 'Edinburg, TX',
  1956386 => 'Edinburg, TX',
  1956389 => 'Harlingen, TX',
  1956399 => 'San Benito, TX',
  1956412 => 'Harlingen, TX',
  1956421 => 'Harlingen, TX',
  1956423 => 'Harlingen, TX',
  1956424 => 'Mission, TX',
  1956425 => 'Harlingen, TX',
  1956428 => 'Harlingen, TX',
  1956440 => 'Harlingen, TX',
  1956447 => 'Weslaco, TX',
  1956461 => 'Donna, TX',
  1956464 => 'Donna, TX',
  1956465 => 'Brownsville, TX',
  1956487 => 'Rio Grande City, TX',
  1956488 => 'Rio Grande City, TX',
  1956504 => 'Brownsville, TX',
  1956514 => 'Mercedes, TX',
  1956519 => 'Mission, TX',
  1956523 => 'Laredo, TX',
  1956541 => 'Brownsville, TX',
  1956542 => 'Brownsville, TX',
  1956544 => 'Brownsville, TX',
  1956546 => 'Brownsville, TX',
  1956548 => 'Brownsville, TX',
  1956550 => 'Brownsville, TX',
  1956554 => 'Brownsville, TX',
  1956565 => 'Mercedes, TX',
  1956568 => 'Laredo, TX',
  1956574 => 'Brownsville, TX',
  1956580 => 'Mission, TX',
  1956581 => 'Mission, TX',
  1956583 => 'Mission, TX',
  1956584 => 'Mission, TX',
  1956585 => 'Mission, TX',
  1956618 => 'McAllen, TX',
  1956621 => 'Brownsville, TX',
  1956627 => 'McAllen, TX',
  1956630 => 'McAllen, TX',
  1956631 => 'McAllen, TX',
  1956661 => 'McAllen, TX',
  1956664 => 'McAllen, TX',
  1956668 => 'McAllen, TX',
  1956682 => 'McAllen, TX',
  1956683 => 'McAllen, TX',
  1956686 => 'McAllen, TX',
  1956687 => 'McAllen, TX',
  1956688 => 'McAllen, TX',
  1956689 => 'Raymondville, TX',
  1956702 => 'Pharr, TX',
  1956712 => 'Laredo, TX',
  1956717 => 'Laredo, TX',
  1956718 => 'Laredo, TX',
  195672 => 'Laredo, TX',
  1956748 => 'Rio Hondo, TX',
  1956753 => 'Laredo, TX',
  1956765 => 'Zapata, TX',
  1956781 => 'Pharr, TX',
  1956782 => 'Pharr, TX',
  1956783 => 'Pharr, TX',
  1956787 => 'Pharr, TX',
  1956791 => 'Laredo, TX',
  1956795 => 'Laredo, TX',
  1956796 => 'Laredo, TX',
  1956797 => 'La Feria, TX',
  1956825 => 'Mercedes, TX',
  1956831 => 'Brownsville, TX',
  1956838 => 'Brownsville, TX',
  1956843 => 'Hidalgo, TX',
  1956849 => 'Roma, TX',
  1956928 => 'McAllen, TX',
  1956943 => 'Port Isabel, TX',
  1956968 => 'Weslaco, TX',
  1956969 => 'Weslaco, TX',
  1956971 => 'McAllen, TX',
  1956972 => 'McAllen, TX',
  1956973 => 'Weslaco, TX',
  1956982 => 'Brownsville, TX',
  1956992 => 'McAllen, TX',
  1956994 => 'McAllen, TX',
  1959 => 'Connecticut',
  1970 => 'Colorado',
  1970203 => 'Loveland, CO',
  1970204 => 'Fort Collins, CO',
  1970206 => 'Fort Collins, CO',
  1970207 => 'Fort Collins, CO',
  197022 => 'Fort Collins, CO',
  1970232 => 'Fort Collins, CO',
  197024 => 'Grand Junction, CO',
  1970240 => 'Montrose, CO',
  1970247 => 'Durango, CO',
  1970249 => 'Montrose, CO',
  1970250 => 'Grand Junction, CO',
  1970252 => 'Montrose, CO',
  1970254 => 'Grand Junction, CO',
  1970255 => 'Grand Junction, CO',
  1970256 => 'Grand Junction, CO',
  1970257 => 'Grand Junction, CO',
  1970259 => 'Durango, CO',
  1970261 => 'Grand Junction, CO',
  1970263 => 'Grand Junction, CO',
  1970264 => 'Pagosa Springs, CO',
  1970266 => 'Fort Collins, CO',
  1970276 => 'Hayden, CO',
  1970278 => 'Loveland, CO',
  1970282 => 'Fort Collins, CO',
  1970284 => 'La Salle, CO',
  1970285 => 'Parachute, CO',
  1970298 => 'Grand Junction, CO',
  1970304 => 'Greeley, CO',
  1970323 => 'Olathe, CO',
  1970325 => 'Ouray, CO',
  1970327 => 'Norwood, CO',
  1970328 => 'Eagle, CO',
  1970330 => 'Greeley, CO',
  1970332 => 'Wray, CO',
  1970336 => 'Greeley, CO',
  1970339 => 'Greeley, CO',
  1970345 => 'Akron, CO',
  1970346 => 'Greeley, CO',
  1970347 => 'Greeley, CO',
  1970349 => 'Crested Butte, CO',
  1970350 => 'Greeley, CO',
  1970351 => 'Greeley, CO',
  1970352 => 'Greeley, CO',
  1970353 => 'Greeley, CO',
  1970356 => 'Greeley, CO',
  1970375 => 'Durango, CO',
  1970377 => 'Fort Collins, CO',
  1970378 => 'Greeley, CO',
  1970382 => 'Durango, CO',
  1970384 => 'Glenwood Springs, CO',
  1970385 => 'Durango, CO',
  1970387 => 'Silverton, CO',
  1970392 => 'Greeley, CO',
  1970396 => 'Greeley, CO',
  1970403 => 'Durango, CO',
  1970407 => 'Fort Collins, CO',
  1970416 => 'Fort Collins, CO',
  1970424 => 'Grand Junction, CO',
  1970429 => 'Aspen, CO',
  1970449 => 'Fort Collins, CO',
  1970453 => 'Breckenridge, CO',
  1970454 => 'Eaton, CO',
  1970461 => 'Loveland, CO',
  1970464 => 'Palisade, CO',
  1970472 => 'Fort Collins, CO',
  1970474 => 'Julesburg, CO',
  1970476 => 'Vail, CO',
  1970479 => 'Vail, CO',
  1970482 => 'Fort Collins, CO',
  1970483 => 'Wiggins, CO',
  1970484 => 'Fort Collins, CO',
  197049 => 'Fort Collins, CO',
  1970506 => 'Greeley, CO',
  1970521 => 'Sterling, CO',
  1970522 => 'Sterling, CO',
  1970523 => 'Grand Junction, CO',
  1970524 => 'Gypsum, CO',
  1970527 => 'Paonia, CO',
  1970532 => 'Berthoud, CO',
  1970533 => 'Mancos, CO',
  1970542 => 'Fort Morgan, CO',
  1970544 => 'Aspen, CO',
  1970547 => 'Breckenridge, CO',
  1970563 => 'Ignacio, CO',
  1970564 => 'Cortez, CO',
  1970565 => 'Cortez, CO',
  1970568 => 'Wellington, CO',
  1970569 => 'Edwards, CO',
  1970577 => 'Estes Park, CO',
  1970586 => 'Estes Park, CO',
  1970587 => 'Johnstown, CO',
  1970593 => 'Loveland, CO',
  1970613 => 'Loveland, CO',
  1970619 => 'Loveland, CO',
  1970622 => 'Loveland, CO',
  1970625 => 'Rifle, CO',
  1970626 => 'Ridgway, CO',
  1970627 => 'Grand Lake, CO',
  1970631 => 'Fort Collins, CO',
  1970635 => 'Loveland, CO',
  1970641 => 'Gunnison, CO',
  1970663 => 'Loveland, CO',
  1970667 => 'Loveland, CO',
  1970668 => 'Frisco, CO',
  1970669 => 'Loveland, CO',
  1970672 => 'Fort Collins, CO',
  1970673 => 'Greeley, CO',
  1970674 => 'Windsor, CO',
  1970675 => 'Rangely, CO',
  1970677 => 'Dove Creek, CO',
  1970682 => 'Fort Collins, CO',
  1970686 => 'Windsor, CO',
  1970689 => 'Fort Collins, CO',
  1970704 => 'Carbondale, CO',
  1970723 => 'Walden, CO',
  1970724 => 'Kremmling, CO',
  1970728 => 'Telluride, CO',
  1970731 => 'Pagosa Springs, CO',
  1970739 => 'Cortez, CO',
  1970748 => 'Avon, CO',
  1970749 => 'Durango, CO',
  1970759 => 'Durango, CO',
  1970764 => 'Durango, CO',
  1970769 => 'Durango, CO',
  1970774 => 'Haxtun, CO',
  1970776 => 'Loveland, CO',
  1970785 => 'Platteville, CO',
  1970799 => 'Durango, CO',
  1970824 => 'Craig, CO',
  1970827 => 'Minturn, CO',
  1970834 => 'Ault, CO',
  1970842 => 'Brush, CO',
  1970845 => 'Avon, CO',
  1970848 => 'Yuma, CO',
  1970854 => 'Holyoke, CO',
  1970856 => 'Cedaredge, CO',
  1970858 => 'Fruita, CO',
  1970864 => 'Nucla, CO',
  1970867 => 'Fort Morgan, CO',
  1970870 => 'Steamboat Spgs, CO',
  1970871 => 'Steamboat Spgs, CO',
  1970872 => 'Hotchkiss, CO',
  1970874 => 'Delta, CO',
  1970876 => 'Silt, CO',
  1970878 => 'Meeker, CO',
  1970879 => 'Steamboat Spgs, CO',
  1970882 => 'Dolores, CO',
  1970884 => 'Bayfield, CO',
  1970887 => 'Granby, CO',
  1970903 => 'Durango, CO',
  1970920 => 'Aspen, CO',
  1970923 => 'Snowmass Village, CO',
  1970925 => 'Aspen, CO',
  1970926 => 'Edwards, CO',
  1970927 => 'Basalt, CO',
  1970928 => 'Glenwood Springs, CO',
  1970944 => 'Lake City, CO',
  1970945 => 'Glenwood Springs, CO',
  1970946 => 'Durango, CO',
  1970947 => 'Glenwood Springs, CO',
  1970949 => 'Avon, CO',
  1970962 => 'Loveland, CO',
  1970963 => 'Carbondale, CO',
  1970984 => 'New Castle, CO',
  1971 => 'Oregon',
  1971255 => 'Portland, OR',
  1971279 => 'Portland, OR',
  1972 => 'Texas',
  1972202 => 'Plano, TX',
  1972205 => 'Garland, TX',
  1972206 => 'Grand Prairie, TX',
  1972208 => 'Plano, TX',
  1972216 => 'Mesquite, TX',
  1972218 => 'Lancaster, TX',
  1972219 => 'Lewisville, TX',
  1972221 => 'Lewisville, TX',
  1972222 => 'Mesquite, TX',
  1972223 => 'DeSoto, TX',
  1972225 => 'Hutchins, TX',
  1972227 => 'Lancaster, TX',
  1972230 => 'DeSoto, TX',
  1972231 => 'Richardson, TX',
  1972233 => 'Dallas, TX',
  1972234 => 'Richardson, TX',
  1972235 => 'Richardson, TX',
  1972237 => 'Grand Prairie, TX',
  1972238 => 'Richardson, TX',
  1972239 => 'Dallas, TX',
  1972240 => 'Garland, TX',
  1972241 => 'Dallas, TX',
  1972242 => 'Carrollton, TX',
  1972243 => 'Dallas, TX',
  1972245 => 'Carrollton, TX',
  1972247 => 'Dallas, TX',
  1972248 => 'Dallas, TX',
  197225 => 'Irving, TX',
  1972262 => 'Grand Prairie, TX',
  1972263 => 'Grand Prairie, TX',
  1972264 => 'Grand Prairie, TX',
  1972266 => 'Grand Prairie, TX',
  1972270 => 'Mesquite, TX',
  1972271 => 'Garland, TX',
  1972272 => 'Garland, TX',
  1972274 => 'DeSoto, TX',
  1972276 => 'Garland, TX',
  1972278 => 'Garland, TX',
  1972279 => 'Mesquite, TX',
  1972285 => 'Mesquite, TX',
  1972287 => 'Seagoville, TX',
  1972288 => 'Mesquite, TX',
  1972289 => 'Mesquite, TX',
  1972291 => 'Cedar Hill, TX',
  1972293 => 'Cedar Hill, TX',
  1972296 => 'Duncanville, TX',
  1972298 => 'Duncanville, TX',
  1972299 => 'Cedar Hill, TX',
  1972303 => 'Garland, TX',
  1972304 => 'Coppell, TX',
  1972312 => 'Plano, TX',
  1972313 => 'Irving, TX',
  1972315 => 'Lewisville, TX',
  1972316 => 'Lewisville, TX',
  1972317 => 'Lewisville, TX',
  1972323 => 'Carrollton, TX',
  1972329 => 'Mesquite, TX',
  1972335 => 'Frisco, TX',
  1972346 => 'Prosper, TX',
  1972347 => 'Prosper, TX',
  1972352 => 'Grand Prairie, TX',
  1972353 => 'Lewisville, TX',
  1972355 => 'Flower Mound, TX',
  1972359 => 'Allen, TX',
  1972369 => 'McKinney, TX',
  1972370 => 'The Colony, TX',
  1972377 => 'Frisco, TX',
  1972378 => 'Plano, TX',
  1972382 => 'Celina, TX',
  1972385 => 'Dallas, TX',
  1972386 => 'Dallas, TX',
  1972387 => 'Dallas, TX',
  1972390 => 'Allen, TX',
  1972392 => 'Dallas, TX',
  1972393 => 'Coppell, TX',
  1972394 => 'Carrollton, TX',
  1972395 => 'Carrollton, TX',
  1972396 => 'Allen, TX',
  1972398 => 'Plano, TX',
  1972401 => 'Irving, TX',
  1972402 => 'Irving, TX',
  1972403 => 'Plano, TX',
  1972404 => 'Dallas, TX',
  1972406 => 'Dallas, TX',
  1972407 => 'Dallas, TX',
  1972409 => 'Irving, TX',
  1972412 => 'Rowlett, TX',
  1972414 => 'Garland, TX',
  1972416 => 'Carrollton, TX',
  1972417 => 'Carrollton, TX',
  1972418 => 'Carrollton, TX',
  1972419 => 'Dallas, TX',
  1972420 => 'Lewisville, TX',
  1972422 => 'Plano, TX',
  1972423 => 'Plano, TX',
  1972424 => 'Plano, TX',
  1972429 => 'Wylie, TX',
  1972434 => 'Lewisville, TX',
  1972436 => 'Lewisville, TX',
  1972437 => 'Richardson, TX',
  1972438 => 'Irving, TX',
  1972442 => 'Wylie, TX',
  1972445 => 'Irving, TX',
  1972446 => 'Carrollton, TX',
  1972450 => 'Dallas, TX',
  1972456 => 'Dallas, TX',
  1972458 => 'Dallas, TX',
  1972459 => 'Lewisville, TX',
  1972462 => 'Coppell, TX',
  1972463 => 'Rowlett, TX',
  1972466 => 'Carrollton, TX',
  1972470 => 'Richardson, TX',
  1972473 => 'Plano, TX',
  1972475 => 'Rowlett, TX',
  1972478 => 'Carrollton, TX',
  1972479 => 'Richardson, TX',
  1972481 => 'Dallas, TX',
  1972484 => 'Dallas, TX',
  1972485 => 'Garland, TX',
  1972487 => 'Garland, TX',
  1972488 => 'Dallas, TX',
  1972490 => 'Dallas, TX',
  1972491 => 'Plano, TX',
  1972492 => 'Carrollton, TX',
  1972494 => 'Garland, TX',
  1972495 => 'Garland, TX',
  1972496 => 'Garland, TX',
  1972501 => 'Irving, TX',
  1972503 => 'Dallas, TX',
  1972509 => 'Plano, TX',
  1972516 => 'Plano, TX',
  1972517 => 'Plano, TX',
  1972519 => 'Plano, TX',
  1972522 => 'Grand Prairie, TX',
  1972524 => 'Terrell, TX',
  1972527 => 'Plano, TX',
  1972529 => 'McKinney, TX',
  1972530 => 'Garland, TX',
  1972539 => 'Flower Mound, TX',
  1972540 => 'McKinney, TX',
  1972542 => 'McKinney, TX',
  1972544 => 'Ferris, TX',
  1972547 => 'McKinney, TX',
  1972548 => 'McKinney, TX',
  1972550 => 'Irving, TX',
  1972551 => 'Terrell, TX',
  1972552 => 'Forney, TX',
  1972554 => 'Irving, TX',
  1972562 => 'McKinney, TX',
  1972563 => 'Terrell, TX',
  1972564 => 'Forney, TX',
  1972566 => 'Dallas, TX',
  1972569 => 'McKinney, TX',
  1972570 => 'Irving, TX',
  1972574 => 'Dallas, TX',
  1972576 => 'Red Oak, TX',
  1972578 => 'Plano, TX',
  1972579 => 'Irving, TX',
  1972580 => 'Irving, TX',
  1972594 => 'Irving, TX',
  1972596 => 'Plano, TX',
  1972599 => 'Plano, TX',
  1972600 => 'Irving, TX',
  1972602 => 'Grand Prairie, TX',
  1972606 => 'Grand Prairie, TX',
  1972608 => 'Plano, TX',
  1972612 => 'Plano, TX',
  1972613 => 'Mesquite, TX',
  1972617 => 'Red Oak, TX',
  1972618 => 'Plano, TX',
  1972620 => 'Dallas, TX',
  1972623 => 'Grand Prairie, TX',
  1972625 => 'The Colony, TX',
  1972633 => 'Plano, TX',
  1972635 => 'Royse City, TX',
  1972636 => 'Royse City, TX',
  1972641 => 'Grand Prairie, TX',
  1972642 => 'Grand Prairie, TX',
  1972644 => 'Richardson, TX',
  1972647 => 'Grand Prairie, TX',
  1972660 => 'Grand Prairie, TX',
  1972661 => 'Dallas, TX',
  1972663 => 'Dallas, TX',
  1972664 => 'Richardson, TX',
  1972665 => 'Plano, TX',
  1972668 => 'Frisco, TX',
  1972669 => 'Richardson, TX',
  1972671 => 'Richardson, TX',
  1972675 => 'Garland, TX',
  1972678 => 'Allen, TX',
  1972680 => 'Richardson, TX',
  1972681 => 'Mesquite, TX',
  1972682 => 'Mesquite, TX',
  1972686 => 'Mesquite, TX',
  1972690 => 'Richardson, TX',
  1972691 => 'Flower Mound, TX',
  1972698 => 'Mesquite, TX',
  1972699 => 'Richardson, TX',
  1972701 => 'Dallas, TX',
  1972702 => 'Dallas, TX',
  1972712 => 'Frisco, TX',
  1972717 => 'Irving, TX',
  1972719 => 'Irving, TX',
  1972721 => 'Irving, TX',
  1972722 => 'Rockwall, TX',
  1972723 => 'Midlothian, TX',
  1972724 => 'Flower Mound, TX',
  1972726 => 'Dallas, TX',
  1972727 => 'Allen, TX',
  1972732 => 'Dallas, TX',
  1972733 => 'Dallas, TX',
  1972736 => 'Princeton, TX',
  1972744 => 'Richardson, TX',
  1972745 => 'Coppell, TX',
  1972747 => 'Allen, TX',
  1972758 => 'Plano, TX',
  1972769 => 'Plano, TX',
  1972770 => 'Dallas, TX',
  1972771 => 'Rockwall, TX',
  1972772 => 'Rockwall, TX',
  1972774 => 'Dallas, TX',
  1972775 => 'Midlothian, TX',
  1972780 => 'Duncanville, TX',
  1972781 => 'Plano, TX',
  1972782 => 'Farmersville, TX',
  1972783 => 'Richardson, TX',
  1972784 => 'Farmersville, TX',
  1972788 => 'Dallas, TX',
  1972789 => 'Dallas, TX',
  1972790 => 'Irving, TX',
  1972801 => 'Plano, TX',
  1972837 => 'Melissa, TX',
  1972840 => 'Garland, TX',
  1972851 => 'Dallas, TX',
  1972855 => 'Dallas, TX',
  1972864 => 'Garland, TX',
  1972867 => 'Plano, TX',
  1972870 => 'Irving, TX',
  1972871 => 'Irving, TX',
  1972874 => 'Flower Mound, TX',
  1972875 => 'Ennis, TX',
  1972878 => 'Ennis, TX',
  1972881 => 'Plano, TX',
  1972889 => 'Richardson, TX',
  1972906 => 'Lewisville, TX',
  1972907 => 'Richardson, TX',
  1972910 => 'Irving, TX',
  1972918 => 'Richardson, TX',
  1972923 => 'Waxahachie, TX',
  1972924 => 'Anna, TX',
  1972926 => 'Garland, TX',
  1972929 => 'Irving, TX',
  1972932 => 'Kaufman, TX',
  1972934 => 'Dallas, TX',
  1972935 => 'Waxahachie, TX',
  1972937 => 'Waxahachie, TX',
  1972938 => 'Waxahachie, TX',
  1972939 => 'Carrollton, TX',
  1972941 => 'Plano, TX',
  1972943 => 'Plano, TX',
  1972960 => 'Dallas, TX',
  1972961 => 'Rockwall, TX',
  1972962 => 'Kaufman, TX',
  1972964 => 'Plano, TX',
  1972980 => 'Dallas, TX',
  1972981 => 'Plano, TX',
  1972984 => 'McKinney, TX',
  1972985 => 'Plano, TX',
  1972986 => 'Irving, TX',
  1972988 => 'Grand Prairie, TX',
  1972991 => 'Dallas, TX',
  1973 => 'New Jersey',
  1973227 => 'Fairfield, NJ',
  1973230 => 'Newark, NJ',
  1973233 => 'Montclair, NJ',
  1973235 => 'Nutley, NJ',
  1973239 => 'Verona, NJ',
  1973242 => 'Newark, NJ',
  1973243 => 'West Orange, NJ',
  1973244 => 'Fairfield, NJ',
  1973247 => 'Paterson, NJ',
  1973253 => 'Clifton, NJ',
  1973259 => 'Bloomfield, NJ',
  1973266 => 'East Orange, NJ',
  1973267 => 'Morristown, NJ',
  1973268 => 'Newark, NJ',
  1973273 => 'Newark, NJ',
  1973274 => 'Newark, NJ',
  1973276 => 'Fairfield, NJ',
  1973278 => 'Paterson, NJ',
  1973279 => 'Paterson, NJ',
  1973284 => 'Nutley, NJ',
  1973285 => 'Morristown, NJ',
  1973292 => 'Morristown, NJ',
  1973293 => 'Montague Township, NJ',
  1973300 => 'Newton, NJ',
  1973301 => 'Florham Park, NJ',
  1973305 => 'Wayne, NJ',
  1973321 => 'Paterson, NJ',
  1973322 => 'Livingston, NJ',
  1973324 => 'West Orange, NJ',
  1973325 => 'West Orange, NJ',
  1973326 => 'Morristown, NJ',
  1973333 => 'Paterson, NJ',
  1973338 => 'Bloomfield, NJ',
  1973340 => 'Clifton, NJ',
  1973341 => 'Paterson, NJ',
  1973344 => 'Newark, NJ',
  1973345 => 'Paterson, NJ',
  1973350 => 'Newark, NJ',
  1973353 => 'Newark, NJ',
  1973357 => 'Paterson, NJ',
  1973365 => 'Passaic, NJ',
  1973371 => 'Irvington, NJ',
  1973372 => 'Irvington, NJ',
  1973373 => 'Irvington, NJ',
  1973374 => 'Irvington, NJ',
  1973375 => 'Irvington, NJ',
  1973383 => 'Newton, NJ',
  1973395 => 'East Orange, NJ',
  1973399 => 'Irvington, NJ',
  1973414 => 'East Orange, NJ',
  1973416 => 'Irvington, NJ',
  1973422 => 'Livingston, NJ',
  1973423 => 'Hawthorne, NJ',
  1973425 => 'Morristown, NJ',
  1973427 => 'Hawthorne, NJ',
  1973429 => 'Bloomfield, NJ',
  1973439 => 'Fairfield, NJ',
  1973450 => 'Belleville, NJ',
  1973455 => 'Morristown, NJ',
  1973465 => 'Newark, NJ',
  1973466 => 'Newark, NJ',
  1973481 => 'Newark, NJ',
  1973482 => 'Newark, NJ',
  1973483 => 'Newark, NJ',
  1973484 => 'Newark, NJ',
  1973485 => 'Newark, NJ',
  1973491 => 'Newark, NJ',
  1973509 => 'Montclair, NJ',
  1973522 => 'Newark, NJ',
  1973523 => 'Paterson, NJ',
  1973533 => 'Livingston, NJ',
  1973535 => 'Livingston, NJ',
  1973538 => 'Morristown, NJ',
  1973539 => 'Morristown, NJ',
  1973540 => 'Morristown, NJ',
  1973542 => 'Nutley, NJ',
  1973543 => 'Mendham, NJ',
  1973546 => 'Clifton, NJ',
  1973569 => 'Paterson, NJ',
  1973575 => 'Fairfield, NJ',
  1973579 => 'Newton, NJ',
  1973589 => 'Newark, NJ',
  1973593 => 'Madison, NJ',
  1973594 => 'Clifton, NJ',
  1973596 => 'Newark, NJ',
  1973597 => 'Livingston, NJ',
  1973605 => 'Morristown, NJ',
  1973621 => 'Newark, NJ',
  1973622 => 'Newark, NJ',
  1973623 => 'Newark, NJ',
  1973624 => 'Newark, NJ',
  1973625 => 'Denville, NJ',
  1973628 => 'Wayne, NJ',
  1973633 => 'Wayne, NJ',
  1973635 => 'Chatham, NJ',
  1973642 => 'Newark, NJ',
  1973643 => 'Newark, NJ',
  1973644 => 'Morristown, NJ',
  1973645 => 'Newark, NJ',
  1973648 => 'Newark, NJ',
  1973653 => 'Paterson, NJ',
  1973655 => 'Montclair, NJ',
  1973656 => 'Morristown, NJ',
  1973660 => 'Florham Park, NJ',
  1973661 => 'Nutley, NJ',
  1973663 => 'Lake Hopatcong, NJ',
  1973667 => 'Nutley, NJ',
  1973669 => 'West Orange, NJ',
  197367 => 'East Orange, NJ',
  1973680 => 'Bloomfield, NJ',
  1973684 => 'Paterson, NJ',
  1973686 => 'Wayne, NJ',
  1973689 => 'Paterson, NJ',
  1973694 => 'Wayne, NJ',
  1973696 => 'Wayne, NJ',
  1973701 => 'Chatham, NJ',
  1973702 => 'Sussex, NJ',
  1973706 => 'Wayne, NJ',
  1973726 => 'Sparta Township, NJ',
  1973728 => 'West Milford, NJ',
  1973729 => 'Sparta Township, NJ',
  1973731 => 'West Orange, NJ',
  1973732 => 'Newark, NJ',
  1973733 => 'Newark, NJ',
  1973736 => 'West Orange, NJ',
  1973740 => 'Livingston, NJ',
  1973742 => 'Paterson, NJ',
  1973743 => 'Bloomfield, NJ',
  1973744 => 'Montclair, NJ',
  1973746 => 'Montclair, NJ',
  1973748 => 'Bloomfield, NJ',
  1973751 => 'Belleville, NJ',
  1973754 => 'Paterson, NJ',
  1973759 => 'Belleville, NJ',
  1973761 => 'Maplewood, NJ',
  1973764 => 'Vernon Township, NJ',
  1973772 => 'Clifton, NJ',
  1973782 => 'Paterson, NJ',
  1973783 => 'Montclair, NJ',
  1973786 => 'Andover, NJ',
  1973808 => 'Fairfield, NJ',
  1973817 => 'Newark, NJ',
  1973824 => 'Newark, NJ',
  1973829 => 'Morristown, NJ',
  1973844 => 'Belleville, NJ',
  1973853 => 'Hewitt, NJ',
  1973857 => 'Verona, NJ',
  1973872 => 'Wayne, NJ',
  1973875 => 'Sussex, NJ',
  1973877 => 'Newark, NJ',
  1973881 => 'Paterson, NJ',
  1973882 => 'Fairfield, NJ',
  1973889 => 'Morristown, NJ',
  1973895 => 'Randolph, NJ',
  1973898 => 'Morristown, NJ',
  1973923 => 'Newark, NJ',
  1973925 => 'Paterson, NJ',
  1973926 => 'Newark, NJ',
  1973940 => 'Newton, NJ',
  1973948 => 'Branchville, NJ',
  1973962 => 'Ringwood, NJ',
  1973971 => 'Morristown, NJ',
  1973972 => 'Newark, NJ',
  1973977 => 'Paterson, NJ',
  1973984 => 'Morristown, NJ',
  1973991 => 'Newark, NJ',
  1973992 => 'Livingston, NJ',
  1973993 => 'Morristown, NJ',
  1973994 => 'Livingston, NJ',
  1978 => 'Massachusetts',
  1978208 => 'Lawrence, MA',
  1978232 => 'Beverly, MA',
  1978244 => 'Chelmsford, MA',
  1978249 => 'Athol, MA',
  1978250 => 'Chelmsford, MA',
  1978251 => 'North Chelmsford, MA',
  1978256 => 'Chelmsford, MA',
  1978258 => 'Lawrence, MA',
  1978263 => 'Acton, MA',
  1978264 => 'Acton, MA',
  1978266 => 'Acton, MA',
  1978275 => 'Lowell, MA',
  1978276 => 'North Reading, MA',
  1978281 => 'Gloucester, MA',
  1978282 => 'Gloucester, MA',
  1978283 => 'Gloucester, MA',
  1978287 => 'Concord, MA',
  1978297 => 'Winchendon, MA',
  1978318 => 'Concord, MA',
  1978327 => 'Lawrence, MA',
  1978342 => 'Fitchburg, MA',
  1978343 => 'Fitchburg, MA',
  1978345 => 'Fitchburg, MA',
  1978346 => 'Merrimac, MA',
  1978352 => 'Georgetown, MA',
  1978354 => 'Salem, MA',
  1978355 => 'Barre, MA',
  1978356 => 'Ipswich, MA',
  1978363 => 'West Newbury, MA',
  1978365 => 'Clinton, MA',
  1978368 => 'Clinton, MA',
  1978369 => 'Concord, MA',
  1978371 => 'Concord, MA',
  1978372 => 'Haverhill, MA',
  1978373 => 'Haverhill, MA',
  1978374 => 'Haverhill, MA',
  1978386 => 'Ashby, MA',
  1978388 => 'Amesbury, MA',
  1978392 => 'Westford, MA',
  1978422 => 'Sterling, MA',
  1978425 => 'Shirley, MA',
  1978433 => 'Pepperell, MA',
  1978440 => 'Sudbury, MA',
  1978441 => 'Lowell, MA',
  1978443 => 'Sudbury, MA',
  1978446 => 'Lowell, MA',
  1978448 => 'Groton, MA',
  1978452 => 'Lowell, MA',
  1978453 => 'Lowell, MA',
  1978454 => 'Lowell, MA',
  1978456 => 'Harvard, MA',
  1978458 => 'Lowell, MA',
  1978459 => 'Lowell, MA',
  1978461 => 'Maynard, MA',
  1978462 => 'Newburyport, MA',
  1978463 => 'Newburyport, MA',
  1978464 => 'Princeton, MA',
  1978465 => 'Newburyport, MA',
  1978466 => 'Leominster, MA',
  1978468 => 'South Hamilton, MA',
  1978470 => 'Andover, MA',
  1978474 => 'Andover, MA',
  1978475 => 'Andover, MA',
  1978486 => 'Littleton, MA',
  1978499 => 'Newburyport, MA',
  1978521 => 'Haverhill, MA',
  1978524 => 'Beverly, MA',
  1978525 => 'Gloucester, MA',
  1978526 => 'Manchester, MA',
  1978531 => 'Peabody, MA',
  1978532 => 'Peabody, MA',
  1978534 => 'Leominster, MA',
  1978535 => 'Peabody, MA',
  1978536 => 'Peabody, MA',
  1978537 => 'Leominster, MA',
  1978538 => 'Peabody, MA',
  1978544 => 'Orange, MA',
  1978546 => 'Rockport, MA',
  1978556 => 'Haverhill, MA',
  1978557 => 'Lawrence, MA',
  1978562 => 'Hudson, MA',
  1978567 => 'Hudson, MA',
  1978568 => 'Hudson, MA',
  1978582 => 'Lunenburg, MA',
  1978594 => 'Salem, MA',
  1978597 => 'Townsend, MA',
  1978630 => 'Gardner, MA',
  1978632 => 'Gardner, MA',
  1978635 => 'Acton, MA',
  1978640 => 'Tewksbury, MA',
  1978646 => 'Danvers, MA',
  1978649 => 'Tyngsborough, MA',
  1978657 => 'Wilmington, MA',
  1978658 => 'Wilmington, MA',
  1978663 => 'Billerica, MA',
  1978664 => 'North Reading, MA',
  1978667 => 'Billerica, MA',
  1978670 => 'Billerica, MA',
  1978671 => 'Billerica, MA',
  1978692 => 'Westford, MA',
  1978735 => 'Lowell, MA',
  1978739 => 'Danvers, MA',
  1978740 => 'Salem, MA',
  1978741 => 'Salem, MA',
  1978744 => 'Salem, MA',
  1978745 => 'Salem, MA',
  1978749 => 'Andover, MA',
  1978750 => 'Danvers, MA',
  1978762 => 'Danvers, MA',
  1978768 => 'Essex, MA',
  1978772 => 'Ayer, MA',
  1978774 => 'Danvers, MA',
  1978777 => 'Danvers, MA',
  1978779 => 'Bolton, MA',
  1978827 => 'Ashburnham, MA',
  1978834 => 'Amesbury, MA',
  1978838 => 'Berlin, MA',
  1978840 => 'Leominster, MA',
  1978851 => 'Tewksbury, MA',
  1978858 => 'Tewksbury, MA',
  1978874 => 'Westminster, MA',
  1978887 => 'Topsfield, MA',
  1978897 => 'Maynard, MA',
  1978921 => 'Beverly, MA',
  1978922 => 'Beverly, MA',
  1978927 => 'Beverly, MA',
  1978928 => 'Hubbardston, MA',
  1978934 => 'Lowell, MA',
  1978937 => 'Lowell, MA',
  1978939 => 'Templeton, MA',
  1978948 => 'Rowley, MA',
  1978952 => 'Littleton, MA',
  1978957 => 'Dracut, MA',
  1978969 => 'Beverly, MA',
  1978970 => 'Lowell, MA',
  1978977 => 'Peabody, MA',
  1978988 => 'Wilmington, MA',
  1979 => 'Texas',
  1979209 => 'Bryan, TX',
  1979233 => 'Freeport, TX',
  1979234 => 'Eagle Lake, TX',
  1979239 => 'Freeport, TX',
  1979242 => 'La Grange, TX',
  1979244 => 'Bay City, TX',
  1979245 => 'Bay City, TX',
  1979251 => 'Brenham, TX',
  1979265 => 'Clute, TX',
  1979272 => 'Caldwell, TX',
  1979277 => 'Brenham, TX',
  1979279 => 'Hearne, TX',
  1979282 => 'Wharton, TX',
  1979285 => 'Lake Jackson, TX',
  1979297 => 'Lake Jackson, TX',
  1979299 => 'Lake Jackson, TX',
  1979323 => 'Bay City, TX',
  1979335 => 'East Bernard, TX',
  1979345 => 'West Columbia, TX',
  1979421 => 'Brenham, TX',
  1979480 => 'Lake Jackson, TX',
  1979532 => 'Wharton, TX',
  1979542 => 'Giddings, TX',
  1979543 => 'El Campo, TX',
  1979548 => 'Sweeny, TX',
  1979567 => 'Caldwell, TX',
  1979578 => 'El Campo, TX',
  1979596 => 'Somerville, TX',
  197969 => 'College Station, TX',
  1979703 => 'Bryan, TX',
  1979725 => 'Weimar, TX',
  1979731 => 'Bryan, TX',
  1979732 => 'Columbus, TX',
  1979733 => 'Columbus, TX',
  1979743 => 'Schulenburg, TX',
  1979764 => 'College Station, TX',
  1979773 => 'Lexington, TX',
  1979774 => 'Bryan, TX',
  1979775 => 'Bryan, TX',
  1979776 => 'Bryan, TX',
  1979778 => 'Bryan, TX',
  1979779 => 'Bryan, TX',
  1979793 => 'Needville, TX',
  1979798 => 'Brazoria, TX',
  1979822 => 'Bryan, TX',
  1979823 => 'Bryan, TX',
  1979826 => 'Hempstead, TX',
  1979828 => 'Franklin, TX',
  1979830 => 'Brenham, TX',
  1979836 => 'Brenham, TX',
  1979845 => 'College Station, TX',
  1979848 => 'Angleton, TX',
  1979849 => 'Angleton, TX',
  1979864 => 'Angleton, TX',
  1979865 => 'Bellville, TX',
  1979885 => 'Sealy, TX',
  1979968 => 'La Grange, TX',
  1980 => 'North Carolina',
  1980207 => 'Charlotte, NC',
  1980224 => 'Charlotte, NC',
  1980343 => 'Charlotte, NC',
  1980487 => 'Shelby, NC',
  1980819 => 'Charlotte, NC',
  1983 => 'Colorado',
  1984 => 'North Carolina',
  1985 => 'Louisiana',
  1985223 => 'Houma, LA',
  1985229 => 'Kentwood, LA',
  1985230 => 'Hammond, LA',
  1985246 => 'Covington, LA',
  1985249 => 'Covington, LA',
  1985252 => 'Pierre Part, LA',
  1985262 => 'Houma, LA',
  1985288 => 'Slidell, LA',
  1985325 => 'Cut Off, LA',
  1985327 => 'Covington, LA',
  1985331 => 'Luling, LA',
  1985340 => 'Hammond, LA',
  1985345 => 'Hammond, LA',
  1985359 => 'LaPlace, LA',
  1985369 => 'Napoleonville, LA',
  1985370 => 'Ponchatoula, LA',
  1985384 => 'Morgan City, LA',
  1985385 => 'Morgan City, LA',
  1985386 => 'Ponchatoula, LA',
  1985395 => 'Patterson, LA',
  1985396 => 'Golden Meadow, LA',
  1985419 => 'Hammond, LA',
  1985429 => 'Hammond, LA',
  1985446 => 'Thibodaux, LA',
  1985447 => 'Thibodaux, LA',
  1985448 => 'Thibodaux, LA',
  1985449 => 'Thibodaux, LA',
  1985475 => 'Golden Meadow, LA',
  1985493 => 'Thibodaux, LA',
  1985532 => 'Lockport, LA',
  1985536 => 'Reserve, LA',
  1985537 => 'Raceland, LA',
  1985542 => 'Hammond, LA',
  1985543 => 'Hammond, LA',
  1985580 => 'Houma, LA',
  1985624 => 'Mandeville, LA',
  1985626 => 'Mandeville, LA',
  1985632 => 'Cut Off, LA',
  1985639 => 'Slidell, LA',
  1985641 => 'Slidell, LA',
  1985643 => 'Slidell, LA',
  1985645 => 'Slidell, LA',
  1985646 => 'Slidell, LA',
  1985649 => 'Slidell, LA',
  1985651 => 'LaPlace, LA',
  1985652 => 'LaPlace, LA',
  1985662 => 'Hammond, LA',
  1985674 => 'Mandeville, LA',
  1985690 => 'Slidell, LA',
  1985693 => 'Larose, LA',
  1985727 => 'Mandeville, LA',
  1985732 => 'Bogalusa, LA',
  1985735 => 'Bogalusa, LA',
  1985747 => 'Amite City, LA',
  1985748 => 'Amite City, LA',
  1985764 => 'Destrehan, LA',
  1985778 => 'Mandeville, LA',
  1985781 => 'Slidell, LA',
  1985783 => 'Hahnville, LA',
  1985785 => 'Luling, LA',
  1985792 => 'Madisonville, LA',
  1985795 => 'Franklinton, LA',
  1985796 => 'Folsom, LA',
  1985809 => 'Covington, LA',
  1985839 => 'Franklinton, LA',
  1985845 => 'Madisonville, LA',
  1985847 => 'Slidell, LA',
  1985851 => 'Houma, LA',
  1985853 => 'Houma, LA',
  1985857 => 'Houma, LA',
  1985863 => 'Pearl River, LA',
  1985867 => 'Covington, LA',
  1985868 => 'Houma, LA',
  1985871 => 'Covington, LA',
  1985872 => 'Houma, LA',
  1985873 => 'Houma, LA',
  1985875 => 'Covington, LA',
  1985876 => 'Houma, LA',
  1985878 => 'Independence, LA',
  1985879 => 'Houma, LA',
  1985882 => 'Lacombe, LA',
  1985892 => 'Covington, LA',
  1985893 => 'Covington, LA',
  1985898 => 'Covington, LA',
  1986 => 'Idaho',
  1989 => 'Michigan',
  1989224 => 'St. Johns, MI',
  1989227 => 'St. Johns, MI',
  1989246 => 'Gladwin, MI',
  1989249 => 'Saginaw, MI',
  1989269 => 'Bad Axe, MI',
  1989275 => 'Roscommon, MI',
  1989288 => 'Durand, MI',
  1989317 => 'Mount Pleasant, MI',
  1989343 => 'West Branch, MI',
  1989345 => 'West Branch, MI',
  1989348 => 'Grayling, MI',
  1989352 => 'Lakeview, MI',
  1989354 => 'Alpena, MI',
  1989356 => 'Alpena, MI',
  1989358 => 'Alpena, MI',
  1989362 => 'Tawas City, MI',
  1989366 => 'Houghton Lake, MI',
  1989386 => 'Clare, MI',
  1989389 => 'Saint Helen, MI',
  1989401 => 'Saginaw, MI',
  1989422 => 'Houghton Lake, MI',
  1989426 => 'Gladwin, MI',
  1989427 => 'Edmore, MI',
  1989435 => 'Beaverton, MI',
  1989453 => 'Pigeon, MI',
  1989463 => 'Alma, MI',
  1989465 => 'Coleman, MI',
  1989466 => 'Alma, MI',
  1989471 => 'Ossineke, MI',
  1989479 => 'Harbor Beach, MI',
  1989486 => 'Midland, MI',
  1989496 => 'Midland, MI',
  1989497 => 'Saginaw, MI',
  1989539 => 'Harrison, MI',
  1989583 => 'Saginaw, MI',
  1989584 => 'Carson City, MI',
  1989588 => 'Farwell, MI',
  1989593 => 'Fowler, MI',
  1989624 => 'Birch Run, MI',
  1989631 => 'Midland, MI',
  1989633 => 'Midland, MI',
  1989635 => 'Marlette, MI',
  1989642 => 'Hemlock, MI',
  1989643 => 'Merrill, MI',
  1989644 => 'Weidman, MI',
  1989652 => 'Frankenmuth, MI',
  1989658 => 'Ubly, MI',
  1989662 => 'Auburn, MI',
  1989667 => 'Bay City, MI',
  1989671 => 'Bay City, MI',
  1989672 => 'Caro, MI',
  1989673 => 'Caro, MI',
  1989681 => 'St. Louis, MI',
  1989684 => 'Bay City, MI',
  1989685 => 'Rose City, MI',
  1989686 => 'Bay City, MI',
  1989687 => 'Sanford, MI',
  1989695 => 'Freeland, MI',
  1989697 => 'Linwood, MI',
  1989705 => 'Gaylord, MI',
  1989723 => 'Owosso, MI',
  1989724 => 'Harrisville, MI',
  1989725 => 'Owosso, MI',
  1989727 => 'Hubbard Lake, MI',
  1989728 => 'Hale, MI',
  1989729 => 'Owosso, MI',
  1989731 => 'Gaylord, MI',
  1989732 => 'Gaylord, MI',
  1989733 => 'Onaway, MI',
  1989734 => 'Rogers City, MI',
  1989736 => 'Lincoln, MI',
  1989738 => 'Port Austin, MI',
  1989739 => 'Oscoda, MI',
  1989742 => 'Hillman, MI',
  1989743 => 'Corunna, MI',
  1989752 => 'Saginaw, MI',
  1989753 => 'Saginaw, MI',
  1989754 => 'Saginaw, MI',
  1989755 => 'Saginaw, MI',
  1989759 => 'Saginaw, MI',
  1989772 => 'Mount Pleasant, MI',
  1989773 => 'Mount Pleasant, MI',
  1989775 => 'Mount Pleasant, MI',
  1989779 => 'Mount Pleasant, MI',
  1989781 => 'Saginaw, MI',
  1989785 => 'Atlanta, MI',
  1989786 => 'Lewiston, MI',
  198979 => 'Saginaw, MI',
  1989821 => 'Roscommon, MI',
  1989823 => 'Vassar, MI',
  1989826 => 'Mio, MI',
  1989828 => 'Shepherd, MI',
  1989831 => 'Stanton, MI',
  1989832 => 'Midland, MI',
  1989834 => 'Ovid, MI',
  1989835 => 'Midland, MI',
  1989837 => 'Midland, MI',
  1989839 => 'Midland, MI',
  1989842 => 'Breckenridge, MI',
  1989843 => 'Mayville, MI',
  1989845 => 'Chesaning, MI',
  1989846 => 'Standish, MI',
  1989848 => 'Fairview, MI',
  1989856 => 'Caseville, MI',
  1989862 => 'Elsie, MI',
  1989865 => 'St. Charles, MI',
  1989868 => 'Reese, MI',
  1989871 => 'Millington, MI',
  1989872 => 'Cass City, MI',
  1989873 => 'Prescott, MI',
  1989875 => 'Ithaca, MI',
  1989876 => 'Au Gres, MI',
  1989879 => 'Pinconning, MI',
  1989883 => 'Sebewaing, MI',
  1989891 => 'Bay City, MI',
  1989892 => 'Bay City, MI',
  1989893 => 'Bay City, MI',
  1989894 => 'Bay City, MI',
  1989895 => 'Bay City, MI',
);
