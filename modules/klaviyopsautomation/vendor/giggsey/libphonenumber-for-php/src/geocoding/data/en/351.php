<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  35121 => 'Lisbon',
  35122 => 'Porto',
  351231 => 'Mealhada',
  351232 => 'Viseu',
  351233 => 'Figueira da Foz',
  351234 => 'Aveiro',
  351235 => 'Arganil',
  351236 => 'Pombal',
  351238 => 'Seia',
  351239 => 'Coimbra',
  351241 => 'Abrantes',
  351242 => 'Ponte de Sôr',
  351243 => 'Santarém',
  351244 => 'Leiria',
  351245 => 'Portalegre',
  351249 => 'Torres Novas',
  351251 => 'Valença',
  351252 => '<PERSON><PERSON> <PERSON><PERSON>',
  351253 => '<PERSON>raga',
  351254 => '<PERSON>eso da Régua',
  351255 => 'Penafiel',
  351256 => 'S. <PERSON> da Madeira',
  351257 => '<PERSON>raga',
  351258 => 'Viana do Castelo',
  351259 => 'Vila Real',
  351261 => 'Torres Vedras',
  351262 => 'Caldas da Rainha',
  351263 => 'Vila Franca de Xira',
  351265 => 'Setúbal',
  351266 => 'Évora',
  351268 => 'Estremoz',
  351269 => 'Santiago do Cacém',
  351271 => 'Guarda',
  351272 => 'Castelo Branco',
  351273 => 'Bragança',
  351274 => 'Proença-a-Nova',
  351275 => 'Covilhã',
  351276 => 'Chaves',
  351277 => 'Idanha-a-Nova',
  351278 => 'Mirandela',
  351279 => 'Moncorvo',
  351281 => 'Tavira',
  351282 => 'Portimão',
  351283 => 'Odemira',
  351284 => 'Beja',
  351285 => 'Moura',
  351286 => 'Castro Verde',
  351289 => 'Faro',
  351291 => 'Funchal',
  351292 => 'Horta',
  351295 => 'Angra do Heroísmo',
  351296 => 'Ponta Delgada',
);
