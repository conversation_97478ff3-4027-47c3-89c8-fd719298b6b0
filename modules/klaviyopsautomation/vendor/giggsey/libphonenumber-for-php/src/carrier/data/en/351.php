<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  3511 => 'NOS',
  351609230 => 'NOS',
  35160929 => 'NOS',
  3516093 => 'NOS',
  35163920 => 'Lycamobile',
  351639230 => 'NOS',
  351639233 => 'Digi Communications',
  351639234 => 'G9 Telecom',
  35163924 => 'MEO',
  35163929 => 'NOS',
  3516393 => 'NOS',
  35165920 => 'Lycamobile',
  351659230 => 'NOS',
  351659233 => 'Digi Communications',
  351659234 => 'G9 Telecom',
  35165924 => 'MEO',
  35165929 => 'NOS',
  3516593 => 'NOS',
  351669230 => 'NOS',
  35166929 => 'NOS',
  3516693 => 'NOS',
  35191 => 'Vodafone',
  3519200 => 'Lycamobile',
  3519201 => 'Lycamobile',
  3519202 => 'Lycamobile',
  3519203 => 'Lycamobile',
  3519204 => 'Lycamobile',
  3519205 => 'Lycamobile',
  3519208 => 'Lycamobile',
  351921 => 'Vodafone',
  3519220 => 'Vodafone',
  3519221 => 'MEO',
  3519222 => 'MEO',
  351923 => 'NOS',
  3519231 => 'Vodafone',
  3519232 => 'MEO',
  3519233 => 'Digi Communications',
  3519234 => 'G9 Telecom',
  351924 => 'MEO',
  351925 => 'MEO',
  351926 => 'MEO',
  351927 => 'MEO',
  3519280 => 'NOWO',
  3519281 => 'NOWO',
  3519285 => 'ONITELECOM',
  3519290 => 'NOS',
  3519291 => 'NOS',
  3519292 => 'NOS',
  3519293 => 'NOS',
  3519294 => 'NOS',
  3519295 => 'Sumamovil Portugal',
  35193 => 'NOS',
  35196 => 'MEO',
);
