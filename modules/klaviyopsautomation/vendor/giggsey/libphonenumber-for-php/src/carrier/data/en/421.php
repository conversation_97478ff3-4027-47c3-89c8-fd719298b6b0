<?php
/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return array (
  421901 => 'T-Mobile (Slovak Telekom)',
  421902 => 'T-Mobile (Slovak Telekom)',
  421903 => 'T-Mobile (Slovak Telekom)',
  421904 => 'T-Mobile (Slovak Telekom)',
  421905 => 'Orange',
  421906 => 'Orange',
  421907 => 'Orange',
  421908 => 'Orange',
  4219091 => 'T-Mobile (Slovak Telekom)',
  4219092 => 'T-Mobile (Slovak Telekom)',
  4219093 => 'T-Mobile (Slovak Telekom)',
  4219094 => 'T-Mobile (Slovak Telekom)',
  4219095 => 'T-Mobile (Slovak Telekom)',
  4219096 => 'T-Mobile (Slovak Telekom)',
  4219097 => 'T-Mobile (Slovak Telekom)',
  4219098 => 'T-Mobile (Slovak Telekom)',
  4219099 => 'T-Mobile (Slovak Telekom)',
  421910 => 'T-Mobile (Slovak Telekom)',
  421911 => 'T-Mobile (Slovak Telekom)',
  421912 => 'T-Mobile (Slovak Telekom)',
  421914 => 'T-Mobile (Slovak Telekom)',
  421915 => 'Orange',
  421916 => 'Orange',
  421917 => 'Orange',
  421918 => 'Orange',
  421919 => 'Orange',
  421940 => 'Telefonica O2',
  4219430 => 'BSG Estonia',
  42194312 => 'Alternet, s.r.o.',
  42194333 => 'IPfon, s.r.o.',
  421944 => 'Telefonica O2',
  421945 => 'Orange',
  421947 => 'Telefonica O2',
  421948 => 'Telefonica O2',
  421949 => 'Telefonica O2',
  421950 => '4ka of SWAN',
  421951 => '4ka of SWAN',
  421952 => '4ka of SWAN',
  4219598 => 'Slovak Republic Railways (GSM-R)',
);
