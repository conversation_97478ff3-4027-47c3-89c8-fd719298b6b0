<?php
/**
 * This file has been @generated by a phing task from CLDR version 993632df2f5d6a2d33cbbf40d922474c2482eaca.
 * See [README.md](README.md#generating-data) for more information.
 *
 * @internal Please do not require this file directly.
 * It may change location/format between versions
 *
 * Do not modify this file directly!
 */

return array (
  'AD' => 'Andorra',
  'AE' => 'United Arab Emirates',
  'AF' => 'Afghanistan',
  'AG' => 'Antigua gi Barbuda',
  'AI' => 'Anguilla',
  'AL' => 'Albania',
  'AM' => 'Armenia',
  'AO' => 'Angola',
  'AR' => 'Argentina',
  'AS' => 'American Samoa',
  'AT' => 'Austria',
  'AU' => 'Australia',
  'AW' => 'Aruba',
  'AZ' => 'Azerbaijan',
  'BA' => 'Bosnia gi Herzegovina',
  'BB' => 'Barbados',
  'BD' => 'Bangladesh',
  'BE' => 'Belgium',
  'BF' => 'Burkina Faso',
  'BG' => 'Bulgaria',
  'BH' => 'Bahrain',
  'BI' => 'Burundi',
  'BJ' => 'Benin',
  'BM' => 'Bermuda',
  'BN' => 'Brunei',
  'BO' => 'Bolivia',
  'BR' => 'Brazil',
  'BS' => 'Bahamas',
  'BT' => 'Bhutan',
  'BW' => 'Botswana',
  'BY' => 'Belarus',
  'BZ' => 'Belize',
  'CA' => 'Canada',
  'CD' => 'Democratic Republic of the Congo',
  'CF' => 'Central African Republic',
  'CG' => 'Congo',
  'CH' => 'Switzerland',
  'CI' => 'Côte d',
  'CK' => 'Cook Islands',
  'CL' => 'Chile',
  'CM' => 'Cameroon',
  'CN' => 'China',
  'CO' => 'Colombia',
  'CR' => 'Costa Rica',
  'CU' => 'Cuba',
  'CV' => 'Cape Verde Islands',
  'CY' => 'Cyprus',
  'CZ' => 'Czech Republic',
  'DE' => 'Germany',
  'DJ' => 'Djibouti',
  'DK' => 'Denmark',
  'DM' => 'Dominica',
  'DO' => 'Dominican Republic',
  'DZ' => 'Algeria',
  'EC' => 'Ecuador',
  'EE' => 'Estonia',
  'EG' => 'Egypt',
  'ER' => 'Eritrea',
  'ES' => 'Spain',
  'ET' => 'Ethiopia',
  'FI' => 'Finland',
  'FJ' => 'Fiji',
  'FK' => 'Chuia mar Falkland',
  'FM' => 'Micronesia',
  'FR' => 'France',
  'GA' => 'Gabon',
  'GB' => 'United Kingdom',
  'GD' => 'Grenada',
  'GE' => 'Georgia',
  'GF' => 'French Guiana',
  'GH' => 'Ghana',
  'GI' => 'Gibraltar',
  'GL' => 'Greenland',
  'GM' => 'Gambia',
  'GN' => 'Guinea',
  'GP' => 'Guadeloupe',
  'GQ' => 'Equatorial Guinea',
  'GR' => 'Greece',
  'GT' => 'Guatemala',
  'GU' => 'Guam',
  'GW' => 'Guinea-Bissau',
  'GY' => 'Guyana',
  'HN' => 'Honduras',
  'HR' => 'Croatia',
  'HT' => 'Haiti',
  'HU' => 'Hungary',
  'ID' => 'Indonesia',
  'IE' => 'Ireland',
  'IL' => 'Israel',
  'IN' => 'India',
  'IO' => 'British Indian Ocean Territory',
  'IQ' => 'Iraq',
  'IR' => 'Iran',
  'IS' => 'Iceland',
  'IT' => 'Italy',
  'JM' => 'Jamaica',
  'JO' => 'Jordan',
  'JP' => 'Japan',
  'KE' => 'Kenya',
  'KG' => 'Kyrgyzstan',
  'KH' => 'Cambodia',
  'KI' => 'Kiribati',
  'KM' => 'Comoros',
  'KN' => 'Saint Kitts gi Nevis',
  'KP' => 'Korea Masawa',
  'KR' => 'Korea Milambo',
  'KW' => 'Kuwait',
  'KY' => 'Cayman Islands',
  'KZ' => 'Kazakhstan',
  'LA' => 'Laos',
  'LB' => 'Lebanon',
  'LC' => 'Saint Lucia',
  'LI' => 'Liechtenstein',
  'LK' => 'Sri Lanka',
  'LR' => 'Liberia',
  'LS' => 'Lesotho',
  'LT' => 'Lithuania',
  'LU' => 'Luxembourg',
  'LV' => 'Latvia',
  'LY' => 'Libya',
  'MA' => 'Morocco',
  'MC' => 'Monaco',
  'MD' => 'Moldova',
  'MG' => 'Madagascar',
  'MH' => 'Chuia mar Marshall',
  'ML' => 'Mali',
  'MM' => 'Myanmar',
  'MN' => 'Mongolia',
  'MP' => 'Northern Mariana Islands',
  'MQ' => 'Martinique',
  'MR' => 'Mauritania',
  'MS' => 'Montserrat',
  'MT' => 'Malta',
  'MU' => 'Mauritius',
  'MV' => 'Maldives',
  'MW' => 'Malawi',
  'MX' => 'Mexico',
  'MY' => 'Malaysia',
  'MZ' => 'Mozambique',
  'NA' => 'Namibia',
  'NC' => 'New Caledonia',
  'NE' => 'Niger',
  'NF' => 'Chuia mar Norfolk',
  'NG' => 'Nigeria',
  'NI' => 'Nicaragua',
  'NL' => 'Netherlands',
  'NO' => 'Norway',
  'NP' => 'Nepal',
  'NR' => 'Nauru',
  'NU' => 'Niue',
  'NZ' => 'New Zealand',
  'OM' => 'Oman',
  'PA' => 'Panama',
  'PE' => 'Peru',
  'PF' => 'French Polynesia',
  'PG' => 'Papua New Guinea',
  'PH' => 'Philippines',
  'PK' => 'Pakistan',
  'PL' => 'Poland',
  'PM' => 'Saint Pierre gi Miquelon',
  'PN' => 'Pitcairn',
  'PR' => 'Puerto Rico',
  'PS' => 'Palestinian West Bank gi Gaza',
  'PT' => 'Portugal',
  'PW' => 'Palau',
  'PY' => 'Paraguay',
  'QA' => 'Qatar',
  'RE' => 'Réunion',
  'RO' => 'Romania',
  'RU' => 'Russia',
  'RW' => 'Rwanda',
  'SA' => 'Saudi Arabia',
  'SB' => 'Solomon Islands',
  'SC' => 'Seychelles',
  'SD' => 'Sudan',
  'SE' => 'Sweden',
  'SG' => 'Singapore',
  'SH' => 'Saint Helena',
  'SI' => 'Slovenia',
  'SK' => 'Slovakia',
  'SL' => 'Sierra Leone',
  'SM' => 'San Marino',
  'SN' => 'Senegal',
  'SO' => 'Somalia',
  'SR' => 'Suriname',
  'ST' => 'São Tomé gi Príncipe',
  'SV' => 'El Salvador',
  'SY' => 'Syria',
  'SZ' => 'Swaziland',
  'TC' => 'Turks gi Caicos Islands',
  'TD' => 'Chad',
  'TG' => 'Togo',
  'TH' => 'Thailand',
  'TJ' => 'Tajikistan',
  'TK' => 'Tokelau',
  'TL' => 'East Timor',
  'TM' => 'Turkmenistan',
  'TN' => 'Tunisia',
  'TO' => 'Tonga',
  'TR' => 'Turkey',
  'TT' => 'Trinidad gi Tobago',
  'TV' => 'Tuvalu',
  'TW' => 'Taiwan',
  'TZ' => 'Tanzania',
  'UA' => 'Ukraine',
  'UG' => 'Uganda',
  'US' => 'USA',
  'UY' => 'Uruguay',
  'UZ' => 'Uzbekistan',
  'VA' => 'Vatican State',
  'VC' => 'Saint Vincent gi Grenadines',
  'VE' => 'Venezuela',
  'VG' => 'British Virgin Islands',
  'VI' => 'U.S. Virgin Islands',
  'VN' => 'Vietnam',
  'VU' => 'Vanuatu',
  'WF' => 'Wallis gi Futuna',
  'WS' => 'Samoa',
  'YE' => 'Yemen',
  'YT' => 'Mayotte',
  'ZA' => 'South Africa',
  'ZM' => 'Zambia',
  'ZW' => 'Zimbabwe',
);
