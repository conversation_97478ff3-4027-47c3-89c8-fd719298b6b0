.bootstrap.panel {
    clear: both;
}
.bootstrap .form-group {
	margin-bottom: 20px;
}
.bootstrap .form-group .form-group {
	margin-bottom: 0;
}
.bootstrap .form-horizontal .form-wrapper {
  max-width: 100%;
}
.bootstrap .input-group input.color {
  min-width: 100px;
}
@media (min-width: 1200px) {
	.bootstrap .form-group.translatable-field {
		display: flex;
	}
	.bootstrap .form-group.translatable-field .col-lg-9 {
		flex: 1;
	}
	.bootstrap .form-group.translatable-field .col-lg-2 {
		width: auto;
	}
	.bootstrap .form-group.translatable-field .col-lg-2 .dropdown-menu {
		top: 40px;
		right: 0;
		left: auto;
	}
}
:is(body:not(.no-smb-reskin)) .panel-heading .panel-heading-action i {
	display: block;
}