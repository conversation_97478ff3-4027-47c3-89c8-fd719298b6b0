<?php
/**
 * 2007-2020 PrestaShop.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License 3.0 (AFL-3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/AFL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to http://www.prestashop.com for more information.
 *
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2007-2020 PrestaShop SA
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 * International Registered Trademark & Property of PrestaShop SA
 */

/**
 * @since 1.5.0
 *
 * @property Ps_Emailsubscription $module
 */
class Ps_EmailsubscriptionSubscriptionModuleFrontController extends ModuleFrontController
{
    private $variables = [];

    /**
     * @see FrontController::postProcess()
     */
    public function postProcess()
    {
        $this->variables['value'] = Tools::getValue('email', '');
        $this->variables['msg'] = '';
        $this->variables['conditions'] = Configuration::get('NW_CONDITIONS', $this->context->language->id);

        if (Tools::isSubmit('submitNewsletter') || $this->ajax) {
            $this->module->newsletterRegistration();
            if ($this->module->error) {
                $this->variables['msg'] = $this->module->error;
                $this->variables['nw_error'] = true;
            } elseif ($this->module->valid) {
                $this->variables['msg'] = $this->module->valid;
                $this->variables['nw_error'] = false;
            }

            if ($this->ajax) {
                header('Content-Type: application/json');
                if (version_compare(_PS_VERSION_, '1.7.5', '>=')) {
                    $this->ajaxRender(json_encode($this->variables));
                    exit;
                } else {
                    $this->ajaxDie(json_encode($this->variables));
                }
            }
        }
    }

    /**
     * @see FrontController::initContent()
     */
    public function initContent()
    {
        parent::initContent();

        $this->context->smarty->assign('variables', $this->variables);
        $this->setTemplate('module:ps_emailsubscription/views/templates/front/subscription_execution.tpl');
    }
}
