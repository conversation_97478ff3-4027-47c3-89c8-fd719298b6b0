<?php
/**
 * ISC License
 *
 * Copyright (c) 2024 idnovate.com
 * idnovate is a Registered Trademark & Property of idnovate.com, innovación y desarrollo SCP
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
 * OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 *
 * <AUTHOR>
 * @copyright 2024 idnovate
 * @license   https://www.isc.org/licenses/ https://opensource.org/licenses/ISC ISC License
 */
if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminCartsController extends AdminCartsControllerCore
{
    public function ajaxProcessUpdateDeliveryOption()
    {
        if (Module::isEnabled('quantitydiscountpro')) {
            $delivery_option = Tools::getValue('delivery_option');
            if ($delivery_option !== false) {
                $this->context->cart->setDeliveryOption([$this->context->cart->id_address_delivery => $delivery_option]);
            }

            include_once _PS_MODULE_DIR_ . 'quantitydiscountpro/quantitydiscountpro.php';
            $quantityDiscount = new QuantityDiscountRule();
            $quantityDiscount->createAndRemoveRules();
        }

        echo parent::ajaxProcessUpdateDeliveryOption();
    }

    public function ajaxProcessAddVoucher()
    {
        if (!Module::isEnabled('quantitydiscountpro')) {
            return parent::ajaxProcessAddVoucher();
        }

        if ($this->tabAccess['edit'] === '1') {
            include_once _PS_MODULE_DIR_ . 'quantitydiscountpro/quantitydiscountpro.php';
            $errors = [];

            if (($id_cart_rule = Tools::getValue('id_cart_rule')) && substr($id_cart_rule, 0, 9) === '999999999') {
                $quantityDiscount = new quantityDiscountRule(str_replace('999999999', '', $id_cart_rule));
                if (Validate::isLoadedObject($quantityDiscount)) {
                    //TODO : Call isQuantityDiscountRuleValid and compatibleCartRules but check tehre's not context->cart
                    if (!$quantityDiscount->validateQuantityDiscountRuleConditions()) {
                        $errors[] = Tools::displayError('Can\'t add the voucher.');
                    } elseif ($quantityDiscount->calculateCartRule($quantityDiscount) !== true) {
                        $errors[] = Tools::displayError('Can\'t add the voucher.');
                    }
                }
            } elseif (!($id_cart_rule = Tools::getValue('id_cart_rule')) || !$cart_rule = new CartRule((int) $id_cart_rule)) {
                $errors[] = Tools::displayError('Invalid voucher.');
            } elseif ($err = $cart_rule->checkValidity($this->context)) {
                $errors[] = $err;
            }

            if (!count($errors)) {
                if (!$this->context->cart->addCartRule((int) $cart_rule->id)) {
                    $errors[] = Tools::displayError('Can\'t add the voucher.');
                }
            }

            echo json_encode(array_merge($this->ajaxReturnVars(), ['errors' => $errors]));
        }
    }
}
