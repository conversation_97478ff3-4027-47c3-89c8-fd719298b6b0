<?php
/**
 * ISC License
 *
 * Copyright (c) 2024 idnovate.com
 * idnovate is a Registered Trademark & Property of idnovate.com, innovación y desarrollo SCP
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
 * OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 *
 * <AUTHOR>
 * @copyright 2024 idnovate
 * @license   https://www.isc.org/licenses/ https://opensource.org/licenses/ISC ISC License
 */
if (!defined('_PS_VERSION_')) {
    exit;
}

class ChangeCurrencyController extends ChangeCurrencyControllerCore
{
    public function initContent()
    {
        $currentCurrency = $this->context->currency;
        $currency = new Currency((int) Tools::getValue('id_currency'));
        if (Validate::isLoadedObject($currency) && !$currency->deleted) {
            $this->context->cookie->id_currency = (int) $currency->id;

            if (Module::isEnabled('quantitydiscountpro')) {
                include_once _PS_MODULE_DIR_ . 'quantitydiscountpro/quantitydiscountpro.php';

                $this->context->currency = $currency;
                $this->context->cart->id_currency = $currency->id;

                $quantityDiscount = new QuantityDiscountRule();
                $quantityDiscount->createAndRemoveRules();

                $this->context->currency = $currentCurrency;
                $this->context->cart->id_currency = $currentCurrency->id;
            }
            die('1');
        }
        die('0');
    }
}
