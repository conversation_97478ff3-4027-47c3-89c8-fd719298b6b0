<?php
/**
 * ISC License
 *
 * Copyright (c) 2024 idnovate.com
 * idnovate is a Registered Trademark & Property of idnovate.com, innovación y desarrollo SCP
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
 * OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 *
 * <AUTHOR>
 * @copyright 2024 idnovate
 * @license   https://www.isc.org/licenses/ https://opensource.org/licenses/ISC ISC License
 */
if (!defined('_PS_VERSION_')) {
    exit;
}

class CartRule extends CartRuleCore
{
    public static function autoRemoveFromCart($context = null)
    {
        if (Module::isEnabled('quantitydiscountpro')) {
            include_once _PS_MODULE_DIR_ . 'quantitydiscountpro/quantitydiscountpro.php';
            $quantityDiscount = new QuantityDiscountRule();
            $quantityDiscount->createAndRemoveRules(null, $context);
        }

        parent::autoRemoveFromCart($context);
    }

    public static function autoAddToCart(Context $context = null)
    {
        parent::autoAddToCart($context);

        if (Module::isEnabled('quantitydiscountpro')) {
            include_once _PS_MODULE_DIR_ . 'quantitydiscountpro/quantitydiscountpro.php';
            $quantityDiscount = new QuantityDiscountRule();
            $quantityDiscount->createAndRemoveRules(null, $context);
        }
    }

    public function update($null_values = false)
    {
        $r = parent::update($null_values);

        if (Module::isEnabled('quantitydiscountpro')) {
            include_once _PS_MODULE_DIR_ . 'quantitydiscountpro/quantitydiscountpro.php';
            if (CartRule::isCurrentlyUsed('cart_rule', true) || QuantityDiscountRule::isCurrentlyUsed(null, true)) {
                Configuration::updateGlobalValue('PS_CART_RULE_FEATURE_ACTIVE', true);
            } else {
                Configuration::updateGlobalValue('PS_CART_RULE_FEATURE_ACTIVE', false);
            }
        }

        return $r;
    }

    public function delete()
    {
        $r = parent::delete();

        if (Module::isEnabled('quantitydiscountpro')) {
            include_once _PS_MODULE_DIR_ . 'quantitydiscountpro/quantitydiscountpro.php';
            if (CartRule::isCurrentlyUsed('cart_rule', true) || QuantityDiscountRule::isCurrentlyUsed(null, true)) {
                Configuration::updateGlobalValue('PS_CART_RULE_FEATURE_ACTIVE', true);
            } else {
                Configuration::updateGlobalValue('PS_CART_RULE_FEATURE_ACTIVE', false);
            }
        }

        return $r;
    }

    public static function getCustomerCartRules($id_lang, $id_customer, $active = false, $includeGeneric = true, $inStock = false, Cart $cart = null, $free_shipping_only = false)
    {
        $result = parent::getCustomerCartRules($id_lang, $id_customer, $active, $includeGeneric, $inStock, $cart, $free_shipping_only);
        if (!Module::isEnabled('quantitydiscountpro')) {
            return $result;
        }

        include_once _PS_MODULE_DIR_ . 'quantitydiscountpro/quantitydiscountpro.php';
        $quantityDiscount = new QuantityDiscountRule();

        return array_merge($result, $quantityDiscount->getHighlightedQuantityDiscountRules());
    }

    protected function getCartRuleCombinations()
    {
        if (!Module::isEnabled('quantitydiscountpro')) {
            return parent::getCartRuleCombinations();
        }

        $array = [];
        $array['selected'] = Db::getInstance()->executeS('
        SELECT cr.*, crl.*, 1 as selected
        FROM ' . _DB_PREFIX_ . 'cart_rule cr
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule_lang crl ON (cr.id_cart_rule = crl.id_cart_rule AND crl.id_lang = ' . (int) Context::getContext()->language->id . ')
        WHERE cr.id_cart_rule != ' . (int) $this->id . '
        AND (
            cr.cart_rule_restriction = 0
            OR cr.id_cart_rule IN (
                SELECT IF(id_cart_rule_1 = ' . (int) $this->id . ', id_cart_rule_2, id_cart_rule_1)
                FROM ' . _DB_PREFIX_ . 'cart_rule_combination
                WHERE ' . (int) $this->id . ' = id_cart_rule_1
                OR ' . (int) $this->id . ' = id_cart_rule_2
            )
        )
        AND cr.id_cart_rule NOT IN (SELECT id_cart_rule FROM `' . _DB_PREFIX_ . 'quantity_discount_rule_cart`)');

        $array['unselected'] = Db::getInstance()->executeS('
        SELECT cr.*, crl.*, 1 as selected
        FROM ' . _DB_PREFIX_ . 'cart_rule cr
        LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule_lang crl ON (cr.id_cart_rule = crl.id_cart_rule AND crl.id_lang = ' . (int) Context::getContext()->language->id . ')
        WHERE cr.cart_rule_restriction = 1
        AND cr.id_cart_rule != ' . (int) $this->id . '
        AND cr.id_cart_rule NOT IN (
            SELECT IF(id_cart_rule_1 = ' . (int) $this->id . ', id_cart_rule_2, id_cart_rule_1)
            FROM ' . _DB_PREFIX_ . 'cart_rule_combination
            WHERE ' . (int) $this->id . ' = id_cart_rule_1
            OR ' . (int) $this->id . ' = id_cart_rule_2
        )
        AND cr.id_cart_rule NOT IN (SELECT id_cart_rule FROM `' . _DB_PREFIX_ . 'quantity_discount_rule_cart`)');

        return $array;
    }

    public function getAssociatedRestrictions($type, $active_only, $i18n)
    {
        if (!Module::isEnabled('quantitydiscountpro')) {
            return parent::getAssociatedRestrictions($type, $active_only, $i18n);
        }

        $array = ['selected' => [], 'unselected' => []];

        if (!in_array($type, ['country', 'carrier', 'group', 'cart_rule', 'shop'])) {
            return false;
        }

        $shop_list = '';
        if ($type == 'shop') {
            $shops = Context::getContext()->employee->getAssociatedShops();
            if (count($shops)) {
                $shop_list = ' AND t.id_shop IN (' . implode(array_map('intval', $shops), ',') . ') ';
            }
        }

        if (!Validate::isLoadedObject($this) || $this->{$type . '_restriction'} == 0) {
            $array['selected'] = Db::getInstance()->executeS('
            SELECT t.*' . ($i18n ? ', tl.*' : '') . ', 1 as selected
            FROM `' . _DB_PREFIX_ . $type . '` t
            ' . ($i18n ? 'LEFT JOIN `' . _DB_PREFIX_ . $type . '_lang` tl ON (t.id_' . $type . ' = tl.id_' . $type . ' AND tl.id_lang = ' . (int) Context::getContext()->language->id . ')' : '') . '
            WHERE 1
            ' . ($active_only ? 'AND t.active = 1' : '') . '
            ' . (in_array($type, ['carrier', 'shop']) ? ' AND t.deleted = 0' : '') . '
            ' . ($type == 'cart_rule' ? 'AND t.id_cart_rule != ' . (int) $this->id . ' AND t.id_cart_rule NOT IN (SELECT id_cart_rule FROM `' . _DB_PREFIX_ . 'quantity_discount_rule_cart`)' : '') .
            $shop_list .
            ' ORDER BY name ASC');
        } else {
            if ($type == 'cart_rule') {
                $array = $this->getCartRuleCombinations();
            } else {
                $resource = Db::getInstance()->query(
                    'SELECT t.*' . ($i18n ? ', tl.*' : '') . ', IF(crt.id_' . $type . ' IS NULL, 0, 1) as selected
                    FROM `' . _DB_PREFIX_ . $type . '` t
                    ' . ($i18n ? 'LEFT JOIN `' . _DB_PREFIX_ . $type . '_lang` tl ON (t.id_' . $type . ' = tl.id_' . $type . ' AND tl.id_lang = ' . (int) Context::getContext()->language->id . ')' : '') . '
                    LEFT JOIN (SELECT id_' . $type . ' FROM `' . _DB_PREFIX_ . 'cart_rule_' . $type . '` WHERE id_cart_rule = ' . (int) $this->id . ') crt ON t.id_' . ($type == 'carrier' ? 'reference' : $type) . ' = crt.id_' . $type . '
                    WHERE 1 ' . ($active_only ? ' AND t.active = 1' : '') .
                        $shop_list
                        . (in_array($type, ['carrier', 'shop']) ? ' AND t.deleted = 0' : '') .
                        ' ORDER BY name ASC',
                    false
                );

                while ($row = Db::getInstance()->nextRow($resource)) {
                    $array[($row['selected'] || $this->{$type . '_restriction'} == 0) ? 'selected' : 'unselected'][] = $row;
                }
            }
        }

        return $array;
    }

    public static function getCartsRuleByCode($name, $id_lang, $extended = false)
    {
        if (!Module::isEnabled('quantitydiscountpro')) {
            return parent::getCartsRuleByCode($name, $id_lang, $extended);
        }

        $query = '
            SELECT cr.`id_cart_rule`, cr.`code`, crl.`name`
            FROM ' . _DB_PREFIX_ . 'cart_rule cr
            LEFT JOIN ' . _DB_PREFIX_ . 'cart_rule_lang crl ON (cr.id_cart_rule = crl.id_cart_rule AND crl.id_lang = ' . (int) $id_lang . ')
            WHERE cr.`id_cart_rule` NOT IN (SELECT qdrc.`id_cart_rule` FROM ' . _DB_PREFIX_ . 'quantity_discount_rule_cart qdrc) AND (code LIKE \'%' . pSQL($name) . '%\''
            . ($extended ? ' OR name LIKE \'%' . pSQL($name) . '%\'' : '') . ')
            UNION
            SELECT CONCAT("999999999", qdr.`id_quantity_discount_rule`) as `id_cart_rule`, qdr.`code`, qdrl.`name`
            FROM ' . _DB_PREFIX_ . 'quantity_discount_rule qdr
            LEFT JOIN ' . _DB_PREFIX_ . 'quantity_discount_rule_lang qdrl ON (qdr.id_quantity_discount_rule = qdrl.id_quantity_discount_rule AND qdrl.id_lang = ' . (int) $id_lang . ')
            WHERE active = 1
                AND NOW() BETWEEN qdr.date_from AND qdr.date_to
                AND (code LIKE \'%' . pSQL($name) . '%\''
                    . ($extended ? ' OR name LIKE \'%' . pSQL($name) . '%\'' : '') . ')';

        return Db::getInstance()->executeS($query);
    }
}
