<?php
/**
 * ISC License
 *
 * Copyright (c) 2024 idnovate.com
 * idnovate is a Registered Trademark & Property of idnovate.com, innovación y desarrollo SCP
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
 * OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 *
 * <AUTHOR>
 * @copyright 2024 idnovate
 * @license   https://www.isc.org/licenses/ https://opensource.org/licenses/ISC ISC License
 */
if (!defined('_PS_VERSION_')) {
    exit;
}

class AdminQuantityDiscountRulesFamiliesController extends ModuleAdminController
{
    protected $isShopSelected = true;

    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'quantity_discount_rule_family';
        $this->className = 'QuantityDiscountRuleFamily';
        $this->tabClassName = 'AdminQuantityDiscountRulesFamilies';
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->_orderWay = 'DESC';

        parent::__construct();

        $this->bulk_actions = ['delete' => ['text' => $this->l('Delete selected'), 'icon' => 'icon-trash', 'confirm' => $this->l('Delete selected items?')]];

        $this->fields_list = [
            'id_quantity_discount_rule_family' => ['title' => $this->l('ID'), 'align' => 'center', 'class' => 'fixed-width-xs'],
            'name' => ['title' => $this->l('Name')],
            'description' => ['title' => $this->l('Description'), 'align' => 'center'],
            'priority' => ['title' => $this->l('Priority'), 'class' => 'fixed-width-sm'],
            'execute_other_families' => ['title' => $this->l('Execute other families'), 'active' => 'execute_other_families', 'type' => 'bool', 'orderby' => false, 'align' => 'center'],
            'active' => ['title' => $this->l('Active'), 'active' => 'status', 'type' => 'bool', 'orderby' => false, 'align' => 'center'],
        ];

        if (Shop::isFeatureActive() && (Shop::getContext() == Shop::CONTEXT_ALL || Shop::getContext() == Shop::CONTEXT_GROUP)) {
            $this->isShopSelected = false;
        }

        if (!Shop::isFeatureActive()) {
            $this->shopLinkType = '';
        } else {
            $this->shopLinkType = 'shop';
        }
    }

    public function setMedia($isNewTheme = false)
    {
        parent::setMedia($isNewTheme);

        if (version_compare(_PS_VERSION_, '1.6', '>=')) {
            $this->addCSS(_MODULE_DIR_ . 'quantitydiscountpro/views/css/admin.css');
        } else {
            $this->addCSS(_MODULE_DIR_ . 'quantitydiscountpro/views/css/admin-15.css');
        }
    }

    public function initPageHeaderToolbar()
    {
        if (empty($this->display)) {
            $this->page_header_toolbar_btn['new_quantity_discount_rule_family'] = [
                'href' => self::$currentIndex . '&addquantity_discount_rule_family&token=' . $this->token,
                'desc' => $this->l('Add new family', null, null, false),
                'icon' => 'process-icon-new',
            ];

            $this->page_header_toolbar_btn['edit_quantity_discount_rule_family'] = [
                'href' => $this->context->link->getAdminLink('AdminQuantityDiscountRules'),
                'desc' => $this->l('Back to rules', null, null, false),
                'icon' => 'process-icon-back',
            ];
        }

        parent::initPageHeaderToolbar();

        $this->context->smarty->clearAssign('help_link', '');
    }

    public function initToolbar()
    {
        parent::initToolbar();

        if (empty($this->display)) {
            $this->toolbar_btn['new'] = [
                'href' => self::$currentIndex . '&addquantity_discount_rule_family&token=' . $this->token,
                'desc' => $this->l('Add new family', null, null, false),
            ];

            $this->toolbar_btn['back'] = [
                'href' => $this->context->link->getAdminLink('AdminQuantityDiscountRules'),
                'desc' => $this->l('Back to rules', null, null, false),
            ];
        }
    }

    public function initProcess()
    {
        parent::initProcess();

        if (Tools::isSubmit('execute_other_families' . $this->table)) {
            $object = $this->loadObject();

            if (!Validate::isLoadedObject($object)) {
                $this->errors[] = Tools::displayError('An error occurred while updating information.');
            }

            $object->execute_other_families = !$object->execute_other_families;
            if (!$object->update()) {
                $this->errors[] = Tools::displayError('An error occurred while updating information.');
            }

            Tools::redirectAdmin(self::$currentIndex . '&token=' . $this->token);
        }
    }

    public function renderList()
    {
        if (Tools::getValue('magic')) {
            return $this->module->getContent();
        }

        if ($this->isShopSelected &&
            ((version_compare(_PS_VERSION_, '1.5.0.13', '<') && !Module::isInstalled($this->module->name))
             || (version_compare(_PS_VERSION_, '1.5.0.13', '>=') && !Module::isEnabled($this->module->name)))) {
            $this->warnings[] = $this->l('Module is not enabled in this shop.');
        }

        //Redirect if no family created
        if ($this->isShopSelected && !QuantityDiscountRuleFamily::getNbObjects()) {
            $this->redirect_after = 'index.php?controller=' . $this->tabClassName . '&add' . $this->table . '&token=' . Tools::getAdminTokenLite($this->tabClassName);
            $this->redirect();
        }

        if (!$this->isShopSelected && !QuantityDiscountRuleFamily::getNbObjects()) {
            $this->errors[] = $this->l('Please select a shop.');

            return;
        }

        return parent::renderList();
    }

    public function renderForm()
    {
        if (Tools::getValue('magic')) {
            return $this->module->getContent();
        }

        if (!$this->isShopSelected && $this->display == 'add') {
            $this->errors[] = $this->l('Please select a shop.');

            return;
        }

        if ($this->isShopSelected &&
            ((version_compare(_PS_VERSION_, '1.5.0.13', '<') && !Module::isInstalled($this->module->name))
             || (version_compare(_PS_VERSION_, '1.5.0.13', '>=') && !Module::isEnabled($this->module->name)))) {
            $this->warnings[] = $this->l('Module is not enabled in this shop.');
        }

        $this->toolbar_btn['save-and-stay'] = [
            'href' => '#',
            'desc' => $this->l('Save and Stay'),
        ];

        if (!$this->loadObject(true)) {
            return;
        }

        $this->fields_form = [
            'legend' => [
                'title' => $this->l('Family'),
                'icon' => 'icon-edit',
            ],
            'input' => [
                [
                    'type' => version_compare(_PS_VERSION_, '1.6', '>=') ? 'switch' : 'radio',
                    'label' => $this->l('Enabled?'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'class' => 't',
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Yes'),
                        ],
                        [
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('No'),
                        ],
                    ],
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Name'),
                    'name' => 'name',
                    'required' => true,
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'description',
                ],
                [
                    'col' => 1,
                    'type' => 'text',
                    'label' => $this->l('Priority'),
                    'name' => 'priority',
                ],
                [
                    'type' => version_compare(_PS_VERSION_, '1.6', '>=') ? 'switch' : 'radio',
                    'label' => $this->l('Execute rules from other families?'),
                    'name' => 'execute_other_families',
                    'required' => false,
                    'is_bool' => true,
                    'class' => 't',
                    'values' => [
                        [
                            'id' => 'execute_other_families_on',
                            'value' => 1,
                            'label' => $this->l('Yes'),
                        ],
                        [
                            'id' => 'execute_other_families_off',
                            'value' => 0,
                            'label' => $this->l('No'),
                        ],
                    ],
                ],
            ],
        ];

        $this->fields_form['submit'] = [
            'title' => $this->l('Save'),
        ];

        return parent::renderForm();
    }

    public function initContent()
    {
        if (version_compare($this->module->version, $this->module->getDatabaseVersion(), '>')) {
            return $this->errors[] = $this->l('Upgrade available');
        }

        if ($warnings = $this->module->getWarnings(false)) {
            $this->errors[] = Tools::displayError($warnings);

            return;
        }

        parent::initContent();

        if (version_compare(_PS_VERSION_, '1.6', '>=')) {
            $module = $this->module;

            $default_iso_code = 'en';
            $local_path = $module->getLocalPath();

            $readme = null;
            if (file_exists($local_path . '/readme_' . $this->context->language->iso_code . '.pdf')) {
                $readme = 'readme_' . $this->context->language->iso_code . '.pdf';
            } elseif (file_exists($local_path . '/readme_' . $default_iso_code . '.pdf')) {
                $readme = 'readme_' . $default_iso_code . '.pdf';
            }

            $this->context->smarty->assign([
                'support_id' => $module->addons_id_product,
                'readme' => $readme,
                'this_path' => $module->getPathUri(),
            ]);

            if (file_exists($local_path . '/views/templates/admin/company/information_' . $this->context->language->iso_code . '.tpl')) {
                $this->content .= $this->context->smarty->fetch($local_path . '/views/templates/admin/company/information_' . $this->context->language->iso_code . '.tpl');
            } elseif (file_exists($local_path . '/views/templates/admin/company/information_' . $default_iso_code . '.tpl')) {
                $this->content .= $this->context->smarty->fetch($local_path . '/views/templates/admin/company/information_' . $default_iso_code . '.tpl');
            }
        }

        $this->context->smarty->assign([
            'content' => $this->content,
        ]);
    }

    public function processDelete()
    {
        $object = $this->loadObject();

        if (count(QuantityDiscountRule::getQuantityDiscountRulesByFamily($object->id_quantity_discount_rule_family)) > 0) {
            $this->errors[] = Tools::displayError('You cannot remove this family because there are rules associated to it.');
        } else {
            return parent::processDelete();
        }
    }
}
