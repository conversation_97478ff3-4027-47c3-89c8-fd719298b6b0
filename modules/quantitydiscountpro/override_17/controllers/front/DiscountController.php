<?php
/**
 * ISC License
 *
 * Copyright (c) 2024 idnovate.com
 * idnovate is a Registered Trademark & Property of idnovate.com, innovación y desarrollo SCP
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
 * OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 *
 * <AUTHOR>
 * @copyright 2024 idnovate
 * @license   https://www.isc.org/licenses/ https://opensource.org/licenses/ISC ISC License
 */
if (!defined('_PS_VERSION_')) {
    exit;
}

class DiscountController extends DiscountControllerCore
{
    /**
     * Assign template vars related to page content
     *
     * @see FrontController::initContent()
     */
    public function initContent()
    {
        if (Module::isEnabled('quantitydiscountpro')) {
            include_once _PS_MODULE_DIR_ . 'quantitydiscountpro/quantitydiscountpro.php';

            if (Configuration::isCatalogMode()) {
                Tools::redirect('index.php');
            }

            $cart_rules = $this->getTemplateVarCartRules();

            if (is_array(array_filter($cart_rules))) {
                foreach ($cart_rules as $key => &$discount) {
                    if (QuantityDiscountRule::cartRuleGeneratedByAQuantityDiscountRuleCodeWithoutCode($discount['code'])) {
                        unset($cart_rules[$key]);
                    }
                }
            }

            if (count($cart_rules) <= 0) {
                $this->warning[] = $this->trans('You do not have any vouchers.', [], 'Shop.Notifications.Warning');
            }

            $this->context->smarty->assign([
                'cart_rules' => $cart_rules,
            ]);

            FrontController::initContent();
            $this->setTemplate('customer/discount');
        } else {
            parent::initContent();
        }
    }
}
