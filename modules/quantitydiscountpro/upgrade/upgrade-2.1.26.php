<?php
/**
 * ISC License
 *
 * Copyright (c) 2024 idnovate.com
 * idnovate is a Registered Trademark & Property of idnovate.com, innovación y desarrollo SCP
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
 * REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
 * AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
 * INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
 * LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
 * OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
 * PERFORMANCE OF THIS SOFTWARE.
 *
 * <AUTHOR>
 * @copyright 2024 idnovate
 * @license   https://www.isc.org/licenses/ https://opensource.org/licenses/ISC ISC License
 */
if (!defined('_PS_VERSION_')) {
    exit;
}

function upgrade_module_2_1_26($module)
{
    Db::getInstance()->execute(
        'DELETE
        FROM `' . _DB_PREFIX_ . 'cart_rule_combination`
        WHERE `id_cart_rule_1` IN (
            SELECT `id_cart_rule`
            FROM `' . _DB_PREFIX_ . "cart_rule`
            WHERE `cart_rule_restriction` = '1' AND `id_cart_rule_1` IN (SELECT `id_cart_rule` FROM `" . _DB_PREFIX_ . 'quantity_discount_rule_cart`)
        );'
    );

    Db::getInstance()->execute(
        'DELETE
        FROM `' . _DB_PREFIX_ . 'cart_rule_combination`
        WHERE `id_cart_rule_2` IN (
            SELECT `id_cart_rule`
            FROM `' . _DB_PREFIX_ . "cart_rule`
            WHERE `cart_rule_restriction` = '1' AND `id_cart_rule_2` IN (SELECT `id_cart_rule` FROM `" . _DB_PREFIX_ . 'quantity_discount_rule_cart`)
        );'
    );

    Db::getInstance()->execute(
        'UPDATE `' . _DB_PREFIX_ . 'cart_rule`
        SET `cart_rule_restriction` = 0
        WHERE `id_cart_rule` IN (SELECT `id_cart_rule` FROM `' . _DB_PREFIX_ . 'quantity_discount_rule_cart`);'
    );

    if (version_compare(_PS_VERSION_, '1.7', '>=') && !$module->active) {
        return true;
    }

    $module->copyOverrideFolder();

    $module->removeOverride('CartRule');
    $module->addOverride('CartRule');

    return true;
}
