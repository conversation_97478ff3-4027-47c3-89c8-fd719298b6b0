<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to https://devdocs.prestashop.com/ for more information.
 *
 * <AUTHOR> SA and Contributors <<EMAIL>>
 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

/**
 * Class SupplierCore.
 */
class EosSupplier extends ObjectModel
{
    public $id;

    public $id_supplier;

    public $name;

    public $active;

    public $type;

    public $email;

    public $class_name;

    public $email_title;

    public $email_content;

    public $other_settings;

    public $build_vars;

    public $extrafields;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'eos_supplier',
        'primary' => 'id_supplier',
        'fields' => [
            'id_supplier' => ['type' => self::TYPE_INT],
            'active' => ['type' => self::TYPE_BOOL],
            'type' => ['type' => self::TYPE_STRING],
            'email' => ['type' => self::TYPE_STRING],
            'class_name' => ['type' => self::TYPE_STRING],
            'email_title' => ['type' => self::TYPE_STRING],
            'email_content' => ['type' => self::TYPE_HTML],
            'other_settings' => ['type' => self::TYPE_STRING],
        ],
    ];

    /**
     * SupplierCore constructor.
     *
     * @param null $id
     * @param null $idLang
     */
    public function __construct($id = null)
    {
        parent::__construct($id);

        if (!$this->id && $id) {
            self::addNewEntryToEosSupplier($id);
            parent::__construct($id);
        }

        $this->name = Supplier::getNameById($this->id);
        $other_settings = json_decode($this->other_settings, true);
        $this->other_settings = array();
        if ($other_settings) {
            foreach ($other_settings as $key => $value) {
                $key_alt = 'other_settings['.$key.']';
                $this->$key = $value;
                $this->$key_alt = $value;
            }
        }
        
        if ($this->extrafields) {
            $this->extrafields .= ',packages';
        } else {
            $this->extrafields = 'packages';
        }
        
        if ($this->extrafields) {
            $this->extrafields_array = explode(',', $this->extrafields);
            foreach($this->extrafields_array as &$k) {
                $k = trim($k);
            }
        } else {
            $this->extrafields_array = array();
        }
    }

    public function getEmails()
    {
        $emails = explode(',', $this->email);
        foreach ($emails as $email) {
            return trim($email);
        }
    }

    public function getBcc()
    {
        $emails = explode(',', $this->email);
        $return = array();
        foreach ($emails as $key => $email) {
            if ($key == 0) {
                continue;
            }
            $return[] = trim($email);
        }
        if (!$return) {
            $return = null;
        }

        return $return;
    }

    public function generateEmailTitle($order, $data)
    {
        return $this->replaceVars($this->email_title, $order, $data);
    }

    public function generateEmailContent($order, $data)
    {
        return $this->replaceVars($this->email_content, $order, $data);
    }

    public function replaceVars($content, $order, $data)
    {
        $vars = $this->buildVars($order, $data);

        return str_replace(array_keys($vars), array_values($vars), $content);
    }

    public function buildVars($order, $data)
    {
        if ($this->build_vars) {
            return $this->build_vars;
        }

        $customer_name = '';
        $customer = new Customer($order->id_customer);
        $address_invoice = new Address($order->id_address_invoice);
        $address_delivery = new Address($order->id_address_delivery);

        if (trim($address_invoice->company, ' .')) {
            $customer_name = $address_invoice->company;
        } elseif (trim($address_delivery->company, ' .')) {
            $customer_name = $address_delivery->company;
        } elseif (trim($customer->company, ' .')) {
            $customer_name = $customer->company;
        } elseif (trim($address_delivery->firstname, ' .')) {
            $customer_name = $address_delivery->firstname . ' ' . $address_delivery->lastname;
        } elseif (trim($address_invoice->firstname, ' .')) {
            $customer_name = $address_invoice->firstname . ' ' . $address_invoice->lastname;
        } elseif (trim($customer->firstname, ' .')) {
            $customer_name = $customer->firstname . ' ' . $customer->lastname;
        }

        $return = array(
            '{id_order}' => $order->id,
            '{customer_name}' => $customer_name,
            '{reference}' => $order->reference,
            '{products_html}' => $this->buildProductsHTML($order, $data),
            '{products_txt}' => $this->buildProductsTXT($order, $data),
            '{awb}' => $data['awb'],
        );

        $this->build_vars = $return;

        return $this->build_vars;
    }

    public function buildProductsHTML($order, $data)
    {
        Context::getContext()->smarty->assign(array(
            'order' => $order,
            'data' => $data,
            'EosSupplier' => $this,
        ));
        return Context::getContext()->smarty->fetch(Exportordertosupplier::getPath() . '/views/templates/hook/products_html.tpl');
    }

    public function buildProductsTXT($order, $data)
    {
        Context::getContext()->smarty->assign(array(
            'order' => $order,
            'data' => $data,
            'EosSupplier' => $this,
        ));
        return Context::getContext()->smarty->fetch(Exportordertosupplier::getPath() . '/views/templates/hook/products_txt.tpl');
    }

    public static function addNewEntryToEosSupplier($id)
    {
        Db::getInstance()->insert('eos_supplier', array(
            'id_supplier' => $id,
            'email_title' => 'New order from {shop_name} - #{id_order} for {customer_name}',
            'email_content' => Tools::file_get_contents(Exportordertosupplier::getPath() . '/default_email_content.html'),
        ));
    }

    public function update($null_values = false)
    {
        $this->other_settings = json_encode($this->other_settings);
        return parent::update();
    }
}
